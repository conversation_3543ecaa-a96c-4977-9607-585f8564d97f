using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Services;

namespace OracleMS.ViewModels;

public class ConnectionManagerViewModel : ViewModelBase, ISaveable
{
    private readonly IConnectionService _connectionService;

    public ObservableCollection<ConnectionInfo> Connections { get; } = new();
    
    private ConnectionInfo? _selectedConnection;
    public ConnectionInfo? SelectedConnection
    {
        get => _selectedConnection;
        set => SetProperty(ref _selectedConnection, value);
    }

    private ConnectionInfo _editingConnection = new();
    public ConnectionInfo EditingConnection
    {
        get => _editingConnection;
        set => SetProperty(ref _editingConnection, value);
    }

    private bool _isEditing;
    public bool IsEditing
    {
        get => _isEditing;
        set => SetProperty(ref _isEditing, value);
    }

    private bool _isTesting;
    public bool IsTesting
    {
        get => _isTesting;
        set => SetProperty(ref _isTesting, value);
    }

    private string _testResult = string.Empty;
    public string TestResult
    {
        get => _testResult;
        set => SetProperty(ref _testResult, value);
    }

    private bool _hasUnsavedChanges;
    public bool HasUnsavedChanges
    {
        get => _hasUnsavedChanges;
        set => SetProperty(ref _hasUnsavedChanges, value);
    }

    public ICommand AddConnectionCommand { get; }
    public ICommand EditConnectionCommand { get; }
    public ICommand DeleteConnectionCommand { get; }
    public ICommand TestConnectionCommand { get; }
    public ICommand ConnectCommand { get; }
    public ICommand DisconnectCommand { get; }
    public ICommand SaveConnectionCommand { get; }
    public ICommand CancelEditCommand { get; }
    public ICommand RefreshCommand { get; }

    public event EventHandler<ConnectionInfo>? ConnectionEstablished;

    public ConnectionManagerViewModel(IConnectionService connectionService)
    {
        _connectionService = connectionService;

        AddConnectionCommand = new RelayCommand(OnAddConnection);
        EditConnectionCommand = new RelayCommand(OnEditConnection, () => SelectedConnection != null);
        DeleteConnectionCommand = new RelayCommand(OnDeleteConnection, () => SelectedConnection != null);
        TestConnectionCommand = new AsyncRelayCommand(OnTestConnectionAsync);
        ConnectCommand = new AsyncRelayCommand(OnConnectAsync, () => SelectedConnection != null);
        DisconnectCommand = new RelayCommand(OnDisconnect, () => SelectedConnection != null && IsConnectionActive(SelectedConnection.Id));
        SaveConnectionCommand = new AsyncRelayCommand(OnSaveConnectionAsync, () => IsEditing);
        CancelEditCommand = new RelayCommand(OnCancelEdit, () => IsEditing);
        RefreshCommand = new AsyncRelayCommand(OnRefreshAsync);

        // Subscribe to connection state changes
        _connectionService.ConnectionStateChanged += OnConnectionStateChanged;

        // Load saved connections on initialization
        _ = LoadConnectionsAsync();
    }

    private async Task LoadConnectionsAsync()
    {
        try
        {
            var connections = await _connectionService.GetSavedConnectionsAsync();
            Connections.Clear();
            foreach (var connection in connections)
            {
                Connections.Add(connection);
            }
        }
        catch (Exception ex)
        {
            TestResult = $"載入連線失敗: {ex.Message}";
        }
    }

    private void OnAddConnection()
    {
        EditingConnection = new ConnectionInfo
        {
            Id = Guid.NewGuid().ToString(),
            Name = "新連線",
            Server = "localhost",
            Port = 1521,
            Type = ConnectionType.Basic
        };
        IsEditing = true;
        TestResult = string.Empty;
    }

    private void OnEditConnection()
    {
        if (SelectedConnection == null) return;

        EditingConnection = new ConnectionInfo
        {
            Id = SelectedConnection.Id,
            Name = SelectedConnection.Name,
            Server = SelectedConnection.Server,
            Port = SelectedConnection.Port,
            ServiceName = SelectedConnection.ServiceName,
            Username = SelectedConnection.Username,
            Password = SelectedConnection.Password,
            Type = SelectedConnection.Type,
            SavePassword = SelectedConnection.SavePassword
        };
        IsEditing = true;
        TestResult = string.Empty;
    }

    private async void OnDeleteConnection()
    {
        if (SelectedConnection == null) return;

        try
        {
            await _connectionService.DeleteConnectionAsync(SelectedConnection.Id);
            Connections.Remove(SelectedConnection);
            SelectedConnection = null;
            HasUnsavedChanges = true;
            TestResult = "連線已刪除";
        }
        catch (Exception ex)
        {
            TestResult = $"刪除連線失敗: {ex.Message}";
        }
    }

    private async Task OnTestConnectionAsync()
    {
        if (EditingConnection == null) return;

        IsTesting = true;
        TestResult = "測試連線中...";

        try
        {
            var success = await _connectionService.TestConnectionAsync(EditingConnection);
            TestResult = success ? "連線測試成功!" : "連線測試失敗";
        }
        catch (Exception ex)
        {
            TestResult = $"連線測試失敗: {ex.Message}";
        }
        finally
        {
            IsTesting = false;
        }
    }

    private async Task OnConnectAsync()
    {
        if (SelectedConnection == null) return;

        try
        {
            // Use managed connection for state management and auto-reconnection
            var connection = await _connectionService.CreateManagedConnectionAsync(SelectedConnection);
            SelectedConnection.LastConnected = DateTime.Now;
            
            // Update the connection in the list
            var existingConnection = Connections.FirstOrDefault(c => c.Id == SelectedConnection.Id);
            if (existingConnection != null)
            {
                existingConnection.LastConnected = SelectedConnection.LastConnected;
            }

            ConnectionEstablished?.Invoke(this, SelectedConnection);
            TestResult = $"已連線到: {SelectedConnection.Name}";
        }
        catch (Exception ex)
        {
            TestResult = $"連線失敗: {ex.Message}";
        }
    }

    private void OnDisconnect()
    {
        if (SelectedConnection == null) return;

        try
        {
            _connectionService.CloseConnection(SelectedConnection.Id);
            TestResult = $"已中斷連線: {SelectedConnection.Name}";
        }
        catch (Exception ex)
        {
            TestResult = $"中斷連線失敗: {ex.Message}";
        }
    }

    private async Task OnSaveConnectionAsync()
    {
        if (EditingConnection == null) return;

        try
        {
            await _connectionService.SaveConnectionAsync(EditingConnection);

            // Update or add to the connections list
            var existingConnection = Connections.FirstOrDefault(c => c.Id == EditingConnection.Id);
            if (existingConnection != null)
            {
                // Update existing connection
                existingConnection.Name = EditingConnection.Name;
                existingConnection.Server = EditingConnection.Server;
                existingConnection.Port = EditingConnection.Port;
                existingConnection.ServiceName = EditingConnection.ServiceName;
                existingConnection.Username = EditingConnection.Username;
                existingConnection.Password = EditingConnection.Password;
                existingConnection.Type = EditingConnection.Type;
                existingConnection.SavePassword = EditingConnection.SavePassword;
            }
            else
            {
                // Add new connection
                Connections.Add(new ConnectionInfo
                {
                    Id = EditingConnection.Id,
                    Name = EditingConnection.Name,
                    Server = EditingConnection.Server,
                    Port = EditingConnection.Port,
                    ServiceName = EditingConnection.ServiceName,
                    Username = EditingConnection.Username,
                    Password = EditingConnection.Password,
                    Type = EditingConnection.Type,
                    SavePassword = EditingConnection.SavePassword
                });
            }

            IsEditing = false;
            HasUnsavedChanges = false;
            TestResult = "連線已儲存";
        }
        catch (Exception ex)
        {
            TestResult = $"儲存連線失敗: {ex.Message}";
        }
    }

    private void OnCancelEdit()
    {
        IsEditing = false;
        EditingConnection = new ConnectionInfo();
        TestResult = string.Empty;
    }

    private async Task OnRefreshAsync()
    {
        await LoadConnectionsAsync();
        TestResult = "連線清單已重新整理";
    }

    /// <summary>
    /// 處理連線狀態變更事件
    /// </summary>
    private void OnConnectionStateChanged(object? sender, Services.ConnectionStateChangedEventArgs e)
    {
        // Update UI based on connection state
        switch (e.State)
        {
            case Services.ConnectionStatus.Connected:
                TestResult = $"已連線到: {e.ConnectionInfo.Name}";
                break;
            case Services.ConnectionStatus.Disconnected:
                TestResult = string.IsNullOrEmpty(e.ErrorMessage) 
                    ? $"已中斷連線: {e.ConnectionInfo.Name}"
                    : $"連線中斷: {e.ConnectionInfo.Name} - {e.ErrorMessage}";
                break;
            case Services.ConnectionStatus.Reconnected:
                TestResult = $"重新連線成功: {e.ConnectionInfo.Name}";
                break;
            case Services.ConnectionStatus.Failed:
                TestResult = $"連線失敗: {e.ConnectionInfo.Name} - {e.ErrorMessage}";
                break;
        }

        // Update connection status in the list
        var connection = Connections.FirstOrDefault(c => c.Id == e.ConnectionInfo.Id);
        if (connection != null)
        {
            connection.LastConnected = e.Timestamp;
        }

        // Update command states
        ((AsyncRelayCommand)ConnectCommand).NotifyCanExecuteChanged();
        ((RelayCommand)DisconnectCommand).NotifyCanExecuteChanged();
    }

    /// <summary>
    /// 中斷指定連線
    /// </summary>
    public void DisconnectConnection(string connectionId)
    {
        _connectionService.CloseConnection(connectionId);
        TestResult = "連線已中斷";
    }

    /// <summary>
    /// 取得活動連線清單
    /// </summary>
    public IEnumerable<ConnectionInfo> GetActiveConnections()
    {
        return _connectionService.GetActiveConnections();
    }

    /// <summary>
    /// 檢查連線是否為活動狀態
    /// </summary>
    public bool IsConnectionActive(string connectionId)
    {
        return _connectionService.IsConnectionActive(connectionId);
    }

    public void Save()
    {
        if (IsEditing)
        {
            _ = OnSaveConnectionAsync();
        }
    }
}