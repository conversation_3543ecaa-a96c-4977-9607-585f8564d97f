using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;

namespace TestIndexDefinitionRestore
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("測試 IndexDefinition 欄位恢復功能");
            
            // 設定 Mock 物件
            var mockDatabaseService = new Mock<IDatabaseService>();
            var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            var mockObjectEditorService = new Mock<IObjectEditorService>();
            var mockConnection = new Mock<IDbConnection>();
            var mockLogger = new Mock<ILogger>();

            // 創建 IndexEditorInfo（編輯模式）
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsUnique = false,
                IsEditMode = true, // 編輯模式
                Columns = new List<string>()
            };

            // 創建 ViewModel
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                mockDatabaseService.Object,
                mockScriptGeneratorService.Object,
                mockObjectEditorService.Object,
                () => mockConnection.Object,
                mockLogger.Object
            );

            Console.WriteLine("初始狀態:");
            Console.WriteLine($"可用欄位數量: {viewModel.AvailableColumns.Count}");
            Console.WriteLine($"已選欄位數量: {viewModel.SelectedColumns.Count}");

            // 先添加一些可用欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("NAME");
            viewModel.AvailableColumns.Add("EMAIL");
            viewModel.AvailableColumns.Add("CREATED_DATE");

            Console.WriteLine("\n添加可用欄位後:");
            Console.WriteLine($"可用欄位數量: {viewModel.AvailableColumns.Count}");
            Console.WriteLine($"可用欄位: {string.Join(", ", viewModel.AvailableColumns)}");
            Console.WriteLine($"已選欄位數量: {viewModel.SelectedColumns.Count}");

            // 創建有有效 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "ID", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "NAME", Position = 2 }
                }
            };

            Console.WriteLine("\n設定 IndexDefinition:");
            Console.WriteLine($"IndexDefinition 欄位數量: {indexDefinition.Columns.Count}");
            Console.WriteLine($"IndexDefinition 欄位: {string.Join(", ", indexDefinition.Columns.Select(c => c.ColumnName))}");

            // 使用反射來設定 IndexDefinition 屬性（模擬實際載入過程）
            var property = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            if (property != null)
            {
                property.SetValue(viewModel, indexDefinition);
                Console.WriteLine("IndexDefinition 已設定");
            }
            else
            {
                Console.WriteLine("無法找到 IndexDefinition 屬性");
            }

            Console.WriteLine("\n設定 IndexDefinition 後:");
            Console.WriteLine($"可用欄位數量: {viewModel.AvailableColumns.Count}");
            Console.WriteLine($"可用欄位: {string.Join(", ", viewModel.AvailableColumns)}");
            Console.WriteLine($"已選欄位數量: {viewModel.SelectedColumns.Count}");
            Console.WriteLine($"已選欄位: {string.Join(", ", viewModel.SelectedColumns)}");

            // 驗證結果
            bool testPassed = true;
            
            if (viewModel.SelectedColumns.Count != 2)
            {
                Console.WriteLine($"\n❌ 測試失敗: 預期已選欄位數量為 2，實際為 {viewModel.SelectedColumns.Count}");
                testPassed = false;
            }
            
            if (!viewModel.SelectedColumns.Contains("ID"))
            {
                Console.WriteLine("\n❌ 測試失敗: 已選欄位中應包含 'ID'");
                testPassed = false;
            }
            
            if (!viewModel.SelectedColumns.Contains("NAME"))
            {
                Console.WriteLine("\n❌ 測試失敗: 已選欄位中應包含 'NAME'");
                testPassed = false;
            }
            
            if (viewModel.AvailableColumns.Contains("ID"))
            {
                Console.WriteLine("\n❌ 測試失敗: 可用欄位中不應包含 'ID'");
                testPassed = false;
            }
            
            if (viewModel.AvailableColumns.Contains("NAME"))
            {
                Console.WriteLine("\n❌ 測試失敗: 可用欄位中不應包含 'NAME'");
                testPassed = false;
            }

            if (testPassed)
            {
                Console.WriteLine("\n✅ 測試通過: IndexDefinition 欄位恢復功能正常工作");
            }
            else
            {
                Console.WriteLine("\n❌ 測試失敗: IndexDefinition 欄位恢復功能有問題");
            }

            Console.WriteLine("\n按任意鍵結束...");
            Console.ReadKey();
        }
    }
}
