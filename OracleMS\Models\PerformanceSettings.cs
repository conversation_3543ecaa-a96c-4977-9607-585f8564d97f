using System;

namespace OracleMS.Models
{
    /// <summary>
    /// 效能設定類別
    /// </summary>
    public class PerformanceSettings
    {
        /// <summary>
        /// 預設設定實例
        /// </summary>
        public static PerformanceSettings Default { get; } = new PerformanceSettings();

        /// <summary>
        /// 快取過期時間（分鐘）
        /// </summary>
        public int CacheExpirationMinutes { get; set; } = 5;

        /// <summary>
        /// 最大顯示Table數量
        /// </summary>
        public int MaxDisplayTables { get; set; } = 1000;

        /// <summary>
        /// 最大顯示Column數量
        /// </summary>
        public int MaxDisplayColumns { get; set; } = 500;

        /// <summary>
        /// UI更新批次大小
        /// </summary>
        public int UIUpdateBatchSize { get; set; } = 50;

        /// <summary>
        /// 驗證延遲時間（毫秒）
        /// </summary>
        public int ValidationDelayMs { get; set; } = 500;

        /// <summary>
        /// 效能監控更新間隔（毫秒）
        /// </summary>
        public int PerformanceMonitorIntervalMs { get; set; } = 5000;

        /// <summary>
        /// 是否啟用背景預載入
        /// </summary>
        public bool EnableBackgroundPreloading { get; set; } = true;

        /// <summary>
        /// 是否啟用記憶體優化
        /// </summary>
        public bool EnableMemoryOptimization { get; set; } = true;

        /// <summary>
        /// 記憶體使用閾值（MB），超過此值會觸發垃圾回收
        /// </summary>
        public long MemoryThresholdMB { get; set; } = 100;

        /// <summary>
        /// 是否啟用UI虛擬化
        /// </summary>
        public bool EnableUIVirtualization { get; set; } = true;

        /// <summary>
        /// 是否啟用快取
        /// </summary>
        public bool EnableCaching { get; set; } = true;

        /// <summary>
        /// 最大快取項目數量
        /// </summary>
        public int MaxCacheItems { get; set; } = 100;

        /// <summary>
        /// 是否啟用效能監控
        /// </summary>
        public bool EnablePerformanceMonitoring { get; set; } = true;

        /// <summary>
        /// 是否啟用詳細日誌記錄
        /// </summary>
        public bool EnableVerboseLogging { get; set; } = false;

        /// <summary>
        /// 載入超時時間（秒）
        /// </summary>
        public int LoadingTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// 驗證設定的有效性
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (CacheExpirationMinutes <= 0)
                result.AddError("快取過期時間必須大於0分鐘");

            if (MaxDisplayTables <= 0)
                result.AddError("最大顯示Table數量必須大於0");

            if (MaxDisplayColumns <= 0)
                result.AddError("最大顯示Column數量必須大於0");

            if (UIUpdateBatchSize <= 0)
                result.AddError("UI更新批次大小必須大於0");

            if (ValidationDelayMs < 0)
                result.AddError("驗證延遲時間不能為負數");

            if (PerformanceMonitorIntervalMs <= 0)
                result.AddError("效能監控更新間隔必須大於0毫秒");

            if (MemoryThresholdMB <= 0)
                result.AddError("記憶體使用閾值必須大於0MB");

            if (MaxCacheItems <= 0)
                result.AddError("最大快取項目數量必須大於0");

            if (LoadingTimeoutSeconds <= 0)
                result.AddError("載入超時時間必須大於0秒");

            return result;
        }

        /// <summary>
        /// 重設為預設值
        /// </summary>
        public void ResetToDefaults()
        {
            var defaultSettings = new PerformanceSettings();
            
            CacheExpirationMinutes = defaultSettings.CacheExpirationMinutes;
            MaxDisplayTables = defaultSettings.MaxDisplayTables;
            MaxDisplayColumns = defaultSettings.MaxDisplayColumns;
            UIUpdateBatchSize = defaultSettings.UIUpdateBatchSize;
            ValidationDelayMs = defaultSettings.ValidationDelayMs;
            PerformanceMonitorIntervalMs = defaultSettings.PerformanceMonitorIntervalMs;
            EnableBackgroundPreloading = defaultSettings.EnableBackgroundPreloading;
            EnableMemoryOptimization = defaultSettings.EnableMemoryOptimization;
            MemoryThresholdMB = defaultSettings.MemoryThresholdMB;
            EnableUIVirtualization = defaultSettings.EnableUIVirtualization;
            EnableCaching = defaultSettings.EnableCaching;
            MaxCacheItems = defaultSettings.MaxCacheItems;
            EnablePerformanceMonitoring = defaultSettings.EnablePerformanceMonitoring;
            EnableVerboseLogging = defaultSettings.EnableVerboseLogging;
            LoadingTimeoutSeconds = defaultSettings.LoadingTimeoutSeconds;
        }

        /// <summary>
        /// 複製設定
        /// </summary>
        /// <returns>設定的副本</returns>
        public PerformanceSettings Clone()
        {
            return new PerformanceSettings
            {
                CacheExpirationMinutes = this.CacheExpirationMinutes,
                MaxDisplayTables = this.MaxDisplayTables,
                MaxDisplayColumns = this.MaxDisplayColumns,
                UIUpdateBatchSize = this.UIUpdateBatchSize,
                ValidationDelayMs = this.ValidationDelayMs,
                PerformanceMonitorIntervalMs = this.PerformanceMonitorIntervalMs,
                EnableBackgroundPreloading = this.EnableBackgroundPreloading,
                EnableMemoryOptimization = this.EnableMemoryOptimization,
                MemoryThresholdMB = this.MemoryThresholdMB,
                EnableUIVirtualization = this.EnableUIVirtualization,
                EnableCaching = this.EnableCaching,
                MaxCacheItems = this.MaxCacheItems,
                EnablePerformanceMonitoring = this.EnablePerformanceMonitoring,
                EnableVerboseLogging = this.EnableVerboseLogging,
                LoadingTimeoutSeconds = this.LoadingTimeoutSeconds
            };
        }
    }
}
