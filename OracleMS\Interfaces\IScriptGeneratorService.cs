using System.Data;
using OracleMS.Models;

namespace OracleMS.Interfaces;

public interface IScriptGeneratorService
{
    Task<string> GenerateCreateScriptAsync(IDbConnection connection, string objectName, DatabaseObjectType type);
    Task<string> GenerateInsertScriptAsync(IDbConnection connection, string tableName);
    Task<string> GenerateDropScriptAsync(string objectName, DatabaseObjectType type);
}