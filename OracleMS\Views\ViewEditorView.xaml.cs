using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.Reflection;
using System.IO;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// ViewEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class ViewEditorView : UserControl
    {
        private ViewEditorViewModel? ViewModel => DataContext as ViewEditorViewModel;

        public ViewEditorView()
        {
            InitializeComponent();
            
            // 載入 SQL 語法高亮定義
            LoadSqlSyntaxHighlighting();
            
            // 設定 TextEditor 的文字變更事件
            SqlDefinitionEditor.TextChanged += SqlDefinitionEditor_TextChanged;
            
            // 設定 DataContext 變更事件
            DataContextChanged += ViewEditorView_DataContextChanged;
            
            // 設定快捷鍵
            SetupKeyBindings();
        }

        private void ViewEditorView_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (e.OldValue is ViewEditorViewModel oldViewModel)
            {
                // 解除舊的 ViewModel 事件綁定
                oldViewModel.PropertyChanged -= ViewModel_PropertyChanged;
            }

            if (e.NewValue is ViewEditorViewModel newViewModel)
            {
                // 綁定新的 ViewModel 事件
                newViewModel.PropertyChanged += ViewModel_PropertyChanged;
                
                // 初始化編輯器文字
                SqlDefinitionEditor.Text = newViewModel.ViewDefinition;
            }
        }

        private void ViewModel_PropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(ViewEditorViewModel.ViewDefinition))
            {
                // 如果 ViewModel 的 ViewDefinition 屬性變更，更新編輯器文字
                if (ViewModel != null && SqlDefinitionEditor.Text != ViewModel.ViewDefinition)
                {
                    SqlDefinitionEditor.Text = ViewModel.ViewDefinition;
                }
            }
        }

        private void SqlDefinitionEditor_TextChanged(object? sender, EventArgs e)
        {
            // 將編輯器文字變更同步到 ViewModel
            if (ViewModel != null && SqlDefinitionEditor.Text != ViewModel.ViewDefinition)
            {
                ViewModel.ViewDefinition = SqlDefinitionEditor.Text;
            }
        }

        private void LoadSqlSyntaxHighlighting()
        {
            try
            {
                // 嘗試載入內嵌的 SQL 語法高亮定義
                using var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.SQL.xshd");
                if (stream != null)
                {
                    using var reader = new XmlTextReader(stream);
                    SqlDefinitionEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                }
                else
                {
                    // 如果找不到內嵌資源，嘗試從檔案載入
                    var xshdFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "SQL.xshd");
                    if (File.Exists(xshdFile))
                    {
                        using var reader = new XmlTextReader(xshdFile);
                        SqlDefinitionEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                    }
                }
            }
            catch (Exception ex)
            {
                // 載入語法高亮定義失敗，記錄錯誤但不中斷程式
                System.Diagnostics.Debug.WriteLine($"載入 SQL 語法高亮定義失敗: {ex.Message}");
            }
        }

        private void SetupKeyBindings()
        {
            // 設定 Ctrl+S 快捷鍵
            var saveBinding = new KeyBinding
            {
                Key = Key.S,
                Modifiers = ModifierKeys.Control,
                Command = ApplicationCommands.Save
            };
            
            this.InputBindings.Add(saveBinding);
            
            // 設定 F5 快捷鍵執行測試
            var executeBinding = new KeyBinding
            {
                Key = Key.F5,
                Command = new RelayCommand(() => ViewModel?.ExecuteTestCommand.Execute(null))
            };
            
            this.InputBindings.Add(executeBinding);
            
            // 設定命令綁定
            this.CommandBindings.Add(new CommandBinding(ApplicationCommands.Save, (s, e) => 
            {
                if (ViewModel != null && ViewModel.SaveCommand.CanExecute(null))
                {
                    ViewModel.SaveCommand.Execute(null);
                }
            }));
        }

        // 複製單元格內容
        public void CopyCell_Click(object sender, RoutedEventArgs e)
        {
            if (ResultsDataGrid.CurrentCell.Item != null && ResultsDataGrid.CurrentCell.Column != null)
            {
                var cellValue = ResultsDataGrid.CurrentCell.Column.GetCellContent(ResultsDataGrid.CurrentCell.Item);
                if (cellValue != null)
                {
                    Clipboard.SetText(cellValue.ToString() ?? string.Empty);
                }
            }
        }

        // 複製整行內容
        public void CopyRow_Click(object sender, RoutedEventArgs e)
        {
            if (ResultsDataGrid.SelectedItem != null)
            {
                var rowData = ResultsDataGrid.SelectedItem;
                var rowText = string.Join("\t", rowData.GetType().GetProperties()
                    .Select(p => p.GetValue(rowData)?.ToString() ?? string.Empty));
                
                Clipboard.SetText(rowText);
            }
        }

        // 複製所有內容
        public void CopyAll_Click(object sender, RoutedEventArgs e)
        {
            if (ResultsDataGrid.ItemsSource != null)
            {
                var items = ResultsDataGrid.ItemsSource.Cast<object>().ToList();
                if (items.Any())
                {
                    // 取得欄位名稱
                    var properties = items[0].GetType().GetProperties();
                    var header = string.Join("\t", properties.Select(p => p.Name));
                    
                    // 取得資料
                    var rows = items.Select(item => string.Join("\t", properties
                        .Select(p => p.GetValue(item)?.ToString() ?? string.Empty)));
                    
                    // 組合標題和資料
                    var text = header + Environment.NewLine + string.Join(Environment.NewLine, rows);
                    
                    Clipboard.SetText(text);
                }
            }
        }
    }


}