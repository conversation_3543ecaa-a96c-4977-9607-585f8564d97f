<UserControl x:Class="OracleMS.Views.IndexEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:controls="clr-namespace:OracleMS.Controls"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:IndexEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- Inverse Boolean Converter -->
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MinWidth" Value="75"/>
            <Setter Property="Height" Value="28"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Style for form labels -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="0,5,0,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <!-- Style for form controls -->
        <Style x:Key="FormControlStyle" TargetType="Control">
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Height" Value="25"/>
        </Style>

        <!-- Style for column selection buttons -->
        <Style x:Key="ColumnButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <!-- Style for ListBox -->
        <Style x:Key="ColumnListBoxStyle" TargetType="ListBox">
            <Setter Property="Height" Value="200"/>
            <Setter Property="BorderBrush" Value="Gray"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="SelectionMode" Value="Single"/>
        </Style>

        <!-- Style for Virtualized ListBox -->
        <Style x:Key="VirtualizedColumnListBoxStyle" TargetType="controls:VirtualizedColumnListBox" BasedOn="{StaticResource ColumnListBoxStyle}">
            <Setter Property="VirtualizingPanel.IsVirtualizing" Value="True"/>
            <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Recycling"/>
            <Setter Property="VirtualizingPanel.ScrollUnit" Value="Item"/>
            <Setter Property="ScrollViewer.CanContentScroll" Value="True"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="controls:VirtualizedColumnListBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ScrollViewer x:Name="ScrollViewer" 
                                          Focusable="False"
                                          CanContentScroll="True"
                                          HorizontalScrollBarVisibility="Auto"
                                          VerticalScrollBarVisibility="Auto">
                                <VirtualizingStackPanel IsItemsHost="True"
                                                         VirtualizingPanel.IsVirtualizing="True"
                                                         VirtualizingPanel.VirtualizationMode="Recycling"/>
                            </ScrollViewer>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Style for read-only ComboBox in edit mode -->
        <Style x:Key="ModeAwareComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource FormControlStyle}">
            <Setter Property="IsEnabled" Value="True"/>
            <Setter Property="Background" Value="White"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                    <Setter Property="IsEnabled" Value="False"/>
                    <Setter Property="Background" Value="LightGray"/>
                    <Setter Property="Foreground" Value="DarkGray"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Style for read-only TextBox in edit mode -->
        <Style x:Key="ModeAwareTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource FormControlStyle}">
            <Setter Property="IsReadOnly" Value="False"/>
            <Setter Property="Background" Value="White"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                    <Setter Property="IsReadOnly" Value="True"/>
                    <Setter Property="Background" Value="LightGray"/>
                    <Setter Property="Foreground" Value="DarkGray"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Style for mode indicator -->
        <Style x:Key="ModeIndicatorStyle" TargetType="Border">
            <Setter Property="CornerRadius" Value="3"/>
            <Setter Property="Padding" Value="8,2"/>
            <Setter Property="Margin" Value="10,0,0,0"/>
            <Setter Property="Background" Value="LightGreen"/>
            <Setter Property="BorderBrush" Value="Green"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                    <Setter Property="Background" Value="LightBlue"/>
                    <Setter Property="BorderBrush" Value="Blue"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Style for mode indicator text -->
        <Style x:Key="ModeIndicatorTextStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="DarkGreen"/>
            <Setter Property="Text" Value="新增模式"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                    <Setter Property="Foreground" Value="DarkBlue"/>
                    <Setter Property="Text" Value="編輯模式"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Style for dynamic title text -->
        <Style x:Key="DynamicTitleStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Foreground" Value="DarkBlue"/>
            <Setter Property="Text" Value="新增索引"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                    <Setter Property="Text" Value="編輯索引"/>
                    <Setter Property="Foreground" Value="DarkSlateBlue"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Title -->
            <RowDefinition Height="*"/> <!-- Content Area -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Title Bar with Mode Indicator -->
        <Border Grid.Row="0" Padding="10,5">
            <Border.Style>
                <Style TargetType="Border">
                    <Setter Property="Background" Value="LightGreen"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                            <Setter Property="Background" Value="LightBlue"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Border.Style>
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Style="{StaticResource DynamicTitleStyle}"/>
                <Border Style="{StaticResource ModeIndicatorStyle}">
                    <TextBlock Style="{StaticResource ModeIndicatorTextStyle}"/>
                </Border>
            </StackPanel>
        </Border>

        <!-- Content Area -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/> <!-- Basic Information -->
                    <RowDefinition Height="Auto"/> <!-- Column Selection -->
                    <RowDefinition Height="Auto"/> <!-- DDL Preview -->
                    <RowDefinition Height="Auto"/> <!-- Operation Buttons -->
                </Grid.RowDefinitions>

                <!-- Basic Information Area -->
                <GroupBox Grid.Row="0" Margin="0,0,0,20">
                    <GroupBox.Header>
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="基本資訊" FontWeight="Bold"/>
                            <Border CornerRadius="2" Padding="4,1" Margin="8,0,0,0" Background="Orange" BorderBrush="DarkOrange" BorderThickness="1">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                                <Setter Property="Visibility" Value="Visible"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="部分欄位唯讀" FontSize="9" FontWeight="Bold" Foreground="White"/>
                            </Border>
                        </StackPanel>
                    </GroupBox.Header>
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Schema -->
                        <StackPanel Grid.Row="0" Grid.Column="0" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="Schema:" Style="{StaticResource FormLabelStyle}"/>
                                <TextBlock Text=" (唯讀)" FontSize="10" Foreground="Gray" VerticalAlignment="Bottom" Margin="5,0,0,2">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <!-- Schema Loading Indicator -->
                                <ProgressBar Width="16" Height="16" 
                                             IsIndeterminate="True"
                                             Visibility="{Binding IsLoadingSchemas, Converter={StaticResource BooleanToVisibilityConverter}}"
                                             Margin="5,0,0,0"
                                             VerticalAlignment="Center"/>
                            </StackPanel>
                            <ComboBox ItemsSource="{Binding AvailableSchemas}"
                                      SelectedItem="{Binding SelectedSchema}"
                                      Style="{StaticResource ModeAwareComboBoxStyle}"
                                      IsEnabled="{Binding IsLoadingSchemas, Converter={StaticResource InverseBooleanConverter}}"/>
                        </StackPanel>

                        <!-- Table -->
                        <StackPanel Grid.Row="0" Grid.Column="1" Margin="10,0,0,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="Table:" Style="{StaticResource FormLabelStyle}"/>
                                <TextBlock Text=" (唯讀)" FontSize="10" Foreground="Gray" VerticalAlignment="Bottom" Margin="5,0,0,2">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <!-- Table Loading Indicator -->
                                <ProgressBar Width="16" Height="16" 
                                             IsIndeterminate="True"
                                             Visibility="{Binding IsLoadingTables, Converter={StaticResource BooleanToVisibilityConverter}}"
                                             Margin="5,0,0,0"
                                             VerticalAlignment="Center"/>
                            </StackPanel>
                            <ComboBox ItemsSource="{Binding AvailableTables}"
                                      SelectedItem="{Binding SelectedTable}"
                                      Style="{StaticResource ModeAwareComboBoxStyle}"
                                      IsEnabled="{Binding IsLoadingTables, Converter={StaticResource InverseBooleanConverter}}"/>
                        </StackPanel>

                        <!-- Index Name -->
                        <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="索引名稱:" Style="{StaticResource FormLabelStyle}"/>
                                <TextBlock Text=" (唯讀)" FontSize="10" Foreground="Gray" VerticalAlignment="Bottom" Margin="5,0,0,2">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                            </StackPanel>
                            <TextBox Text="{Binding IndexDefinition.Name, UpdateSourceTrigger=PropertyChanged}"
                                     Style="{StaticResource ModeAwareTextBoxStyle}"
                                     VerticalContentAlignment="Center"/>
                        </StackPanel>

                        <!-- Uniqueness -->
                        <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="唯一性:" Style="{StaticResource FormLabelStyle}"/>
                            <CheckBox IsChecked="{Binding IndexDefinition.IsUnique}"
                                      Content="唯一索引"
                                      VerticalAlignment="Center"
                                      Margin="0,0,0,10"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- Column Selection Area -->
                <GroupBox Grid.Row="1" Header="欄位選擇" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Available Columns (Left ListBox) -->
                        <StackPanel Grid.Column="0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="可用欄位:" Style="{StaticResource FormLabelStyle}"/>
                                <!-- Column Loading Indicator -->
                                <ProgressBar Width="16" Height="16" 
                                             IsIndeterminate="True"
                                             Visibility="{Binding IsLoadingColumns, Converter={StaticResource BooleanToVisibilityConverter}}"
                                             Margin="5,0,0,0"
                                             VerticalAlignment="Center"/>
                            </StackPanel>
                            <controls:VirtualizedColumnListBox x:Name="AvailableColumnsListBox"
                                                               ItemsSource="{Binding AvailableColumns}"
                                                               Style="{StaticResource VirtualizedColumnListBoxStyle}"
                                                               IsEnabled="{Binding IsLoadingColumns, Converter={StaticResource InverseBooleanConverter}}"/>
                        </StackPanel>

                        <!-- Control Buttons (Middle) -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Center" Margin="10,0">
                            <Button Content="&gt;"
                                    Command="{Binding AddColumnCommand}"
                                    CommandParameter="{Binding SelectedItem, ElementName=AvailableColumnsListBox}"
                                    Style="{StaticResource ColumnButtonStyle}"
                                    ToolTip="將選定欄位加入索引"/>
                            
                            <Button Content="&lt;"
                                    Command="{Binding RemoveColumnCommand}"
                                    CommandParameter="{Binding SelectedItem, ElementName=SelectedColumnsListBox}"
                                    Style="{StaticResource ColumnButtonStyle}"
                                    ToolTip="將選定欄位從索引移除"/>
                            
                            <Separator Margin="0,5"/>
                            
                            <Button Content="↑"
                                    Command="{Binding MoveColumnUpCommand}"
                                    CommandParameter="{Binding SelectedItem, ElementName=SelectedColumnsListBox}"
                                    Style="{StaticResource ColumnButtonStyle}"
                                    ToolTip="將選定欄位上移"/>
                            
                            <Button Content="↓"
                                    Command="{Binding MoveColumnDownCommand}"
                                    CommandParameter="{Binding SelectedItem, ElementName=SelectedColumnsListBox}"
                                    Style="{StaticResource ColumnButtonStyle}"
                                    ToolTip="將選定欄位下移"/>
                        </StackPanel>

                        <!-- Selected Columns (Right ListBox) -->
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="索引欄位:" Style="{StaticResource FormLabelStyle}"/>
                            <controls:VirtualizedColumnListBox x:Name="SelectedColumnsListBox"
                                                               ItemsSource="{Binding SelectedColumns}"
                                                               Style="{StaticResource VirtualizedColumnListBoxStyle}"
                                                               IsEnabled="{Binding IsLoadingColumns, Converter={StaticResource InverseBooleanConverter}}"/>
                        </StackPanel>
                    </Grid>
                </GroupBox>

                <!-- DDL Preview Area -->
                <GroupBox Grid.Row="2" Header="DDL 預覽" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="200"/>
                        </Grid.RowDefinitions>

                        <!-- DDL Toolbar -->
                        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True" Margin="0,0,0,5">
                            <Button Command="{Binding CopyDdlCommand}"
                                    Style="{StaticResource ToolbarButtonStyle}"
                                    ToolTip="複製 DDL 腳本">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📋" Margin="0,0,4,0"/>
                                        <TextBlock Text="複製腳本"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <Button Command="{Binding SaveDdlCommand}"
                                    Style="{StaticResource ToolbarButtonStyle}"
                                    ToolTip="儲存 DDL 腳本至檔案">
                                <Button.Content>
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="💾" Margin="0,0,4,0"/>
                                        <TextBlock Text="儲存腳本"/>
                                    </StackPanel>
                                </Button.Content>
                            </Button>

                            <!-- Loading Status Panel -->
                            <StackPanel Orientation="Horizontal" 
                                        Visibility="{Binding IsAnyLoadingInProgress, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Margin="10,0">
                                <ProgressBar Width="100" Height="16"
                                             Value="{Binding LoadingProgress, Mode=OneWay}"
                                             Maximum="100"
                                             Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding LoadingMessage}" 
                                           FontSize="11" 
                                           Foreground="Gray"
                                           VerticalAlignment="Center"/>
                                <Button Content="取消" 
                                        Command="{Binding CancelLoadingCommand}"
                                        Margin="5,0,0,0"
                                        Padding="5,2"
                                        FontSize="10"/>
                            </StackPanel>
                        </ToolBar>

                        <!-- DDL Preview Editor -->
                        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
                            <avalonedit:TextEditor x:Name="DdlPreviewEditor"
                                                   FontFamily="Consolas"
                                                   FontSize="12"
                                                   ShowLineNumbers="True"
                                                   WordWrap="False"
                                                   IsReadOnly="True"
                                                   HorizontalScrollBarVisibility="Auto"
                                                   VerticalScrollBarVisibility="Auto"
                                                   Background="White"
                                                   Foreground="Black">
                                <avalonedit:TextEditor.Options>
                                    <avalonedit:TextEditorOptions ShowSpaces="False"
                                                                  ShowTabs="False"
                                                                  ShowEndOfLine="False"
                                                                  ShowBoxForControlCharacters="False"
                                                                  ConvertTabsToSpaces="True"
                                                                  IndentationSize="4"/>
                                </avalonedit:TextEditor.Options>
                            </avalonedit:TextEditor>
                        </Border>
                    </Grid>
                </GroupBox>

                <!-- Operation Buttons Area -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,10,0,0">
                    <Button Content="儲存"
                            Command="{Binding CreateIndexCommand}"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource ToolbarButtonStyle}">
                                <Setter Property="Visibility" Value="Visible"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                        <Setter Property="Visibility" Value="Collapsed"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="更新"
                            Command="{Binding UpdateIndexCommand}"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource ToolbarButtonStyle}">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="重建索引"
                            Command="{Binding RebuildIndexCommand}"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource ToolbarButtonStyle}">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="分析索引"
                            Command="{Binding AnalyzeIndexCommand}"
                            Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button" BasedOn="{StaticResource ToolbarButtonStyle}">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button Content="取消"
                            Style="{StaticResource ToolbarButtonStyle}"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </ScrollViewer>

        <!-- Status Bar with Mode Indicator -->
        <StatusBar Grid.Row="2">
            <StatusBar.Style>
                <Style TargetType="StatusBar">
                    <Setter Property="Background" Value="LightGray"/>
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                            <Setter Property="Background" Value="AliceBlue"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </StatusBar.Style>
            
            <!-- Mode Indicator in Status Bar -->
            <StatusBarItem>
                <Border CornerRadius="2" Padding="6,1" Margin="2">
                    <Border.Style>
                        <Style TargetType="Border">
                            <Setter Property="Background" Value="Green"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                    <Setter Property="Background" Value="Blue"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Border.Style>
                    <TextBlock FontSize="10" FontWeight="Bold" Foreground="White">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Text" Value="新增"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsEditMode}" Value="True">
                                        <Setter Property="Text" Value="編輯"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>
                </Border>
            </StatusBarItem>
            
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding IndexDefinition.TableName, StringFormat='表格: {0}'}" 
                           Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>

            <!-- 效能監控資訊 -->
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="快取:" Style="{StaticResource StatusTextStyle}" Margin="0,0,2,0"/>
                    <TextBlock Style="{StaticResource StatusTextStyle}">
                        <TextBlock.Text>
                            <MultiBinding StringFormat="{}{0}/{1}">
                                <Binding Path="CachedSchemasCount"/>
                                <Binding Path="CachedTablesCount"/>
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                </StackPanel>
            </StatusBarItem>

            <!-- 記憶體使用情況 -->
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding MemoryUsageText}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>

            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Text" Value="載入中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsOperationInProgress}" Value="True">
                                    <Setter Property="Text" Value="操作進行中..."/>
                                    <Setter Property="Foreground" Value="Orange"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>