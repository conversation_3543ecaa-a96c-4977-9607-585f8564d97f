using OracleMS.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Xunit;

namespace OracleMS.Tests.Models
{
    public class ColumnSelectionManagerTests
    {
        [Fact]
        public void ColumnSelectionManager_DefaultConstructor_InitializesCollections()
        {
            // Arrange & Act
            var manager = new ColumnSelectionManager();

            // Assert
            Assert.NotNull(manager.AvailableColumns);
            Assert.NotNull(manager.SelectedColumns);
            Assert.Equal(0, manager.AvailableColumns.Count);
            Assert.Equal(0, manager.SelectedColumns.Count);
        }

        [Fact]
        public void MoveToSelected_ValidColumn_MovesFromAvailableToSelected()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("COLUMN1");
            manager.AvailableColumns.Add("COLUMN2");

            // Act
            manager.MoveToSelected("COLUMN1");

            // Assert
            Assert.Equal(1, manager.AvailableColumns.Count);
            Assert.Equal(1, manager.SelectedColumns.Count);
            Assert.False(manager.AvailableColumns.Contains("COLUMN1"));
            Assert.True(manager.SelectedColumns.Contains("COLUMN1"));
            Assert.True(manager.AvailableColumns.Contains("COLUMN2"));
        }

        [Fact]
        public void MoveToSelected_NonExistentColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("COLUMN1");

            // Act
            manager.MoveToSelected("NONEXISTENT");

            // Assert
            Assert.Equal(1, manager.AvailableColumns.Count);
            Assert.Equal(0, manager.SelectedColumns.Count);
            Assert.True(manager.AvailableColumns.Contains("COLUMN1"));
        }

        [Fact]
        public void MoveToSelected_NullOrEmptyColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("COLUMN1");

            // Act
            manager.MoveToSelected(null);
            manager.MoveToSelected("");
            manager.MoveToSelected("   ");

            // Assert
            Assert.Equal(1, manager.AvailableColumns.Count);
            Assert.Equal(0, manager.SelectedColumns.Count);
        }

        [Fact]
        public void MoveToAvailable_ValidColumn_MovesFromSelectedToAvailable()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");

            // Act
            manager.MoveToAvailable("COLUMN1");

            // Assert
            Assert.Equal(1, manager.AvailableColumns.Count);
            Assert.Equal(1, manager.SelectedColumns.Count);
            Assert.True(manager.AvailableColumns.Contains("COLUMN1"));
            Assert.False(manager.SelectedColumns.Contains("COLUMN1"));
            Assert.True(manager.SelectedColumns.Contains("COLUMN2"));
        }

        [Fact]
        public void MoveToAvailable_NonExistentColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");

            // Act
            manager.MoveToAvailable("NONEXISTENT");

            // Assert
            Assert.Equal(0, manager.AvailableColumns.Count);
            Assert.Equal(1, manager.SelectedColumns.Count);
            Assert.True(manager.SelectedColumns.Contains("COLUMN1"));
        }

        [Fact]
        public void MoveToAvailable_NullOrEmptyColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");

            // Act
            manager.MoveToAvailable(null);
            manager.MoveToAvailable("");
            manager.MoveToAvailable("   ");

            // Assert
            Assert.Equal(0, manager.AvailableColumns.Count);
            Assert.Equal(1, manager.SelectedColumns.Count);
        }

        [Fact]
        public void MoveUp_FirstColumn_DoesNotMove()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");
            manager.SelectedColumns.Add("COLUMN3");

            // Act
            manager.MoveUp("COLUMN1");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[1]);
            Assert.Equal("COLUMN3", manager.SelectedColumns[2]);
        }

        [Fact]
        public void MoveUp_MiddleColumn_MovesUpOnePosition()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");
            manager.SelectedColumns.Add("COLUMN3");

            // Act
            manager.MoveUp("COLUMN2");

            // Assert
            Assert.Equal("COLUMN2", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN1", manager.SelectedColumns[1]);
            Assert.Equal("COLUMN3", manager.SelectedColumns[2]);
        }

        [Fact]
        public void MoveUp_LastColumn_MovesUpOnePosition()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");
            manager.SelectedColumns.Add("COLUMN3");

            // Act
            manager.MoveUp("COLUMN3");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN3", manager.SelectedColumns[1]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[2]);
        }

        [Fact]
        public void MoveUp_NonExistentColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");

            // Act
            manager.MoveUp("NONEXISTENT");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[1]);
        }

        [Fact]
        public void MoveUp_NullOrEmptyColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");

            // Act
            manager.MoveUp(null);
            manager.MoveUp("");
            manager.MoveUp("   ");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[1]);
        }

        [Fact]
        public void MoveDown_LastColumn_DoesNotMove()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");
            manager.SelectedColumns.Add("COLUMN3");

            // Act
            manager.MoveDown("COLUMN3");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[1]);
            Assert.Equal("COLUMN3", manager.SelectedColumns[2]);
        }

        [Fact]
        public void MoveDown_FirstColumn_MovesDownOnePosition()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");
            manager.SelectedColumns.Add("COLUMN3");

            // Act
            manager.MoveDown("COLUMN1");

            // Assert
            Assert.Equal("COLUMN2", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN1", manager.SelectedColumns[1]);
            Assert.Equal("COLUMN3", manager.SelectedColumns[2]);
        }

        [Fact]
        public void MoveDown_MiddleColumn_MovesDownOnePosition()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");
            manager.SelectedColumns.Add("COLUMN3");

            // Act
            manager.MoveDown("COLUMN2");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN3", manager.SelectedColumns[1]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[2]);
        }

        [Fact]
        public void MoveDown_NonExistentColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");

            // Act
            manager.MoveDown("NONEXISTENT");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[1]);
        }

        [Fact]
        public void MoveDown_NullOrEmptyColumn_DoesNothing()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("COLUMN1");
            manager.SelectedColumns.Add("COLUMN2");

            // Act
            manager.MoveDown(null);
            manager.MoveDown("");
            manager.MoveDown("   ");

            // Assert
            Assert.Equal("COLUMN1", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN2", manager.SelectedColumns[1]);
        }

        [Fact]
        public void LoadTableColumns_ClearsExistingCollections()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("OLD_COLUMN1");
            manager.SelectedColumns.Add("OLD_COLUMN2");

            // Act
            manager.LoadTableColumns("TEST_SCHEMA", "TEST_TABLE");

            // Assert
            Assert.Equal(0, manager.AvailableColumns.Count);
            Assert.Equal(0, manager.SelectedColumns.Count);
        }

        [Fact]
        public void CompleteWorkflow_MoveColumnsAndReorder_WorksCorrectly()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("ID");
            manager.AvailableColumns.Add("NAME");
            manager.AvailableColumns.Add("EMAIL");
            manager.AvailableColumns.Add("CREATED_DATE");

            // Act - Move columns to selected
            manager.MoveToSelected("NAME");
            manager.MoveToSelected("ID");
            manager.MoveToSelected("CREATED_DATE");

            // Assert - Check initial selection
            Assert.Equal(1, manager.AvailableColumns.Count);
            Assert.Equal(3, manager.SelectedColumns.Count);
            Assert.True(manager.AvailableColumns.Contains("EMAIL"));
            Assert.Equal("NAME", manager.SelectedColumns[0]);
            Assert.Equal("ID", manager.SelectedColumns[1]);
            Assert.Equal("CREATED_DATE", manager.SelectedColumns[2]);

            // Act - Reorder selected columns
            manager.MoveUp("ID"); // Move ID up to position 0
            manager.MoveDown("NAME"); // Move NAME down to position 1

            // Assert - Check reordering
            Assert.Equal("ID", manager.SelectedColumns[0]);
            Assert.Equal("NAME", manager.SelectedColumns[1]);
            Assert.Equal("CREATED_DATE", manager.SelectedColumns[2]);

            // Act - Move one column back to available
            manager.MoveToAvailable("NAME");

            // Assert - Check final state
            Assert.Equal(2, manager.AvailableColumns.Count);
            Assert.Equal(2, manager.SelectedColumns.Count);
            Assert.True(manager.AvailableColumns.Contains("EMAIL"));
            Assert.True(manager.AvailableColumns.Contains("NAME"));
            Assert.Equal("ID", manager.SelectedColumns[0]);
            Assert.Equal("CREATED_DATE", manager.SelectedColumns[1]);
        }

        #region PopulateSelectedColumns Tests

        [Fact]
        public void PopulateSelectedColumns_ValidData_PopulatesColumnsSuccessfully()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "COLUMN_B", Position = 2 },
                new IndexColumnDefinition { ColumnName = "COLUMN_A", Position = 1 },
                new IndexColumnDefinition { ColumnName = "COLUMN_C", Position = 3 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(3, manager.SelectedColumns.Count);
            Assert.Equal("COLUMN_A", manager.SelectedColumns[0]); // Position 1
            Assert.Equal("COLUMN_B", manager.SelectedColumns[1]); // Position 2
            Assert.Equal("COLUMN_C", manager.SelectedColumns[2]); // Position 3

            // Verify logging
            Assert.Contains("Starting column population process", output);
            Assert.Contains("Processing 3 columns for population", output);
            Assert.Contains("Column population completed successfully", output);
            Assert.Contains("Added column 'COLUMN_A' at position 1", output);
            Assert.Contains("Added column 'COLUMN_B' at position 2", output);
            Assert.Contains("Added column 'COLUMN_C' at position 3", output);
        }

        [Fact]
        public void PopulateSelectedColumns_NullCollection_HandlesGracefully()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("EXISTING_COLUMN");

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(null);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(1, manager.SelectedColumns.Count); // Should remain unchanged
            Assert.Equal("EXISTING_COLUMN", manager.SelectedColumns[0]);

            // Verify logging
            Assert.Contains("Starting column population process", output);
            Assert.Contains("Warning: Columns collection is null, skipping population", output);
        }

        [Fact]
        public void PopulateSelectedColumns_EmptyCollection_ClearsSelectedColumns()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("EXISTING_COLUMN1");
            manager.SelectedColumns.Add("EXISTING_COLUMN2");
            var emptyColumns = new List<IndexColumnDefinition>();

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(emptyColumns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(0, manager.SelectedColumns.Count);

            // Verify logging
            Assert.Contains("Starting column population process", output);
            Assert.Contains("Processing 0 columns for population", output);
            Assert.Contains("Info: No columns to populate, clearing selected columns", output);
        }

        [Fact]
        public void PopulateSelectedColumns_ColumnOrderingByPosition_SortsCorrectly()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "COLUMN_Z", Position = 10 },
                new IndexColumnDefinition { ColumnName = "COLUMN_A", Position = 1 },
                new IndexColumnDefinition { ColumnName = "COLUMN_M", Position = 5 },
                new IndexColumnDefinition { ColumnName = "COLUMN_B", Position = 2 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert - Verify correct ordering by Position
            Assert.Equal(4, manager.SelectedColumns.Count);
            Assert.Equal("COLUMN_A", manager.SelectedColumns[0]); // Position 1
            Assert.Equal("COLUMN_B", manager.SelectedColumns[1]); // Position 2
            Assert.Equal("COLUMN_M", manager.SelectedColumns[2]); // Position 5
            Assert.Equal("COLUMN_Z", manager.SelectedColumns[3]); // Position 10

            // Verify logging mentions sorting
            Assert.Contains("Sorted 4 columns by Position property", output);
        }

        [Fact]
        public void PopulateSelectedColumns_RemovesFromAvailableColumns_PreventsDuplication()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("COLUMN_A");
            manager.AvailableColumns.Add("COLUMN_B");
            manager.AvailableColumns.Add("COLUMN_C");
            manager.AvailableColumns.Add("COLUMN_D");

            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "COLUMN_A", Position = 1 },
                new IndexColumnDefinition { ColumnName = "COLUMN_C", Position = 2 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(2, manager.SelectedColumns.Count);
            Assert.Equal("COLUMN_A", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN_C", manager.SelectedColumns[1]);

            // Verify columns were removed from AvailableColumns
            Assert.Equal(2, manager.AvailableColumns.Count);
            Assert.False(manager.AvailableColumns.Contains("COLUMN_A"));
            Assert.True(manager.AvailableColumns.Contains("COLUMN_B"));
            Assert.False(manager.AvailableColumns.Contains("COLUMN_C"));
            Assert.True(manager.AvailableColumns.Contains("COLUMN_D"));

            // Verify logging
            Assert.Contains("Removed column 'COLUMN_A' from AvailableColumns to prevent duplication", output);
            Assert.Contains("Removed column 'COLUMN_C' from AvailableColumns to prevent duplication", output);
        }

        [Fact]
        public void PopulateSelectedColumns_ColumnsNotInAvailable_DoesNotAttemptRemoval()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.AvailableColumns.Add("OTHER_COLUMN");

            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "COLUMN_A", Position = 1 },
                new IndexColumnDefinition { ColumnName = "COLUMN_B", Position = 2 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(2, manager.SelectedColumns.Count);
            Assert.Equal("COLUMN_A", manager.SelectedColumns[0]);
            Assert.Equal("COLUMN_B", manager.SelectedColumns[1]);

            // Verify AvailableColumns remains unchanged
            Assert.Equal(1, manager.AvailableColumns.Count);
            Assert.True(manager.AvailableColumns.Contains("OTHER_COLUMN"));

            // Verify no removal logging for these columns
            Assert.DoesNotContain("Removed column 'COLUMN_A' from AvailableColumns", output);
            Assert.DoesNotContain("Removed column 'COLUMN_B' from AvailableColumns", output);
        }

        [Fact]
        public void PopulateSelectedColumns_ClearsExistingSelections_BeforePopulating()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            manager.SelectedColumns.Add("OLD_COLUMN1");
            manager.SelectedColumns.Add("OLD_COLUMN2");

            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "NEW_COLUMN", Position = 1 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(1, manager.SelectedColumns.Count);
            Assert.Equal("NEW_COLUMN", manager.SelectedColumns[0]);
            Assert.False(manager.SelectedColumns.Contains("OLD_COLUMN1"));
            Assert.False(manager.SelectedColumns.Contains("OLD_COLUMN2"));

            // Verify logging
            Assert.Contains("Clearing existing selected columns", output);
        }

        [Fact]
        public void PopulateSelectedColumns_EmptyColumnName_SkipsColumn()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "VALID_COLUMN", Position = 1 },
                new IndexColumnDefinition { ColumnName = "", Position = 2 },
                new IndexColumnDefinition { ColumnName = null, Position = 3 },
                new IndexColumnDefinition { ColumnName = "   ", Position = 4 },
                new IndexColumnDefinition { ColumnName = "ANOTHER_VALID", Position = 5 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert - Only valid columns should be added
            Assert.Equal(2, manager.SelectedColumns.Count);
            Assert.Equal("VALID_COLUMN", manager.SelectedColumns[0]);
            Assert.Equal("ANOTHER_VALID", manager.SelectedColumns[1]);

            // Verify warning logging for empty columns
            Assert.Contains("Warning: Skipping column with empty name at position 2", output);
            Assert.Contains("Warning: Skipping column with empty name at position 3", output);
            Assert.Contains("Warning: Skipping column with empty name at position 4", output);
        }

        [Fact]
        public void PopulateSelectedColumns_ExceptionDuringProcessing_RethrowsException()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            
            // Create a mock that will throw an exception when enumerated
            var mockColumns = new ThrowingEnumerable();

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(() => 
                manager.PopulateSelectedColumns(mockColumns));

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Verify error logging
            Assert.Contains("Error during column population: Test exception", output);
            Assert.Contains("Stack trace:", output);
            Assert.Equal("Test exception", exception.Message);
        }

        [Fact]
        public void PopulateSelectedColumns_SingleColumn_PopulatesCorrectly()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "SINGLE_COLUMN", Position = 1 }
            };

            // Capture console output
            var originalOut = Console.Out;
            using var stringWriter = new StringWriter();
            Console.SetOut(stringWriter);

            // Act
            manager.PopulateSelectedColumns(columns);

            // Restore console output
            Console.SetOut(originalOut);
            var output = stringWriter.ToString();

            // Assert
            Assert.Equal(1, manager.SelectedColumns.Count);
            Assert.Equal("SINGLE_COLUMN", manager.SelectedColumns[0]);

            // Verify logging
            Assert.Contains("Processing 1 columns for population", output);
            Assert.Contains("Total selected columns: 1", output);
        }

        [Fact]
        public void PopulateSelectedColumns_DuplicatePositions_MaintainsStableSort()
        {
            // Arrange
            var manager = new ColumnSelectionManager();
            var columns = new List<IndexColumnDefinition>
            {
                new IndexColumnDefinition { ColumnName = "COLUMN_B", Position = 1 },
                new IndexColumnDefinition { ColumnName = "COLUMN_A", Position = 1 }, // Same position
                new IndexColumnDefinition { ColumnName = "COLUMN_C", Position = 2 }
            };

            // Act
            manager.PopulateSelectedColumns(columns);

            // Assert - Should maintain original order for same positions (stable sort)
            Assert.Equal(3, manager.SelectedColumns.Count);
            Assert.Equal("COLUMN_B", manager.SelectedColumns[0]); // First with position 1
            Assert.Equal("COLUMN_A", manager.SelectedColumns[1]); // Second with position 1
            Assert.Equal("COLUMN_C", manager.SelectedColumns[2]); // Position 2
        }

        #endregion

        // Helper class for testing exception handling
        private class ThrowingEnumerable : IEnumerable<IndexColumnDefinition>
        {
            public IEnumerator<IndexColumnDefinition> GetEnumerator()
            {
                throw new InvalidOperationException("Test exception");
            }

            System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
            {
                return GetEnumerator();
            }
        }
    }
}