using System;
using Moq;
using OracleMS.ViewModels;
using OracleMS.Models;
using OracleMS.Interfaces;
using Microsoft.Extensions.Logging;

namespace OracleMS.Tests.Helpers
{
    public static class IndexEditorViewModelTestHelper
    {
        /// <summary>
        /// 創建用於測試的 IndexEditorViewModel 實例
        /// </summary>
        /// <returns>配置好的測試 ViewModel</returns>
        public static IndexEditorViewModel CreateTestViewModel()
        {
            // 創建模擬的依賴項
            var mockDatabaseService = new Mock<IDatabaseService>();
            var mockObjectEditorService = new Mock<IObjectEditorService>();
            
            // 創建測試用的 IndexEditorInfo
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsEditMode = false,
                Columns = new System.Collections.Generic.List<string> { "COLUMN1", "COLUMN2" }
            };

            // 創建模擬的其他依賴項
            var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            var mockLogger = new Mock<Microsoft.Extensions.Logging.ILogger>();
            
            // 創建 ViewModel
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                mockDatabaseService.Object,
                mockScriptGeneratorService.Object,
                mockObjectEditorService.Object,
                () => null, // connection factory
                mockLogger.Object
            );

            return viewModel;
        }

        /// <summary>
        /// 創建用於編輯模式測試的 IndexEditorViewModel 實例
        /// </summary>
        /// <returns>配置為編輯模式的測試 ViewModel</returns>
        public static IndexEditorViewModel CreateEditModeViewModel()
        {
            var mockDatabaseService = new Mock<IDatabaseService>();
            var mockObjectEditorService = new Mock<IObjectEditorService>();
            
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "EXISTING_INDEX",
                IsEditMode = true,
                Columns = new System.Collections.Generic.List<string> { "COLUMN1", "COLUMN2" }
            };

            var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            var mockLogger = new Mock<Microsoft.Extensions.Logging.ILogger>();
            
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                mockDatabaseService.Object,
                mockScriptGeneratorService.Object,
                mockObjectEditorService.Object,
                () => null, // connection factory
                mockLogger.Object
            );

            return viewModel;
        }

        /// <summary>
        /// 創建具有驗證錯誤的測試 IndexDefinition
        /// </summary>
        /// <returns>有驗證錯誤的 IndexDefinition</returns>
        public static IndexDefinition CreateInvalidIndexDefinition()
        {
            return new IndexDefinition
            {
                Name = "", // 空名稱會觸發驗證錯誤
                Owner = "",
                TableName = "",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>()
            };
        }

        /// <summary>
        /// 創建有效的測試 IndexDefinition
        /// </summary>
        /// <returns>有效的 IndexDefinition</returns>
        public static IndexDefinition CreateValidIndexDefinition()
        {
            return new IndexDefinition
            {
                Name = "IDX_TEST_VALID",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IsUnique = false,
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "COLUMN2", Position = 2 }
                }
            };
        }

        /// <summary>
        /// 創建具有警告的測試 IndexDefinition
        /// </summary>
        /// <returns>會產生警告的 IndexDefinition</returns>
        public static IndexDefinition CreateIndexDefinitionWithWarnings()
        {
            var indexDefinition = new IndexDefinition
            {
                Name = "BADNAME", // 沒有 IDX 前綴，會產生警告
                Owner = "TEST_SCHEMA",
                TableName = "USERS",
                IsUnique = true,
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>()
            };

            // 新增多個欄位以觸發效能警告
            for (int i = 1; i <= 6; i++)
            {
                indexDefinition.Columns.Add(new IndexColumnDefinition 
                { 
                    ColumnName = $"COLUMN{i}", 
                    Position = i 
                });
            }

            return indexDefinition;
        }
    }
}