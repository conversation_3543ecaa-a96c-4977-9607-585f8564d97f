using OracleMS.Models;
using System.Collections.Generic;
using System.Linq;
using Xunit;

namespace OracleMS.Tests.Models
{
    public class IndexDefinitionExtensionTests
    {
        [Fact]
        public void IndexDefinition_AvailableColumns_DefaultIsEmpty()
        {
            // Arrange & Act
            var indexDefinition = new IndexDefinition();

            // Assert
            Assert.NotNull(indexDefinition.AvailableColumns);
            Assert.Equal(0, indexDefinition.AvailableColumns.Count);
        }

        [Fact]
        public void IndexDefinition_AvailableColumns_CanSetAndGet()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            var expectedColumns = new List<string> { "ID", "NAME", "EMAIL", "CREATED_DATE" };

            // Act
            indexDefinition.AvailableColumns = expectedColumns;

            // Assert
            Assert.Equal(expectedColumns, indexDefinition.AvailableColumns);
            Assert.Equal(4, indexDefinition.AvailableColumns.Count);
        }

        [Fact]
        public void UpdateColumnsFromSelection_EmptySelection_ClearsColumns()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            indexDefinition.Columns.Add(new IndexColumnDefinition { ColumnName = "ID", Position = 1 });
            indexDefinition.Columns.Add(new IndexColumnDefinition { ColumnName = "NAME", Position = 2 });

            // Act
            indexDefinition.UpdateColumnsFromSelection(new List<string>());

            // Assert
            Assert.Equal(0, indexDefinition.Columns.Count);
        }

        [Fact]
        public void UpdateColumnsFromSelection_SingleColumn_CreatesCorrectDefinition()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            var selectedColumns = new List<string> { "ID" };

            // Act
            indexDefinition.UpdateColumnsFromSelection(selectedColumns);

            // Assert
            Assert.Equal(1, indexDefinition.Columns.Count);
            Assert.Equal("ID", indexDefinition.Columns[0].ColumnName);
            Assert.Equal(1, indexDefinition.Columns[0].Position);
            Assert.False(indexDefinition.Columns[0].IsDescending);
        }

        [Fact]
        public void UpdateColumnsFromSelection_MultipleColumns_CreatesCorrectDefinitions()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            var selectedColumns = new List<string> { "ID", "NAME", "EMAIL" };

            // Act
            indexDefinition.UpdateColumnsFromSelection(selectedColumns);

            // Assert
            Assert.Equal(3, indexDefinition.Columns.Count);
            
            // Check first column
            Assert.Equal("ID", indexDefinition.Columns[0].ColumnName);
            Assert.Equal(1, indexDefinition.Columns[0].Position);
            Assert.False(indexDefinition.Columns[0].IsDescending);
            
            // Check second column
            Assert.Equal("NAME", indexDefinition.Columns[1].ColumnName);
            Assert.Equal(2, indexDefinition.Columns[1].Position);
            Assert.False(indexDefinition.Columns[1].IsDescending);
            
            // Check third column
            Assert.Equal("EMAIL", indexDefinition.Columns[2].ColumnName);
            Assert.Equal(3, indexDefinition.Columns[2].Position);
            Assert.False(indexDefinition.Columns[2].IsDescending);
        }

        [Fact]
        public void UpdateColumnsFromSelection_ReplacesExistingColumns()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            // Add initial columns
            indexDefinition.Columns.Add(new IndexColumnDefinition { ColumnName = "OLD_COLUMN1", Position = 1 });
            indexDefinition.Columns.Add(new IndexColumnDefinition { ColumnName = "OLD_COLUMN2", Position = 2 });
            
            var newSelectedColumns = new List<string> { "NEW_COLUMN1", "NEW_COLUMN2", "NEW_COLUMN3" };

            // Act
            indexDefinition.UpdateColumnsFromSelection(newSelectedColumns);

            // Assert
            Assert.Equal(3, indexDefinition.Columns.Count);
            Assert.Equal("NEW_COLUMN1", indexDefinition.Columns[0].ColumnName);
            Assert.Equal("NEW_COLUMN2", indexDefinition.Columns[1].ColumnName);
            Assert.Equal("NEW_COLUMN3", indexDefinition.Columns[2].ColumnName);
            
            // Verify positions are correct
            Assert.Equal(1, indexDefinition.Columns[0].Position);
            Assert.Equal(2, indexDefinition.Columns[1].Position);
            Assert.Equal(3, indexDefinition.Columns[2].Position);
        }

        [Fact]
        public void UpdateColumnsFromSelection_PreservesColumnOrder()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            var selectedColumns = new List<string> { "LAST_NAME", "FIRST_NAME", "MIDDLE_NAME", "ID" };

            // Act
            indexDefinition.UpdateColumnsFromSelection(selectedColumns);

            // Assert
            Assert.Equal(4, indexDefinition.Columns.Count);
            Assert.Equal("LAST_NAME", indexDefinition.Columns[0].ColumnName);
            Assert.Equal("FIRST_NAME", indexDefinition.Columns[1].ColumnName);
            Assert.Equal("MIDDLE_NAME", indexDefinition.Columns[2].ColumnName);
            Assert.Equal("ID", indexDefinition.Columns[3].ColumnName);
        }

        [Fact]
        public void UpdateColumnsFromSelection_AllColumnsDefaultToAscending()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            var selectedColumns = new List<string> { "COL1", "COL2", "COL3" };

            // Act
            indexDefinition.UpdateColumnsFromSelection(selectedColumns);

            // Assert
            Assert.True(indexDefinition.Columns.All(c => !c.IsDescending));
        }

        [Fact]
        public void UpdateColumnsFromSelection_WithIEnumerable_WorksCorrectly()
        {
            // Arrange
            var indexDefinition = new IndexDefinition();
            var selectedColumns = new string[] { "A", "B", "C" }.Where(x => x != "D"); // IEnumerable<string>

            // Act
            indexDefinition.UpdateColumnsFromSelection(selectedColumns);

            // Assert
            Assert.Equal(3, indexDefinition.Columns.Count);
            Assert.Equal("A", indexDefinition.Columns[0].ColumnName);
            Assert.Equal("B", indexDefinition.Columns[1].ColumnName);
            Assert.Equal("C", indexDefinition.Columns[2].ColumnName);
        }
    }
}