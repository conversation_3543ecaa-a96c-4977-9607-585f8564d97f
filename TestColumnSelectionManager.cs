using OracleMS.Models;
using System;

namespace OracleMS
{
    /// <summary>
    /// Simple test runner for ColumnSelectionManager to verify functionality
    /// </summary>
    public class TestColumnSelectionManager
    {
        public static void RunTests()
        {
            Console.WriteLine("Testing ColumnSelectionManager...");
            
            // Test 1: Constructor
            var manager = new ColumnSelectionManager();
            Console.WriteLine($"✓ Constructor: AvailableColumns.Count = {manager.AvailableColumns.Count}, SelectedColumns.Count = {manager.SelectedColumns.Count}");
            
            // Test 2: Add columns to available
            manager.AvailableColumns.Add("COLUMN1");
            manager.AvailableColumns.Add("COLUMN2");
            manager.AvailableColumns.Add("COLUMN3");
            Console.WriteLine($"✓ Added columns: AvailableColumns.Count = {manager.AvailableColumns.Count}");
            
            // Test 3: Move to selected
            manager.MoveToSelected("COLUMN1");
            manager.MoveToSelected("COLUMN3");
            Console.WriteLine($"✓ Move to selected: AvailableColumns.Count = {manager.AvailableColumns.Count}, SelectedColumns.Count = {manager.SelectedColumns.Count}");
            Console.WriteLine($"  Selected: {string.Join(", ", manager.SelectedColumns)}");
            
            // Test 4: Move up/down
            manager.MoveDown("COLUMN1"); // Move COLUMN1 down
            Console.WriteLine($"✓ Move down: Selected order: {string.Join(", ", manager.SelectedColumns)}");
            
            manager.MoveUp("COLUMN1"); // Move COLUMN1 back up
            Console.WriteLine($"✓ Move up: Selected order: {string.Join(", ", manager.SelectedColumns)}");
            
            // Test 5: Move back to available
            manager.MoveToAvailable("COLUMN3");
            Console.WriteLine($"✓ Move to available: AvailableColumns.Count = {manager.AvailableColumns.Count}, SelectedColumns.Count = {manager.SelectedColumns.Count}");
            Console.WriteLine($"  Available: {string.Join(", ", manager.AvailableColumns)}");
            Console.WriteLine($"  Selected: {string.Join(", ", manager.SelectedColumns)}");
            
            // Test 6: LoadTableColumns
            manager.LoadTableColumns("TEST_SCHEMA", "TEST_TABLE");
            Console.WriteLine($"✓ LoadTableColumns: AvailableColumns.Count = {manager.AvailableColumns.Count}, SelectedColumns.Count = {manager.SelectedColumns.Count}");
            
            Console.WriteLine("All tests passed!");
        }
    }
}