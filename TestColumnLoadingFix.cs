using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OracleMS.ViewModels;
using OracleMS.Models;

namespace OracleMS
{
    /// <summary>
    /// 測試 Column 載入修復的邏輯
    /// </summary>
    public class TestColumnLoadingFix
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== Column 載入修復測試 ===");
                Console.WriteLine();

                await TestColumnLoadingLogic();

                Console.WriteLine();
                Console.WriteLine("測試完成！按任意鍵結束...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"測試過程中發生錯誤: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
                Console.ReadKey();
            }
        }

        private static async Task TestColumnLoadingLogic()
        {
            Console.WriteLine("1. 測試 UpdateColumnsUI 方法的修改");
            Console.WriteLine("   - 檢查編輯模式下是否會正確恢復索引欄位");
            Console.WriteLine("   - 檢查創建模式下是否會正確恢復預設欄位");
            Console.WriteLine();

            // 模擬編輯模式的情況
            Console.WriteLine("=== 編輯模式測試 ===");
            
            var mockIndexDefinition = new IndexDefinition
            {
                Name = "PK_EMPLOYEES",
                TableName = "EMPLOYEES",
                Owner = "HR",
                Type = IndexType.Normal,
                IsUnique = true
            };

            // 模擬索引欄位
            mockIndexDefinition.Columns.Add(new IndexColumnDefinition
            {
                ColumnName = "EMPLOYEE_ID",
                Position = 1,
                IsDescending = false
            });
            mockIndexDefinition.Columns.Add(new IndexColumnDefinition
            {
                ColumnName = "DEPARTMENT_ID",
                Position = 2,
                IsDescending = false
            });

            var availableColumns = new[] { 
                "EMPLOYEE_ID", "FIRST_NAME", "LAST_NAME", "EMAIL", 
                "PHONE_NUMBER", "HIRE_DATE", "JOB_ID", "SALARY", 
                "COMMISSION_PCT", "MANAGER_ID", "DEPARTMENT_ID" 
            };
            
            Console.WriteLine($"模擬索引定義: {mockIndexDefinition.Name}");
            Console.WriteLine($"索引欄位數量: {mockIndexDefinition.Columns.Count}");
            Console.WriteLine($"索引欄位: [{string.Join(", ", mockIndexDefinition.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName))}]");
            Console.WriteLine($"可用 Columns: [{string.Join(", ", availableColumns)}]");
            
            // 檢查索引欄位是否都在可用清單中
            var indexColumns = mockIndexDefinition.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName);
            bool allColumnsAvailable = indexColumns.All(col => availableColumns.Contains(col));
            
            Console.WriteLine($"所有索引欄位都在可用清單中: {(allColumnsAvailable ? "✓ 是" : "✗ 否")}");
            
            if (allColumnsAvailable)
            {
                Console.WriteLine("✓ 編輯模式：會將索引欄位移到 SelectedColumns 清單");
                Console.WriteLine($"  - 預期 AvailableColumns 數量: {availableColumns.Length - mockIndexDefinition.Columns.Count}");
                Console.WriteLine($"  - 預期 SelectedColumns 數量: {mockIndexDefinition.Columns.Count}");
                Console.WriteLine($"  - 預期 SelectedColumns 順序: [{string.Join(", ", indexColumns)}]");
            }
            else
            {
                var missingColumns = indexColumns.Where(col => !availableColumns.Contains(col));
                Console.WriteLine($"⚠ 編輯模式：部分索引欄位不在可用清單中: [{string.Join(", ", missingColumns)}]");
            }

            Console.WriteLine();
            Console.WriteLine("=== 創建模式測試 ===");
            
            var mockIndexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "EMPLOYEES",
                IsEditMode = false,
                Columns = new List<string> { "EMPLOYEE_ID", "LAST_NAME" }
            };
            
            Console.WriteLine($"模擬 IndexEditorInfo: Schema={mockIndexInfo.Schema}, Table={mockIndexInfo.TableName}");
            Console.WriteLine($"預設欄位數量: {mockIndexInfo.Columns?.Count ?? 0}");
            Console.WriteLine($"預設欄位: [{string.Join(", ", mockIndexInfo.Columns ?? new List<string>())}]");
            Console.WriteLine($"可用 Columns: [{string.Join(", ", availableColumns)}]");
            
            bool createModeColumnsAvailable = mockIndexInfo.Columns?.All(col => availableColumns.Contains(col)) ?? false;
            Console.WriteLine($"所有預設欄位都在可用清單中: {(createModeColumnsAvailable ? "✓ 是" : "✗ 否")}");
            
            if (createModeColumnsAvailable)
            {
                Console.WriteLine("✓ 創建模式：會將預設欄位移到 SelectedColumns 清單");
                Console.WriteLine($"  - 預期 AvailableColumns 數量: {availableColumns.Length - (mockIndexInfo.Columns?.Count ?? 0)}");
                Console.WriteLine($"  - 預期 SelectedColumns 數量: {mockIndexInfo.Columns?.Count ?? 0}");
                Console.WriteLine($"  - 預期 SelectedColumns 順序: [{string.Join(", ", mockIndexInfo.Columns ?? new List<string>())}]");
            }
            else
            {
                Console.WriteLine("⚠ 創建模式：部分預設欄位不在可用清單中");
            }

            Console.WriteLine();
            Console.WriteLine("=== 修改內容總結 ===");
            Console.WriteLine("1. UpdateColumnsUI 方法的關鍵修改:");
            Console.WriteLine("   - 編輯模式：使用 IndexDefinition.Columns 而不是 _indexInfo.Columns");
            Console.WriteLine("   - 創建模式：仍然使用 _indexInfo.Columns");
            Console.WriteLine("   - 按照 Position 順序恢復已選欄位");
            Console.WriteLine();
            Console.WriteLine("2. 新增了詳細的日誌記錄:");
            Console.WriteLine("   - 記錄可用 Columns 清單");
            Console.WriteLine("   - 記錄欄位移動的過程");
            Console.WriteLine("   - 記錄警告訊息");
            Console.WriteLine();
            Console.WriteLine("3. 修改前的問題:");
            Console.WriteLine("   - 編輯模式下使用 _indexInfo.Columns（通常為空）");
            Console.WriteLine("   - 導致索引欄位無法正確恢復到 SelectedColumns");
            Console.WriteLine();
            Console.WriteLine("4. 修改後的效果:");
            Console.WriteLine("   - 編輯模式下使用 IndexDefinition.Columns（從資料庫載入）");
            Console.WriteLine("   - 索引欄位會正確顯示在 SelectedColumns 中");
            Console.WriteLine("   - 剩餘欄位會顯示在 AvailableColumns 中");

            Console.WriteLine();
            Console.WriteLine("=== 預期效果 ===");
            Console.WriteLine("修改後，編輯模式下的 IndexEditorView 應該能夠:");
            Console.WriteLine("✅ Schema 下拉選單自動選取索引所屬的 Schema");
            Console.WriteLine("✅ Table 下拉選單自動載入並選取索引所屬的 Table");
            Console.WriteLine("✅ AvailableColumns 顯示表格的所有欄位（除了已選的）");
            Console.WriteLine("✅ SelectedColumns 顯示索引的欄位（按正確順序）");
            Console.WriteLine("✅ 用戶可以立即看到完整的索引資訊並進行編輯");

            Console.WriteLine();
            Console.WriteLine("🎉 Column 載入修復邏輯測試完成！");
            Console.WriteLine("建議現在測試實際的應用程式來驗證修復效果。");
        }
    }
}
