using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Data;
using OracleMS.Interfaces;

namespace OracleMS.Services
{
    /// <summary>
    /// Handler for WPF binding errors
    /// </summary>
    public class BindingErrorHandler
    {
        private static IBindingErrorLogger _logger;

        /// <summary>
        /// Initialize the binding error handler with a logger
        /// </summary>
        /// <param name="logger">The binding error logger to use</param>
        public static void Initialize(IBindingErrorLogger logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            
            // Register to handle binding errors application-wide
            PresentationTraceSources.DataBindingSource.Switch.Level = SourceLevels.Error;
            PresentationTraceSources.DataBindingSource.Listeners.Add(new BindingErrorTraceListener());
        }

        /// <summary>
        /// Log a binding error
        /// </summary>
        /// <param name="source">The source object where the binding error occurred</param>
        /// <param name="errorMessage">The error message</param>
        public static void LogBindingError(object source, string errorMessage)
        {
            _logger?.LogBindingError(source, errorMessage);
        }

        /// <summary>
        /// Log a binding error with exception details
        /// </summary>
        /// <param name="source">The source object where the binding error occurred</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="exception">The exception that occurred</param>
        public static void LogBindingError(object source, string errorMessage, Exception exception)
        {
            _logger?.LogBindingError(source, errorMessage, exception);
        }

        /// <summary>
        /// Log a binding success
        /// </summary>
        /// <param name="source">The source object where the binding succeeded</param>
        /// <param name="message">The success message</param>
        public static void LogBindingSuccess(object source, string message)
        {
            _logger?.LogBindingSuccess(source, message);
        }

        /// <summary>
        /// Trace listener for binding errors
        /// </summary>
        private class BindingErrorTraceListener : TraceListener
        {
            public override void Write(string message)
            {
                // Not used
            }

            public override void WriteLine(string message)
            {
                if (message != null && message.Contains("BindingExpression:"))
                {
                    _logger?.LogBindingError(this, $"WPF Binding Error: {message}");
                }
            }
        }
    }
}