using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;

namespace OracleMS.Models
{
    /// <summary>
    /// 資料表定義模型
    /// </summary>
    public class TableDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private string _comments = string.Empty;
        private ObservableCollection<ColumnDefinition> _columns = new();
        private ObservableCollection<IndexDefinition> _indexes = new();
        private ObservableCollection<ConstraintDefinition> _constraints = new();
        private ObservableCollection<TriggerDefinition> _triggers = new();

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料表擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 資料表註解
        /// </summary>
        public string Comments
        {
            get => _comments;
            set => SetProperty(ref _comments, value);
        }

        /// <summary>
        /// 資料表欄位集合
        /// </summary>
        public ObservableCollection<ColumnDefinition> Columns
        {
            get => _columns;
            set => SetProperty(ref _columns, value);
        }

        /// <summary>
        /// 資料表索引集合
        /// </summary>
        public ObservableCollection<IndexDefinition> Indexes
        {
            get => _indexes;
            set => SetProperty(ref _indexes, value);
        }

        /// <summary>
        /// 資料表約束條件集合
        /// </summary>
        public ObservableCollection<ConstraintDefinition> Constraints
        {
            get => _constraints;
            set => SetProperty(ref _constraints, value);
        }

        /// <summary>
        /// 資料表觸發器集合
        /// </summary>
        public ObservableCollection<TriggerDefinition> Triggers
        {
            get => _triggers;
            set => SetProperty(ref _triggers, value);
        }

        /// <summary>
        /// 驗證資料表定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (Columns.Count == 0)
            {
                result.AddError("資料表必須至少有一個欄位");
            }

            foreach (var column in Columns)
            {
                var columnValidation = column.Validate();
                if (!columnValidation.IsValid)
                {
                    result.AddErrors(columnValidation.Errors);
                }
            }

            return result;
        }
    }

    /// <summary>
    /// 欄位定義模型
    /// </summary>
    public class ColumnDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _dataType = string.Empty;
        private int? _length;
        private int? _precision;
        private int? _scale;
        private bool _isNullable;
        private string _defaultValue = string.Empty;
        private string _comments = string.Empty;

        /// <summary>
        /// 欄位名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料類型
        /// </summary>
        public string DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>
        /// 長度 (適用於 VARCHAR2, CHAR 等)
        /// </summary>
        public int? Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        /// <summary>
        /// 精度 (適用於 NUMBER)
        /// </summary>
        public int? Precision
        {
            get => _precision;
            set => SetProperty(ref _precision, value);
        }

        /// <summary>
        /// 小數位數 (適用於 NUMBER)
        /// </summary>
        public int? Scale
        {
            get => _scale;
            set => SetProperty(ref _scale, value);
        }

        /// <summary>
        /// 是否允許 NULL
        /// </summary>
        public bool IsNullable
        {
            get => _isNullable;
            set => SetProperty(ref _isNullable, value);
        }

        /// <summary>
        /// 預設值
        /// </summary>
        public string DefaultValue
        {
            get => _defaultValue;
            set => SetProperty(ref _defaultValue, value);
        }

        /// <summary>
        /// 欄位註解
        /// </summary>
        public string Comments
        {
            get => _comments;
            set => SetProperty(ref _comments, value);
        }

        /// <summary>
        /// 驗證欄位定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError($"欄位名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(DataType))
            {
                result.AddError($"欄位 {Name} 的資料類型不能為空");
            }

            return result;
        }

        /// <summary>
        /// 取得完整的資料類型定義，包含長度、精度和小數位數
        /// </summary>
        /// <returns>完整的資料類型定義</returns>
        public string GetFullDataType()
        {
            if (string.IsNullOrWhiteSpace(DataType))
                return string.Empty;

            var dataTypeUpper = DataType.ToUpper();

            if ((dataTypeUpper == "VARCHAR2" || dataTypeUpper == "CHAR" || dataTypeUpper == "NVARCHAR2" || dataTypeUpper == "NCHAR") && Length.HasValue)
            {
                return $"{dataTypeUpper}({Length})";
            }
            else if (dataTypeUpper == "NUMBER" && Precision.HasValue)
            {
                if (Scale.HasValue && Scale.Value > 0)
                {
                    return $"{dataTypeUpper}({Precision},{Scale})";
                }
                else
                {
                    return $"{dataTypeUpper}({Precision})";
                }
            }
            else
            {
                return dataTypeUpper;
            }
        }
    }

    /// <summary>
    /// 約束條件定義模型
    /// </summary>
    public class ConstraintDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private ConstraintType _type;
        private string _tableName = string.Empty;
        private List<string> _columns = new();
        private string _referencedTable = string.Empty;
        private List<string> _referencedColumns = new();
        private string _checkCondition = string.Empty;
        private bool _isEnabled = true;

        /// <summary>
        /// 約束條件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 約束條件類型
        /// </summary>
        public ConstraintType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName
        {
            get => _tableName;
            set => SetProperty(ref _tableName, value);
        }

        /// <summary>
        /// 約束條件涉及的欄位
        /// </summary>
        public List<string> Columns
        {
            get => _columns;
            set => SetProperty(ref _columns, value);
        }

        /// <summary>
        /// 參考的資料表 (適用於外鍵)
        /// </summary>
        public string ReferencedTable
        {
            get => _referencedTable;
            set => SetProperty(ref _referencedTable, value);
        }

        /// <summary>
        /// 參考的欄位 (適用於外鍵)
        /// </summary>
        public List<string> ReferencedColumns
        {
            get => _referencedColumns;
            set => SetProperty(ref _referencedColumns, value);
        }

        /// <summary>
        /// 檢查條件 (適用於 CHECK 約束)
        /// </summary>
        public string CheckCondition
        {
            get => _checkCondition;
            set => SetProperty(ref _checkCondition, value);
        }

        /// <summary>
        /// 約束條件是否啟用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 驗證約束條件定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("約束條件名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (Type == ConstraintType.PrimaryKey || Type == ConstraintType.Unique || Type == ConstraintType.ForeignKey)
            {
                if (Columns.Count == 0)
                {
                    result.AddError($"約束條件 {Name} 必須指定至少一個欄位");
                }
            }

            if (Type == ConstraintType.ForeignKey)
            {
                if (string.IsNullOrWhiteSpace(ReferencedTable))
                {
                    result.AddError($"外鍵約束條件 {Name} 必須指定參考的資料表");
                }

                if (ReferencedColumns.Count == 0)
                {
                    result.AddError($"外鍵約束條件 {Name} 必須指定參考的欄位");
                }

                if (Columns.Count != ReferencedColumns.Count)
                {
                    result.AddError($"外鍵約束條件 {Name} 的欄位數量必須與參考欄位數量相同");
                }
            }

            if (Type == ConstraintType.Check && string.IsNullOrWhiteSpace(CheckCondition))
            {
                result.AddError($"檢查約束條件 {Name} 必須指定檢查條件");
            }

            return result;
        }
    }

    /// <summary>
    /// 約束條件類型
    /// </summary>
    public enum ConstraintType
    {
        PrimaryKey,
        Unique,
        ForeignKey,
        Check
    }

    /// <summary>
    /// 索引定義模型
    /// </summary>
    public class IndexDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _tableName = string.Empty;
        private string _owner = string.Empty;
        private IndexType _type;
        private List<IndexColumnDefinition> _columns = new();
        private bool _isUnique;
        private string _tablespace = string.Empty;
        private string _status = string.Empty;
        private DateTime _lastAnalyzed;
        private int _distinctKeys;
        private int _leafBlocks;
        private int _clustering;
        private List<string> _availableColumns = new();

        /// <summary>
        /// 索引名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName
        {
            get => _tableName;
            set => SetProperty(ref _tableName, value);
        }

        /// <summary>
        /// 索引擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 索引類型
        /// </summary>
        public IndexType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 索引欄位集合
        /// </summary>
        public List<IndexColumnDefinition> Columns
        {
            get => _columns;
            set => SetProperty(ref _columns, value);
        }

        /// <summary>
        /// 是否為唯一索引
        /// </summary>
        public bool IsUnique
        {
            get => _isUnique;
            set => SetProperty(ref _isUnique, value);
        }

        /// <summary>
        /// 表空間名稱
        /// </summary>
        public string Tablespace
        {
            get => _tablespace;
            set => SetProperty(ref _tablespace, value);
        }

        /// <summary>
        /// 索引狀態
        /// </summary>
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 最後分析時間
        /// </summary>
        public DateTime LastAnalyzed
        {
            get => _lastAnalyzed;
            set => SetProperty(ref _lastAnalyzed, value);
        }

        /// <summary>
        /// 不同鍵值數量
        /// </summary>
        public int DistinctKeys
        {
            get => _distinctKeys;
            set => SetProperty(ref _distinctKeys, value);
        }

        /// <summary>
        /// 葉節點數量
        /// </summary>
        public int LeafBlocks
        {
            get => _leafBlocks;
            set => SetProperty(ref _leafBlocks, value);
        }

        /// <summary>
        /// 叢集因子
        /// </summary>
        public int Clustering
        {
            get => _clustering;
            set => SetProperty(ref _clustering, value);
        }

        /// <summary>
        /// 可用欄位集合 (用於索引編輯器的欄位選擇)
        /// </summary>
        public List<string> AvailableColumns
        {
            get => _availableColumns;
            set => SetProperty(ref _availableColumns, value);
        }

        /// <summary>
        /// 驗證索引定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            return Validate(ValidationMode.Standard);
        }

        /// <summary>
        /// 驗證索引定義是否有效（支援不同驗證模式）
        /// </summary>
        /// <param name="mode">驗證模式</param>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate(ValidationMode mode)
        {
            var result = new ValidationResult();

            // 基本驗證
            ValidateBasicProperties(result);

            // 欄位驗證
            ValidateColumns(result);

            // 根據模式進行額外驗證
            switch (mode)
            {
                case ValidationMode.Create:
                    ValidateForCreation(result);
                    break;
                case ValidationMode.Edit:
                    ValidateForEdit(result);
                    break;
                case ValidationMode.Standard:
                default:
                    // 標準驗證已在基本驗證中完成
                    break;
            }

            return result;
        }

        /// <summary>
        /// 驗證基本屬性
        /// </summary>
        /// <param name="result">驗證結果</param>
        private void ValidateBasicProperties(ValidationResult result)
        {
            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError(IndexValidationMessages.IndexNameRequired, ValidationErrorType.Required, "Name");
            }
            else
            {
                // 驗證索引名稱格式
                if (!IsValidIndexName(Name))
                {
                    result.AddError(IndexValidationMessages.InvalidIndexNameFormat, ValidationErrorType.Format, "Name");
                }

                // 驗證索引名稱長度
                if (Name.Length > 30) // Oracle 索引名稱最大長度為 30
                {
                    result.AddError(IndexValidationMessages.IndexNameTooLong, ValidationErrorType.Length, "Name");
                }

                // 新增效能建議警告
                if (Name.Length < 3)
                {
                    result.AddWarning("建議索引名稱至少包含 3 個字元以提高可讀性", ValidationWarningType.BestPractice);
                }
            }

            if (string.IsNullOrWhiteSpace(Owner))
            {
                result.AddError(IndexValidationMessages.SchemaRequired, ValidationErrorType.Required, "Owner");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError(IndexValidationMessages.TableNameRequired, ValidationErrorType.Required, "TableName");
            }
        }

        /// <summary>
        /// 驗證欄位配置
        /// </summary>
        /// <param name="result">驗證結果</param>
        private void ValidateColumns(ValidationResult result)
        {
            if (Columns.Count == 0)
            {
                result.AddError(IndexValidationMessages.AtLeastOneColumnRequired, ValidationErrorType.Required, "Columns");
            }
            else
            {
                // 檢查重複欄位
                var columnNames = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
                foreach (var column in Columns)
                {
                    if (string.IsNullOrWhiteSpace(column.ColumnName))
                    {
                        result.AddError(IndexValidationMessages.ColumnNameRequired, ValidationErrorType.Required, "ColumnName");
                        continue;
                    }

                    if (!columnNames.Add(column.ColumnName))
                    {
                        result.AddError(string.Format(IndexValidationMessages.DuplicateColumn, column.ColumnName), 
                                      ValidationErrorType.Duplicate, "Columns");
                    }
                }

                // 檢查欄位數量限制（Oracle 索引最多支援 32 個欄位）
                if (Columns.Count > 32)
                {
                    result.AddError(IndexValidationMessages.TooManyColumns, ValidationErrorType.Range, "Columns");
                }

                // 效能警告：過多欄位可能影響效能
                if (Columns.Count > 5)
                {
                    result.AddWarning($"索引包含 {Columns.Count} 個欄位，可能會影響寫入效能", ValidationWarningType.Performance);
                }

                // 最佳實務警告：建議將選擇性高的欄位放在前面
                if (Columns.Count > 1)
                {
                    result.AddWarning("建議將選擇性高的欄位放在索引的前面位置以提高查詢效能", ValidationWarningType.BestPractice);
                }

                // 驗證欄位位置
                ValidateColumnPositions(result);
            }
        }

        /// <summary>
        /// 驗證欄位位置
        /// </summary>
        /// <param name="result">驗證結果</param>
        private void ValidateColumnPositions(ValidationResult result)
        {
            var positions = Columns.Select(c => c.Position).OrderBy(p => p).ToList();
            
            // 檢查位置是否從 1 開始且連續
            for (int i = 0; i < positions.Count; i++)
            {
                if (positions[i] != i + 1)
                {
                    result.AddError(IndexValidationMessages.InvalidColumnPositions, ValidationErrorType.Range, "Position");
                    break;
                }
            }
        }

        /// <summary>
        /// 驗證創建模式的特殊要求
        /// </summary>
        /// <param name="result">驗證結果</param>
        private void ValidateForCreation(ValidationResult result)
        {
            // 創建模式的額外驗證
            if (string.IsNullOrWhiteSpace(Owner))
            {
                result.AddError(IndexValidationMessages.SchemaRequiredForCreation, ValidationErrorType.Required, "Owner");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError(IndexValidationMessages.TableRequiredForCreation, ValidationErrorType.Required, "TableName");
            }

            // 檢查索引名稱是否符合命名規範
            if (!string.IsNullOrWhiteSpace(Name) && !IsValidIndexNameForCreation(Name))
            {
                result.AddError(IndexValidationMessages.InvalidIndexNameForCreation, ValidationErrorType.Format, "Name");
            }

            // 新增創建模式的最佳實務建議
            if (!string.IsNullOrWhiteSpace(Name))
            {
                // 建議使用有意義的索引名稱
                if (!Name.Contains("IDX") && !Name.Contains("IX"))
                {
                    result.AddWarning("建議在索引名稱中包含 'IDX' 或 'IX' 前綴以提高可識別性", ValidationWarningType.BestPractice);
                }

                // 建議包含表格名稱
                if (!string.IsNullOrWhiteSpace(TableName) && !Name.ToUpper().Contains(TableName.ToUpper()))
                {
                    result.AddWarning("建議在索引名稱中包含表格名稱以提高可維護性", ValidationWarningType.BestPractice);
                }
            }

            // 唯一索引的建議
            if (IsUnique && Columns.Count == 1)
            {
                result.AddWarning("單一欄位的唯一索引可能可以用主鍵或唯一約束條件替代", ValidationWarningType.BestPractice);
            }
        }

        /// <summary>
        /// 驗證編輯模式的特殊要求
        /// </summary>
        /// <param name="result">驗證結果</param>
        private void ValidateForEdit(ValidationResult result)
        {
            // 編輯模式的額外驗證
            if (Columns.Count == 0)
            {
                result.AddError(IndexValidationMessages.AtLeastOneColumnRequiredForEdit, ValidationErrorType.Required, "Columns");
            }

            // 檢查是否有可用欄位但未選擇任何欄位
            if (AvailableColumns.Count > 0 && Columns.Count == 0)
            {
                result.AddError(IndexValidationMessages.NoColumnsSelectedFromAvailable, ValidationErrorType.Required, "Columns");
            }

            // 編輯模式的建議
            if (AvailableColumns.Count > 0 && Columns.Count > 0)
            {
                // 檢查是否所有選擇的欄位都在可用欄位清單中
                var selectedColumnNames = Columns.Select(c => c.ColumnName).ToHashSet(StringComparer.OrdinalIgnoreCase);
                var availableColumnNames = AvailableColumns.ToHashSet(StringComparer.OrdinalIgnoreCase);
                
                var invalidColumns = selectedColumnNames.Except(availableColumnNames).ToList();
                if (invalidColumns.Any())
                {
                    foreach (var invalidColumn in invalidColumns)
                    {
                        result.AddError($"欄位 '{invalidColumn}' 不存在於資料表中", ValidationErrorType.Dependency, "Columns");
                    }
                }

                // 建議使用更多可用欄位來提高查詢效能
                if (Columns.Count == 1 && AvailableColumns.Count > 3)
                {
                    result.AddWarning("考慮新增更多欄位到索引中以支援更多查詢模式", ValidationWarningType.Performance);
                }
            }
        }

        /// <summary>
        /// 檢查索引名稱是否有效
        /// </summary>
        /// <param name="name">索引名稱</param>
        /// <returns>是否有效</returns>
        private bool IsValidIndexName(string name)
        {
            if (string.IsNullOrWhiteSpace(name))
                return false;

            // Oracle 識別符規則：以字母開頭，可包含字母、數字、底線、$、#
            return System.Text.RegularExpressions.Regex.IsMatch(name, @"^[A-Za-z][A-Za-z0-9_$#]*$");
        }

        /// <summary>
        /// 檢查索引名稱是否適合創建
        /// </summary>
        /// <param name="name">索引名稱</param>
        /// <returns>是否適合創建</returns>
        private bool IsValidIndexNameForCreation(string name)
        {
            if (!IsValidIndexName(name))
                return false;

            // 避免使用 Oracle 保留字
            var reservedWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
            {
                "INDEX", "TABLE", "SELECT", "INSERT", "UPDATE", "DELETE", "CREATE", "DROP", "ALTER",
                "USER", "SYSTEM", "DUAL", "ALL", "ANY", "SOME", "EXISTS", "IN", "LIKE", "BETWEEN"
            };

            return !reservedWords.Contains(name);
        }

        /// <summary>
        /// 從選擇的欄位名稱更新索引欄位定義
        /// </summary>
        /// <param name="selectedColumns">選擇的欄位名稱集合</param>
        public void UpdateColumnsFromSelection(IEnumerable<string> selectedColumns)
        {
            Columns.Clear();
            int position = 1;
            foreach (var columnName in selectedColumns)
            {
                Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = columnName,
                    Position = position++,
                    IsDescending = false
                });
            }
        }
    }

    /// <summary>
    /// 索引欄位定義
    /// </summary>
    public class IndexColumnDefinition : NotifyPropertyChangedBase
    {
        private string _columnName = string.Empty;
        private bool _isDescending;
        private int _position;

        /// <summary>
        /// 欄位名稱
        /// </summary>
        public string ColumnName
        {
            get => _columnName;
            set => SetProperty(ref _columnName, value);
        }

        /// <summary>
        /// 是否為遞減排序
        /// </summary>
        public bool IsDescending
        {
            get => _isDescending;
            set => SetProperty(ref _isDescending, value);
        }

        /// <summary>
        /// 欄位在索引中的位置
        /// </summary>
        public int Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }
    }

    /// <summary>
    /// 索引類型
    /// </summary>
    public enum IndexType
    {
        Normal,
        Bitmap,
        Function,
        Domain,
        Spatial
    }

    /// <summary>
    /// 驗證模式
    /// </summary>
    public enum ValidationMode
    {
        /// <summary>
        /// 標準驗證
        /// </summary>
        Standard,
        /// <summary>
        /// 創建模式驗證
        /// </summary>
        Create,
        /// <summary>
        /// 編輯模式驗證
        /// </summary>
        Edit
    }

    /// <summary>
    /// 觸發器定義模型
    /// </summary>
    public class TriggerDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _tableName = string.Empty;
        private string _owner = string.Empty;
        private TriggerType _type;
        private TriggerEvent _event;
        private TriggerTiming _timing;
        private string _condition = string.Empty;
        private string _body = string.Empty;
        private bool _isEnabled = true;
        private string _status = string.Empty;

        /// <summary>
        /// 觸發器名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string TableName
        {
            get => _tableName;
            set => SetProperty(ref _tableName, value);
        }

        /// <summary>
        /// 觸發器擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 觸發器類型
        /// </summary>
        public TriggerType Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 觸發事件
        /// </summary>
        public TriggerEvent Event
        {
            get => _event;
            set => SetProperty(ref _event, value);
        }

        /// <summary>
        /// 觸發時機
        /// </summary>
        public TriggerTiming Timing
        {
            get => _timing;
            set => SetProperty(ref _timing, value);
        }

        /// <summary>
        /// 觸發條件
        /// </summary>
        public string Condition
        {
            get => _condition;
            set => SetProperty(ref _condition, value);
        }

        /// <summary>
        /// 觸發器主體
        /// </summary>
        public string Body
        {
            get => _body;
            set => SetProperty(ref _body, value);
        }

        /// <summary>
        /// 觸發器是否啟用
        /// </summary>
        public bool IsEnabled
        {
            get => _isEnabled;
            set => SetProperty(ref _isEnabled, value);
        }

        /// <summary>
        /// 觸發器狀態
        /// </summary>
        public string Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        /// <summary>
        /// 驗證觸發器定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("觸發器名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(TableName))
            {
                result.AddError("資料表名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(Body))
            {
                result.AddError($"觸發器 {Name} 的主體不能為空");
            }

            return result;
        }
    }

    /// <summary>
    /// 觸發器類型
    /// </summary>
    public enum TriggerType
    {
        Row,
        Statement
    }

    /// <summary>
    /// 觸發事件
    /// </summary>
    public enum TriggerEvent
    {
        Insert,
        Update,
        Delete,
        InsertOrUpdate,
        InsertOrDelete,
        UpdateOrDelete,
        InsertOrUpdateOrDelete
    }

    /// <summary>
    /// 觸發時機
    /// </summary>
    public enum TriggerTiming
    {
        Before,
        After,
        Instead
    }

    /// <summary>
    /// 套件定義模型
    /// </summary>
    public class PackageDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private string _specification = string.Empty;
        private string _body = string.Empty;
        private DateTime _specCreated;
        private DateTime _bodyCreated;
        private string _specStatus = string.Empty;
        private string _bodyStatus = string.Empty;

        /// <summary>
        /// 套件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 套件擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 套件規格
        /// </summary>
        public string Specification
        {
            get => _specification;
            set => SetProperty(ref _specification, value);
        }

        /// <summary>
        /// 套件主體
        /// </summary>
        public string Body
        {
            get => _body;
            set => SetProperty(ref _body, value);
        }

        /// <summary>
        /// 套件規格創建時間
        /// </summary>
        public DateTime SpecCreated
        {
            get => _specCreated;
            set => SetProperty(ref _specCreated, value);
        }

        /// <summary>
        /// 套件主體創建時間
        /// </summary>
        public DateTime BodyCreated
        {
            get => _bodyCreated;
            set => SetProperty(ref _bodyCreated, value);
        }

        /// <summary>
        /// 套件規格狀態
        /// </summary>
        public string SpecStatus
        {
            get => _specStatus;
            set => SetProperty(ref _specStatus, value);
        }

        /// <summary>
        /// 套件主體狀態
        /// </summary>
        public string BodyStatus
        {
            get => _bodyStatus;
            set => SetProperty(ref _bodyStatus, value);
        }

        /// <summary>
        /// 驗證套件定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("套件名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(Specification))
            {
                result.AddError($"套件 {Name} 的規格不能為空");
            }

            return result;
        }
    }

    /// <summary>
    /// 序列定義模型
    /// </summary>
    public class SequenceDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _owner = string.Empty;
        private long _minValue = 1;
        private long _maxValue = long.MaxValue;
        private long _incrementBy = 1;
        private long _cacheSize = 20;
        private bool _isCycling;
        private bool _isOrdered;
        private long _lastNumber = 1;

        /// <summary>
        /// 序列名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 序列擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 最小值
        /// </summary>
        public long MinValue
        {
            get => _minValue;
            set => SetProperty(ref _minValue, value);
        }

        /// <summary>
        /// 最大值
        /// </summary>
        public long MaxValue
        {
            get => _maxValue;
            set => SetProperty(ref _maxValue, value);
        }

        /// <summary>
        /// 增量
        /// </summary>
        public long IncrementBy
        {
            get => _incrementBy;
            set => SetProperty(ref _incrementBy, value);
        }

        /// <summary>
        /// 快取大小
        /// </summary>
        public long CacheSize
        {
            get => _cacheSize;
            set => SetProperty(ref _cacheSize, value);
        }

        /// <summary>
        /// 是否循環
        /// </summary>
        public bool IsCycling
        {
            get => _isCycling;
            set => SetProperty(ref _isCycling, value);
        }

        /// <summary>
        /// 是否有序
        /// </summary>
        public bool IsOrdered
        {
            get => _isOrdered;
            set => SetProperty(ref _isOrdered, value);
        }

        /// <summary>
        /// 最後產生的數字
        /// </summary>
        public long LastNumber
        {
            get => _lastNumber;
            set => SetProperty(ref _lastNumber, value);
        }

        /// <summary>
        /// 驗證序列定義是否有效
        /// </summary>
        /// <returns>驗證結果</returns>
        public ValidationResult Validate()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(Name))
            {
                result.AddError("序列名稱不能為空");
            }

            if (MinValue >= MaxValue)
            {
                result.AddError($"序列 {Name} 的最小值必須小於最大值");
            }

            if (IncrementBy == 0)
            {
                result.AddError($"序列 {Name} 的增量不能為零");
            }

            if (CacheSize < 0)
            {
                result.AddError($"序列 {Name} 的快取大小不能為負數");
            }

            return result;
        }
    }

    /// <summary>
    /// 驗證結果模型
    /// </summary>
    public class ValidationResult
    {
        private List<ValidationError> _errors = new();
        private List<ValidationWarning> _warnings = new();

        /// <summary>
        /// 錯誤訊息集合
        /// </summary>
        public IReadOnlyList<string> Errors => _errors.Select(e => e.Message).ToList();

        /// <summary>
        /// 詳細錯誤資訊集合
        /// </summary>
        public IReadOnlyList<ValidationError> DetailedErrors => _errors;

        /// <summary>
        /// 警告訊息集合
        /// </summary>
        public IReadOnlyList<ValidationWarning> Warnings => _warnings;

        /// <summary>
        /// 是否有效（沒有錯誤）
        /// </summary>
        public bool IsValid => _errors.Count == 0;

        /// <summary>
        /// 是否有警告
        /// </summary>
        public bool HasWarnings => _warnings.Count > 0;

        /// <summary>
        /// 錯誤數量
        /// </summary>
        public int ErrorCount => _errors.Count;

        /// <summary>
        /// 警告數量
        /// </summary>
        public int WarningCount => _warnings.Count;

        /// <summary>
        /// 新增錯誤訊息
        /// </summary>
        /// <param name="error">錯誤訊息</param>
        public void AddError(string error)
        {
            _errors.Add(new ValidationError(error, ValidationErrorType.General));
        }

        /// <summary>
        /// 新增錯誤訊息（帶類型）
        /// </summary>
        /// <param name="error">錯誤訊息</param>
        /// <param name="errorType">錯誤類型</param>
        public void AddError(string error, ValidationErrorType errorType)
        {
            _errors.Add(new ValidationError(error, errorType));
        }

        /// <summary>
        /// 新增錯誤訊息（帶類型和欄位）
        /// </summary>
        /// <param name="error">錯誤訊息</param>
        /// <param name="errorType">錯誤類型</param>
        /// <param name="fieldName">相關欄位名稱</param>
        public void AddError(string error, ValidationErrorType errorType, string fieldName)
        {
            _errors.Add(new ValidationError(error, errorType, fieldName));
        }

        /// <summary>
        /// 新增多個錯誤訊息
        /// </summary>
        /// <param name="errors">錯誤訊息集合</param>
        public void AddErrors(IEnumerable<string> errors)
        {
            foreach (var error in errors)
            {
                AddError(error);
            }
        }

        /// <summary>
        /// 新增警告訊息
        /// </summary>
        /// <param name="warning">警告訊息</param>
        public void AddWarning(string warning)
        {
            _warnings.Add(new ValidationWarning(warning, ValidationWarningType.General));
        }

        /// <summary>
        /// 新增警告訊息（帶類型）
        /// </summary>
        /// <param name="warning">警告訊息</param>
        /// <param name="warningType">警告類型</param>
        public void AddWarning(string warning, ValidationWarningType warningType)
        {
            _warnings.Add(new ValidationWarning(warning, warningType));
        }

        /// <summary>
        /// 清除所有錯誤和警告
        /// </summary>
        public void Clear()
        {
            _errors.Clear();
            _warnings.Clear();
        }

        /// <summary>
        /// 取得格式化的錯誤摘要
        /// </summary>
        /// <returns>錯誤摘要</returns>
        public string GetErrorSummary()
        {
            if (IsValid && !HasWarnings)
                return "驗證通過";

            var summary = new System.Text.StringBuilder();
            
            if (_errors.Count > 0)
            {
                summary.AppendLine($"發現 {_errors.Count} 個錯誤：");
                foreach (var error in _errors)
                {
                    summary.AppendLine($"• {error.Message}");
                }
            }

            if (_warnings.Count > 0)
            {
                if (summary.Length > 0)
                    summary.AppendLine();
                
                summary.AppendLine($"發現 {_warnings.Count} 個警告：");
                foreach (var warning in _warnings)
                {
                    summary.AppendLine($"• {warning.Message}");
                }
            }

            return summary.ToString().TrimEnd();
        }
    }

    /// <summary>
    /// 驗證錯誤
    /// </summary>
    public class ValidationError
    {
        public ValidationError(string message, ValidationErrorType errorType, string? fieldName = null)
        {
            Message = message;
            ErrorType = errorType;
            FieldName = fieldName;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 錯誤類型
        /// </summary>
        public ValidationErrorType ErrorType { get; }

        /// <summary>
        /// 相關欄位名稱
        /// </summary>
        public string? FieldName { get; }

        /// <summary>
        /// 錯誤發生時間
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 驗證警告
    /// </summary>
    public class ValidationWarning
    {
        public ValidationWarning(string message, ValidationWarningType warningType, string? fieldName = null)
        {
            Message = message;
            WarningType = warningType;
            FieldName = fieldName;
            Timestamp = DateTime.Now;
        }

        /// <summary>
        /// 警告訊息
        /// </summary>
        public string Message { get; }

        /// <summary>
        /// 警告類型
        /// </summary>
        public ValidationWarningType WarningType { get; }

        /// <summary>
        /// 相關欄位名稱
        /// </summary>
        public string? FieldName { get; }

        /// <summary>
        /// 警告發生時間
        /// </summary>
        public DateTime Timestamp { get; }
    }

    /// <summary>
    /// 驗證錯誤類型
    /// </summary>
    public enum ValidationErrorType
    {
        /// <summary>
        /// 一般錯誤
        /// </summary>
        General,
        /// <summary>
        /// 必填欄位錯誤
        /// </summary>
        Required,
        /// <summary>
        /// 格式錯誤
        /// </summary>
        Format,
        /// <summary>
        /// 長度錯誤
        /// </summary>
        Length,
        /// <summary>
        /// 重複錯誤
        /// </summary>
        Duplicate,
        /// <summary>
        /// 範圍錯誤
        /// </summary>
        Range,
        /// <summary>
        /// 相依性錯誤
        /// </summary>
        Dependency,
        /// <summary>
        /// 權限錯誤
        /// </summary>
        Permission,
        /// <summary>
        /// 資料庫錯誤
        /// </summary>
        Database
    }

    /// <summary>
    /// 驗證警告類型
    /// </summary>
    public enum ValidationWarningType
    {
        /// <summary>
        /// 一般警告
        /// </summary>
        General,
        /// <summary>
        /// 效能警告
        /// </summary>
        Performance,
        /// <summary>
        /// 最佳實務警告
        /// </summary>
        BestPractice,
        /// <summary>
        /// 相容性警告
        /// </summary>
        Compatibility,
        /// <summary>
        /// 安全性警告
        /// </summary>
        Security
    }

    /// <summary>
    /// 屬性變更通知基底類別
    /// </summary>
    public abstract class NotifyPropertyChangedBase : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 設定屬性值並觸發屬性變更事件
        /// </summary>
        /// <typeparam name="T">屬性類型</typeparam>
        /// <param name="field">欄位參考</param>
        /// <param name="value">新值</param>
        /// <param name="propertyName">屬性名稱</param>
        /// <returns>值是否已變更</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = "")
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 觸發屬性變更事件
        /// </summary>
        /// <param name="propertyName">屬性名稱</param>
        protected void OnPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 編譯錯誤模型
    /// </summary>
    public class CompilationError : NotifyPropertyChangedBase
    {
        private int _lineNumber;
        private int _columnNumber;
        private string _message = string.Empty;
        private string _errorCode = string.Empty;

        /// <summary>
        /// 行號
        /// </summary>
        public int LineNumber
        {
            get => _lineNumber;
            set => SetProperty(ref _lineNumber, value);
        }

        /// <summary>
        /// 欄位號
        /// </summary>
        public int ColumnNumber
        {
            get => _columnNumber;
            set => SetProperty(ref _columnNumber, value);
        }

        /// <summary>
        /// 錯誤訊息
        /// </summary>
        public string Message
        {
            get => _message;
            set => SetProperty(ref _message, value);
        }

        /// <summary>
        /// 錯誤代碼
        /// </summary>
        public string ErrorCode
        {
            get => _errorCode;
            set => SetProperty(ref _errorCode, value);
        }
    }

    /// <summary>
    /// 索引驗證訊息類別（支援本地化）
    /// </summary>
    public static class IndexValidationMessages
    {
        // 基本驗證訊息
        public static string IndexNameRequired => GetLocalizedMessage("IndexNameRequired", "索引名稱不能為空");
        public static string SchemaRequired => GetLocalizedMessage("SchemaRequired", "Schema 不能為空");
        public static string TableNameRequired => GetLocalizedMessage("TableNameRequired", "資料表名稱不能為空");
        public static string AtLeastOneColumnRequired => GetLocalizedMessage("AtLeastOneColumnRequired", "索引必須指定至少一個欄位");
        
        // 格式驗證訊息
        public static string InvalidIndexNameFormat => GetLocalizedMessage("InvalidIndexNameFormat", "索引名稱格式無效，必須以字母開頭，只能包含字母、數字、底線、$ 和 # 符號");
        public static string IndexNameTooLong => GetLocalizedMessage("IndexNameTooLong", "索引名稱長度不能超過 30 個字元");
        public static string ColumnNameRequired => GetLocalizedMessage("ColumnNameRequired", "欄位名稱不能為空");
        public static string DuplicateColumn => GetLocalizedMessage("DuplicateColumn", "欄位 '{0}' 重複");
        public static string TooManyColumns => GetLocalizedMessage("TooManyColumns", "索引欄位數量不能超過 32 個");
        public static string InvalidColumnPositions => GetLocalizedMessage("InvalidColumnPositions", "欄位位置必須從 1 開始且連續");
        
        // 創建模式驗證訊息
        public static string SchemaRequiredForCreation => GetLocalizedMessage("SchemaRequiredForCreation", "創建索引時必須指定 Schema");
        public static string TableRequiredForCreation => GetLocalizedMessage("TableRequiredForCreation", "創建索引時必須指定資料表");
        public static string InvalidIndexNameForCreation => GetLocalizedMessage("InvalidIndexNameForCreation", "索引名稱不能使用 Oracle 保留字");
        
        // 編輯模式驗證訊息
        public static string AtLeastOneColumnRequiredForEdit => GetLocalizedMessage("AtLeastOneColumnRequiredForEdit", "編輯索引時必須選擇至少一個欄位");
        public static string NoColumnsSelectedFromAvailable => GetLocalizedMessage("NoColumnsSelectedFromAvailable", "請從可用欄位中選擇要包含在索引中的欄位");
        
        // 操作失敗訊息
        public static string IndexCreationFailed => GetLocalizedMessage("IndexCreationFailed", "索引創建失敗");
        public static string IndexUpdateFailed => GetLocalizedMessage("IndexUpdateFailed", "索引更新失敗");
        public static string IndexValidationFailed => GetLocalizedMessage("IndexValidationFailed", "索引驗證失敗");
        public static string DatabaseConnectionFailed => GetLocalizedMessage("DatabaseConnectionFailed", "無法連接到資料庫");
        public static string InsufficientPermissions => GetLocalizedMessage("InsufficientPermissions", "權限不足，無法執行此操作");
        public static string IndexAlreadyExists => GetLocalizedMessage("IndexAlreadyExists", "索引名稱已存在");
        public static string TableNotFound => GetLocalizedMessage("TableNotFound", "指定的資料表不存在");
        public static string ColumnNotFound => GetLocalizedMessage("ColumnNotFound", "指定的欄位不存在於資料表中");
        
        // 新增的增強錯誤訊息
        public static string TablespaceNotFound => GetLocalizedMessage("TablespaceNotFound", "指定的表空間不存在");
        public static string IndexTooLarge => GetLocalizedMessage("IndexTooLarge", "索引大小超過限制");
        public static string InvalidTablespacePermissions => GetLocalizedMessage("InvalidTablespacePermissions", "沒有在指定表空間中創建索引的權限");
        public static string ConcurrentModification => GetLocalizedMessage("ConcurrentModification", "索引正在被其他會話修改，請稍後再試");
        public static string SystemResourceExhausted => GetLocalizedMessage("SystemResourceExhausted", "系統資源不足，無法完成操作");
        public static string NetworkTimeout => GetLocalizedMessage("NetworkTimeout", "網路連線逾時，請檢查網路連線");
        public static string UnexpectedDatabaseError => GetLocalizedMessage("UnexpectedDatabaseError", "資料庫發生未預期的錯誤");
        
        // 即時驗證相關訊息
        public static string ValidationInProgress => GetLocalizedMessage("ValidationInProgress", "正在驗證索引設定...");
        public static string ValidationCompleted => GetLocalizedMessage("ValidationCompleted", "驗證完成");
        public static string ValidationFailed => GetLocalizedMessage("ValidationFailed", "驗證失敗");
        
        // 操作狀態訊息
        public static string CreatingIndex => GetLocalizedMessage("CreatingIndex", "正在創建索引...");
        public static string UpdatingIndex => GetLocalizedMessage("UpdatingIndex", "正在更新索引...");
        public static string IndexCreatedSuccessfully => GetLocalizedMessage("IndexCreatedSuccessfully", "索引創建成功");
        public static string IndexUpdatedSuccessfully => GetLocalizedMessage("IndexUpdatedSuccessfully", "索引更新成功");
        
        // 用戶指導訊息
        public static string SelectSchemaFirst => GetLocalizedMessage("SelectSchemaFirst", "請先選擇 Schema");
        public static string SelectTableFirst => GetLocalizedMessage("SelectTableFirst", "請先選擇資料表");
        public static string EnterIndexName => GetLocalizedMessage("EnterIndexName", "請輸入索引名稱");
        public static string SelectColumns => GetLocalizedMessage("SelectColumns", "請選擇要包含在索引中的欄位");
        
        /// <summary>
        /// 取得本地化訊息（支援參數格式化）
        /// </summary>
        /// <param name="key">訊息鍵值</param>
        /// <param name="defaultMessage">預設訊息</param>
        /// <param name="args">格式化參數</param>
        /// <returns>本地化訊息</returns>
        public static string GetFormattedMessage(string key, string defaultMessage, params object[] args)
        {
            var message = GetLocalizedMessage(key, defaultMessage);
            return args.Length > 0 ? string.Format(message, args) : message;
        }
        
        /// <summary>
        /// 取得本地化訊息（目前返回預設訊息，未來可擴展為真正的本地化支援）
        /// </summary>
        /// <param name="key">訊息鍵值</param>
        /// <param name="defaultMessage">預設訊息</param>
        /// <returns>本地化訊息</returns>
        private static string GetLocalizedMessage(string key, string defaultMessage)
        {
            // 目前直接返回預設訊息
            // 未來可以擴展為從資源檔或資料庫載入本地化訊息
            // 可以考慮使用 System.Resources.ResourceManager 或其他本地化框架
            return defaultMessage;
        }
    }

    /// <summary>
    /// 相依性資訊模型
    /// </summary>
    public class DependencyInfo : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _type = string.Empty;
        private string _owner = string.Empty;
        private string _dependencyType = string.Empty;
        private string _description = string.Empty;

        /// <summary>
        /// 物件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 物件類型
        /// </summary>
        public string Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 相依性類型
        /// </summary>
        public string DependencyType
        {
            get => _dependencyType;
            set => SetProperty(ref _dependencyType, value);
        }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }
    }
}