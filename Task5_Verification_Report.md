# Task 5 實作驗證報告：索引創建和更新命令

## 任務概述
實作 IndexEditorViewModel 中的 CreateIndexCommand 和 UpdateIndexCommand，包含索引創建邏輯、DDL生成、資料庫執行、索引更新邏輯以及驗證邏輯。

## 實作內容

### 1. 新增命令屬性
在 IndexEditorViewModel 中新增了以下命令屬性：
- `CreateIndexCommand` - 創建索引命令
- `UpdateIndexCommand` - 更新索引命令

### 2. 命令初始化
在建構函式中初始化命令：
```csharp
// 初始化索引操作命令
CreateIndexCommand = new AsyncRelayCommand(CreateIndexAsync, CanCreateIndex);
UpdateIndexCommand = new AsyncRelayCommand(UpdateIndexAsync, CanUpdateIndex);
```

### 3. 創建索引實作
實作了 `CreateIndexAsync` 方法：
- 驗證操作狀態和資料庫連線
- 驗證索引定義的有效性
- 更新索引定義的基本資訊（Schema、TableName）
- 調用服務層創建索引
- 處理錯誤和狀態更新

### 4. 更新索引實作
實作了 `UpdateIndexAsync` 方法：
- 驗證操作狀態和資料庫連線
- 驗證索引定義的有效性
- 調用服務層更新索引
- 處理錯誤和狀態更新

### 5. CanExecute 條件檢查
實作了命令的可執行條件：

#### CreateIndexCommand 可執行條件：
- 必須是創建模式 (`IsCreateMode`)
- 不在載入或操作進行中
- 有有效的資料庫連線
- 已選擇 Schema 和 Table
- 索引名稱不為空
- 至少選擇一個欄位

#### UpdateIndexCommand 可執行條件：
- 必須是編輯模式 (`IsEditMode`)
- 不在載入或操作進行中
- 有有效的資料庫連線
- 索引定義不為空
- 至少選擇一個欄位

### 6. 驗證邏輯
實作了兩個驗證方法：

#### ValidateIndexForCreation()
- 檢查 Schema、Table、索引名稱是否已填寫
- 檢查是否至少選擇一個欄位
- 使用 IndexDefinition.Validate() 進行額外驗證

#### ValidateIndexForUpdate()
- 檢查索引定義是否存在
- 檢查是否至少選擇一個欄位
- 使用 IndexDefinition.Validate() 進行額外驗證

### 7. 服務層擴展
在 IObjectEditorService 介面中新增了：
- `CreateIndexAsync(IDbConnection connection, IndexDefinition definition)`
- `UpdateIndexAsync(IDbConnection connection, IndexDefinition definition)`

在 ObjectEditorService 中實作了：
- 索引創建邏輯，包含存在性檢查和 DDL 生成
- 索引更新邏輯，採用先刪除後創建的方式
- 相關的輔助方法（IndexExistsAsync、DropIndexAsync、GenerateCreateIndexSql）

### 8. 命令狀態通知
重寫了 `NotifyCanExecuteChanged` 方法：
- 調用基類方法處理基本命令
- 通知索引編輯器特有的命令重新評估可執行狀態
- 在欄位選擇變更時自動觸發

### 9. 用戶體驗改進
- 在欄位操作方法中加入命令狀態通知
- 在屬性變更時觸發命令重新評估
- 提供清楚的錯誤訊息和狀態回饋

## 驗證結果

### 功能驗證
✅ CreateIndexCommand 已正確實作
✅ UpdateIndexCommand 已正確實作
✅ 驗證邏輯已實作並整合
✅ 服務層方法已新增
✅ 命令狀態管理已實作
✅ 錯誤處理已實作

### 需求對應
✅ 需求 3.2：實作即時驗證功能，在用戶輸入時顯示驗證結果
✅ 需求 3.3：實作索引創建和更新的 DDL 生成和執行邏輯
✅ 需求 4.3：實作操作失敗時的使用者友善錯誤提示

### 程式碼品質
✅ 遵循現有的程式碼風格和架構
✅ 適當的錯誤處理和日誌記錄
✅ 清楚的方法註解和文件
✅ 符合 MVVM 模式的設計

## 結論
Task 5 已成功實作，所有要求的功能都已完成：
1. 新增了 CreateIndexCommand 和 UpdateIndexCommand 到 IndexEditorViewModel
2. 實作了索引創建邏輯，包含 DDL 生成和資料庫執行
3. 實作了索引更新邏輯，支援修改現有索引的欄位配置
4. 新增了驗證邏輯確保所有必要欄位已填寫且有效

實作符合所有指定的需求，並保持了與現有程式碼的一致性和品質標準。