<UserControl x:Class="OracleMS.Views.SequenceEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:viewModels="clr-namespace:OracleMS.ViewModels"
             d:DataContext="{d:DesignInstance Type=viewModels:SequenceEditorViewModel}"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">

    <UserControl.Resources>
        <!-- Converter for boolean to modified text -->
        <local:BoolToModifiedConverter x:Key="BoolToModifiedConverter"/>

        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具列 -->
        <ToolBarTray Grid.Row="0" Background="Transparent">
            <ToolBar Background="Transparent" BorderThickness="0">
                <Button Command="{Binding SaveCommand}" ToolTip="儲存 (Ctrl+S)">
                    <StackPanel Orientation="Horizontal">
                        <Image Source="/OracleMS;component/Resources/save.png" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button>
                <Separator/>
                <Button Command="{Binding RefreshCommand}" ToolTip="重新載入">
                    <StackPanel Orientation="Horizontal">
                        <Image Source="/OracleMS;component/Resources/refresh.png" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="重新載入"/>
                    </StackPanel>
                </Button>
                <Separator/>
                <Button Command="{Binding GenerateScriptCommand}" ToolTip="產生 DDL 腳本">
                    <StackPanel Orientation="Horizontal">
                        <Image Source="/OracleMS;component/Resources/script.png" Width="16" Height="16" Margin="0,0,5,0"/>
                        <TextBlock Text="產生腳本"/>
                    </StackPanel>
                </Button>
            </ToolBar>
        </ToolBarTray>

        <!-- 主要內容區域 -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 序列屬性編輯表單 -->
            <GroupBox Grid.Column="0" Header="序列屬性" Margin="0,0,5,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="5">
                        <TextBlock Text="序列名稱:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.Name, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                        <TextBlock Text="擁有者:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.Owner, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                        <TextBlock Text="起始值:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.LastNumber, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                        <TextBlock Text="增量值:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.IncrementBy, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                        <TextBlock Text="最小值:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.MinValue, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                        <TextBlock Text="最大值:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.MaxValue, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                        <TextBlock Text="快取大小:" Margin="0,5,0,2"/>
                        <TextBox Text="{Binding SequenceDefinition.CacheSize, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>

                        <CheckBox Content="循環" IsChecked="{Binding SequenceDefinition.IsCycling, UpdateSourceTrigger=PropertyChanged}" Margin="0,5,0,10"/>
                        <CheckBox Content="排序" IsChecked="{Binding SequenceDefinition.IsOrdered, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>
                    </StackPanel>
                </ScrollViewer>
            </GroupBox>

            <!-- DDL 預覽區域 -->
            <GroupBox Grid.Column="1" Header="DDL 預覽" Margin="5,0,0,0">
                <TextBox Text="{Binding DdlPreview, Mode=OneWay}" 
                         IsReadOnly="True" 
                         FontFamily="Consolas" 
                         TextWrapping="Wrap"
                         VerticalScrollBarVisibility="Auto"
                         HorizontalScrollBarVisibility="Auto"
                         Background="#F5F5F5"/>
            </GroupBox>
        </Grid>

        <!-- 狀態列 -->
        <StatusBar Grid.Row="2" Background="#F0F0F0">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="狀態: "/>
                    <TextBlock Text="{Binding HasUnsavedChanges, Converter={StaticResource BoolToModifiedConverter}}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>