using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 預存程序編輯器 ViewModel
    /// </summary>
    public class ProcedureEditorViewModel : BaseObjectEditorViewModel
    {
        private string _procedureSource = string.Empty;
        private ObservableCollection<ParameterDefinition> _parameters = new();
        private ObservableCollection<DependencyInfo> _dependencies = new();
        private ObservableCollection<CompilationError> _compilationErrors = new();
        private string _compilationStatus = "尚未編譯";
        private bool _isCompilationSuccessful;
        private bool _hasCompilationErrors;
        private TimeSpan _lastCompilationTime;

        /// <summary>
        /// 預存程序原始碼
        /// </summary>
        public string ProcedureSource
        {
            get => _procedureSource;
            set
            {
                if (SetProperty(ref _procedureSource, value))
                {
                    if (!_isInitializing)
                    {
                        HasUnsavedChanges = true;
                    }
                }
            }
        }

        /// <summary>
        /// 參數資訊集合
        /// </summary>
        public ObservableCollection<ParameterDefinition> Parameters
        {
            get => _parameters;
            private set => SetProperty(ref _parameters, value);
        }

        /// <summary>
        /// 相依性資訊集合
        /// </summary>
        public ObservableCollection<DependencyInfo> Dependencies
        {
            get => _dependencies;
            private set => SetProperty(ref _dependencies, value);
        }

        /// <summary>
        /// 編譯錯誤集合
        /// </summary>
        public ObservableCollection<CompilationError> CompilationErrors
        {
            get => _compilationErrors;
            private set => SetProperty(ref _compilationErrors, value);
        }

        /// <summary>
        /// 編譯狀態
        /// </summary>
        public string CompilationStatus
        {
            get => _compilationStatus;
            private set => SetProperty(ref _compilationStatus, value);
        }

        /// <summary>
        /// 編譯是否成功
        /// </summary>
        public bool IsCompilationSuccessful
        {
            get => _isCompilationSuccessful;
            private set => SetProperty(ref _isCompilationSuccessful, value);
        }

        /// <summary>
        /// 是否有編譯錯誤
        /// </summary>
        public bool HasCompilationErrors
        {
            get => _hasCompilationErrors;
            private set => SetProperty(ref _hasCompilationErrors, value);
        }

        /// <summary>
        /// 最後編譯時間
        /// </summary>
        public TimeSpan LastCompilationTime
        {
            get => _lastCompilationTime;
            private set => SetProperty(ref _lastCompilationTime, value);
        }

        /// <summary>
        /// 編譯命令
        /// </summary>
        public ICommand CompileCommand { get; }

        /// <summary>
        /// 驗證命令
        /// </summary>
        public ICommand ValidateCommand { get; }

        /// <summary>
        /// 清除編譯結果命令
        /// </summary>
        public ICommand ClearCompilationResultsCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="procedureName">預存程序名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public ProcedureEditorViewModel(
            string procedureName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(procedureName, DatabaseObjectType.Procedure, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            // 初始化命令
            CompileCommand = new AsyncRelayCommand(OnCompileAsync, CanCompile);
            ValidateCommand = new AsyncRelayCommand(OnValidateAsync, CanValidate);
            ClearCompilationResultsCommand = new RelayCommand(OnClearCompilationResults, CanClearCompilationResults);
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 載入預存程序原始碼
            ProcedureSource = await _objectEditorService.GetProcedureSourceAsync(connection, ObjectName);

            // 載入參數資訊
            await LoadParametersAsync(connection);

            // 載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 編譯並儲存預存程序
            await _objectEditorService.CompileProcedureAsync(connection, ObjectName, ProcedureSource);

            // 重新載入參數資訊
            await LoadParametersAsync(connection);

            // 重新載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected override string GenerateScript()
        {
            return ProcedureSource;
        }

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(ProcedureSource))
            {
                result.AddError("預存程序原始碼不能為空");
            }
            else
            {
                var upperSource = ProcedureSource.Trim().ToUpper();
                if (!upperSource.StartsWith("CREATE OR REPLACE PROCEDURE") &&
                    !upperSource.StartsWith("CREATE PROCEDURE"))
                {
                    result.AddError("預存程序原始碼必須以 CREATE [OR REPLACE] PROCEDURE 開頭");
                }
            }

            return result;
        }

        /// <summary>
        /// 載入參數資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadParametersAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        ARGUMENT_NAME AS Name,
                        DATA_TYPE AS DataType,
                        IN_OUT AS Direction,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        DEFAULT_VALUE AS DefaultValue,
                        '' AS Comments
                    FROM 
                        ALL_ARGUMENTS
                    WHERE 
                        OBJECT_NAME = '{ObjectName}'
                        AND PACKAGE_NAME IS NULL
                        AND ARGUMENT_NAME IS NOT NULL
                    ORDER BY 
                        POSITION";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                Parameters.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        Parameters.Add(new ParameterDefinition
                        {
                            Name = row["Name"].ToString() ?? string.Empty,
                            DataType = row["DataType"].ToString() ?? string.Empty,
                            Direction = row["Direction"].ToString() ?? string.Empty,
                            Length = row["Length"] != DBNull.Value ? Convert.ToInt32(row["Length"]) : null,
                            Precision = row["Precision"] != DBNull.Value ? Convert.ToInt32(row["Precision"]) : null,
                            Scale = row["Scale"] != DBNull.Value ? Convert.ToInt32(row["Scale"]) : null,
                            DefaultValue = row["DefaultValue"]?.ToString() ?? string.Empty,
                            Comments = row["Comments"]?.ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入參數資訊失敗：{ex}");
            }
        }

        /// <summary>
        /// 載入相依性資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadDependenciesAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                        d.REFERENCED_TYPE AS Type,
                        d.REFERENCED_OWNER AS Owner,
                        'DEPENDS ON' AS DependencyType,
                        'This procedure depends on this object' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.NAME = '{ObjectName}'
                        AND d.TYPE = 'PROCEDURE'
                    UNION ALL
                    SELECT 
                        d.NAME AS Name,
                        d.TYPE AS Type,
                        d.OWNER AS Owner,
                        'DEPENDENT' AS DependencyType,
                        'This object depends on this procedure' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.REFERENCED_NAME = '{ObjectName}'
                        AND d.REFERENCED_TYPE = 'PROCEDURE'
                    ORDER BY 
                        DependencyType, Name";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                Dependencies.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        Dependencies.Add(new DependencyInfo
                        {
                            Name = row["Name"].ToString() ?? string.Empty,
                            Type = row["Type"].ToString() ?? string.Empty,
                            Owner = row["Owner"].ToString() ?? string.Empty,
                            DependencyType = row["DependencyType"].ToString() ?? string.Empty,
                            Description = row["Description"].ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入相依性資訊失敗：{ex}");
            }
        }

        /// <summary>
        /// 編譯預存程序
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnCompileAsync()
        {
            if (string.IsNullOrWhiteSpace(ProcedureSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                CompilationStatus = "編譯中";
                HasError = false;
                ErrorMessage = string.Empty;
                CompilationErrors.Clear();
                IsCompilationSuccessful = false;
                HasCompilationErrors = false;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 執行編譯
                var stopwatch = Stopwatch.StartNew();
                await _objectEditorService.CompileProcedureAsync(connection, ObjectName, ProcedureSource);
                stopwatch.Stop();

                // 檢查編譯錯誤
                await CheckCompilationErrorsAsync(connection);

                LastCompilationTime = stopwatch.Elapsed;

                if (CompilationErrors.Count == 0)
                {
                    CompilationStatus = "編譯成功";
                    IsCompilationSuccessful = true;
                    HasCompilationErrors = false;
                    StatusMessage = $"預存程序編譯成功，耗時 {LastCompilationTime.TotalSeconds:F2} 秒";
                    
                    // 編譯成功後重新載入參數和相依性資訊
                    await LoadParametersAsync(connection);
                    await LoadDependenciesAsync(connection);
                    
                    // 標記為已儲存
                    HasUnsavedChanges = false;
                }
                else
                {
                    CompilationStatus = "編譯失敗";
                    IsCompilationSuccessful = false;
                    HasCompilationErrors = true;
                    StatusMessage = $"預存程序編譯失敗，發現 {CompilationErrors.Count} 個錯誤";
                }
            }
            catch (Exception ex)
            {
                CompilationStatus = "編譯失敗";
                IsCompilationSuccessful = false;
                HasCompilationErrors = false;
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"預存程序編譯失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以編譯
        /// </summary>
        /// <returns>是否可以編譯</returns>
        private bool CanCompile()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(ProcedureSource) && _getConnection() != null;
        }

        /// <summary>
        /// 驗證 PL/SQL 語法
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnValidateAsync()
        {
            if (string.IsNullOrWhiteSpace(ProcedureSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                StatusMessage = "正在驗證 PL/SQL 語法...";
                HasError = false;
                ErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 執行語法驗證
                // 在 Oracle 中，可以使用 DBMS_SQL.PARSE 來驗證語法
                var validateSql = $@"
                    DECLARE
                        c INTEGER;
                        sql_text CLOB := '{ProcedureSource.Replace("'", "''")}';
                    BEGIN
                        c := DBMS_SQL.OPEN_CURSOR;
                        DBMS_SQL.PARSE(c, sql_text, DBMS_SQL.NATIVE);
                        DBMS_SQL.CLOSE_CURSOR(c);
                    END;";

                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(connection, validateSql);
                StatusMessage = "PL/SQL 語法驗證通過";
            }
            catch (Exception ex)
            {
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"PL/SQL 語法驗證失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以驗證語法
        /// </summary>
        /// <returns>是否可以驗證</returns>
        private bool CanValidate()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(ProcedureSource) && _getConnection() != null;
        }

        /// <summary>
        /// 清除編譯結果
        /// </summary>
        private void OnClearCompilationResults()
        {
            CompilationErrors.Clear();
            CompilationStatus = "尚未編譯";
            IsCompilationSuccessful = false;
            HasCompilationErrors = false;
            StatusMessage = "編譯結果已清除";
        }

        /// <summary>
        /// 是否可以清除編譯結果
        /// </summary>
        /// <returns>是否可以清除</returns>
        private bool CanClearCompilationResults()
        {
            return CompilationErrors.Count > 0 || IsCompilationSuccessful;
        }

        /// <summary>
        /// 檢查編譯錯誤
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task CheckCompilationErrorsAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        LINE AS LineNumber,
                        POSITION AS ColumnNumber,
                        TEXT AS Message,
                        ATTRIBUTE AS ErrorCode
                    FROM 
                        ALL_ERRORS
                    WHERE 
                        OWNER = USER
                        AND NAME = '{ObjectName}'
                        AND TYPE = 'PROCEDURE'
                    ORDER BY 
                        SEQUENCE";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                CompilationErrors.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        CompilationErrors.Add(new CompilationError
                        {
                            LineNumber = row["LineNumber"] != DBNull.Value ? Convert.ToInt32(row["LineNumber"]) : 0,
                            ColumnNumber = row["ColumnNumber"] != DBNull.Value ? Convert.ToInt32(row["ColumnNumber"]) : 0,
                            Message = row["Message"]?.ToString() ?? string.Empty,
                            ErrorCode = row["ErrorCode"]?.ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"檢查編譯錯誤失敗：{ex}");
            }
        }
    }

    /// <summary>
    /// 參數定義模型
    /// </summary>
    public class ParameterDefinition : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _dataType = string.Empty;
        private string _direction = string.Empty;
        private int? _length;
        private int? _precision;
        private int? _scale;
        private string _defaultValue = string.Empty;
        private string _comments = string.Empty;

        /// <summary>
        /// 參數名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料類型
        /// </summary>
        public string DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>
        /// 參數方向 (IN, OUT, IN OUT)
        /// </summary>
        public string Direction
        {
            get => _direction;
            set => SetProperty(ref _direction, value);
        }

        /// <summary>
        /// 長度
        /// </summary>
        public int? Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        /// <summary>
        /// 精度
        /// </summary>
        public int? Precision
        {
            get => _precision;
            set => SetProperty(ref _precision, value);
        }

        /// <summary>
        /// 小數位數
        /// </summary>
        public int? Scale
        {
            get => _scale;
            set => SetProperty(ref _scale, value);
        }

        /// <summary>
        /// 預設值
        /// </summary>
        public string DefaultValue
        {
            get => _defaultValue;
            set => SetProperty(ref _defaultValue, value);
        }

        /// <summary>
        /// 備註
        /// </summary>
        public string Comments
        {
            get => _comments;
            set => SetProperty(ref _comments, value);
        }
    }


}