<UserControl x:Class="OracleMS.Views.TriggerEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:TriggerEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MinWidth" Value="75"/>
            <Setter Property="Height" Value="28"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Style for compilation result text -->
        <Style x:Key="CompilationResultStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        
        <!-- Style for form labels -->
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="0,5,0,2"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/> <!-- Content Area -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Command="{Binding SaveCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="儲存觸發器變更 (Ctrl+S)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" Margin="0,0,4,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding RefreshCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重新載入觸發器定義">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" Margin="0,0,4,0"/>
                        <TextBlock Text="重新載入"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding CompileCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="編譯觸發器 (F5)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔨" Margin="0,0,4,0"/>
                        <TextBlock Text="編譯"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding ValidateCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="驗證觸發器語法">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✓" Margin="0,0,4,0" FontWeight="Bold" Foreground="Blue"/>
                        <TextBlock Text="驗證語法"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding GenerateScriptCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="產生 DDL 腳本">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" Margin="0,0,4,0"/>
                        <TextBlock Text="產生腳本"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <!-- Progress indicator -->
            <ProgressBar Width="100" Height="16" 
                         IsIndeterminate="True"
                         Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                         Margin="10,0"/>
        </ToolBar>

        <!-- Content Area -->
        <TabControl Grid.Row="1">
            <!-- Trigger Settings Tab -->
            <TabItem Header="觸發器設定">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Trigger Settings Form -->
                    <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto" Margin="10">
                        <StackPanel>
                            <GroupBox Header="基本資訊" Margin="0,0,0,10">
                                <StackPanel Margin="5">
                                    <TextBlock Text="觸發器名稱:" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.Name, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                    <TextBlock Text="擁有者:" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.Owner, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>

                                    <TextBlock Text="關聯資料表:" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.TableName, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,0,10"/>
                                </StackPanel>
                            </GroupBox>

                            <GroupBox Header="觸發事件" Margin="0,0,0,10">
                                <StackPanel Margin="5">
                                    <TextBlock Text="觸發時機:" Style="{StaticResource FormLabelStyle}"/>
                                    <ComboBox ItemsSource="{Binding TriggerTimings}" 
                                              SelectedItem="{Binding TriggerDefinition.Timing, UpdateSourceTrigger=PropertyChanged}"
                                              Margin="0,0,0,10"/>

                                    <TextBlock Text="觸發事件:" Style="{StaticResource FormLabelStyle}"/>
                                    <StackPanel Margin="0,5,0,10">
                                        <CheckBox Content="INSERT" IsChecked="{Binding IsInsertEvent, UpdateSourceTrigger=PropertyChanged}" Margin="0,2"/>
                                        <CheckBox Content="UPDATE" IsChecked="{Binding IsUpdateEvent, UpdateSourceTrigger=PropertyChanged}" Margin="0,2"/>
                                        <CheckBox Content="DELETE" IsChecked="{Binding IsDeleteEvent, UpdateSourceTrigger=PropertyChanged}" Margin="0,2"/>
                                    </StackPanel>

                                    <TextBlock Text="觸發層級:" Style="{StaticResource FormLabelStyle}"/>
                                    <StackPanel Orientation="Horizontal" Margin="0,5,0,10">
                                        <RadioButton Content="FOR EACH ROW" IsChecked="{Binding IsRowLevel, UpdateSourceTrigger=PropertyChanged}" Margin="0,0,10,0"/>
                                        <RadioButton Content="STATEMENT" IsChecked="{Binding IsStatementLevel, UpdateSourceTrigger=PropertyChanged}"/>
                                    </StackPanel>
                                </StackPanel>
                            </GroupBox>

                            <GroupBox Header="觸發條件" Margin="0,0,0,10">
                                <StackPanel Margin="5">
                                    <TextBlock Text="觸發條件 (WHEN 子句):" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.Condition, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                             AcceptsReturn="True"
                                             TextWrapping="Wrap"
                                             Height="60"
                                             Margin="0,0,0,10"/>
                                </StackPanel>
                            </GroupBox>

                            <GroupBox Header="狀態" Margin="0,0,0,10">
                                <StackPanel Margin="5">
                                    <CheckBox Content="啟用觸發器" IsChecked="{Binding TriggerDefinition.IsEnabled, UpdateSourceTrigger=PropertyChanged}" Margin="0,5"/>
                                    <TextBlock Text="狀態:" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.Status, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>
                                    <TextBlock Text="建立時間:" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.CreatedDate, StringFormat=yyyy-MM-dd HH:mm:ss, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>
                                    <TextBlock Text="最後修改時間:" Style="{StaticResource FormLabelStyle}"/>
                                    <TextBox Text="{Binding TriggerDefinition.LastModifiedDate, StringFormat=yyyy-MM-dd HH:mm:ss, Mode=OneWay}" IsReadOnly="True" Margin="0,0,0,10"/>
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- DDL Preview -->
                    <GroupBox Grid.Column="1" Header="DDL 預覽" Margin="10">
                        <TextBox Text="{Binding DdlPreview, Mode=OneWay}" 
                                 IsReadOnly="True" 
                                 FontFamily="Consolas" 
                                 TextWrapping="Wrap"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Auto"
                                 Background="#F5F5F5"/>
                    </GroupBox>
                </Grid>
            </TabItem>

            <!-- Trigger Code Tab -->
            <TabItem Header="觸發器程式碼">
                <Grid>
                    <Border BorderBrush="Gray" BorderThickness="1">
                        <avalonedit:TextEditor x:Name="TriggerCodeEditor"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               ShowLineNumbers="True"
                                               WordWrap="False"
                                               HorizontalScrollBarVisibility="Auto"
                                               VerticalScrollBarVisibility="Auto"
                                               Background="White"
                                               Foreground="Black">
                            <avalonedit:TextEditor.Options>
                                <avalonedit:TextEditorOptions ShowSpaces="False"
                                                              ShowTabs="False"
                                                              ShowEndOfLine="False"
                                                              ShowBoxForControlCharacters="False"
                                                              ConvertTabsToSpaces="True"
                                                              IndentationSize="4"/>
                            </avalonedit:TextEditor.Options>
                        </avalonedit:TextEditor>
                    </Border>
                </Grid>
            </TabItem>

            <!-- Compilation Results Tab -->
            <TabItem Header="編譯結果">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Compilation toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <TextBlock Text="{Binding CompilationStatus}" 
                                   FontWeight="Bold">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                                    <Setter Property="Foreground" Value="Gray"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding CompilationStatus}" Value="編譯成功">
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding CompilationStatus}" Value="編譯失敗">
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding CompilationStatus}" Value="編譯中">
                                            <Setter Property="Foreground" Value="Blue"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        <Separator/>
                        <Button Command="{Binding ClearCompilationResultsCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="清除編譯結果">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🗑️" Margin="0,0,4,0"/>
                                    <TextBlock Text="清除結果"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Compilation results content -->
                    <Grid Grid.Row="1">
                        <!-- Success message -->
                        <TextBlock Text="編譯成功！觸發器已成功建立或更新。"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="Green"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Visibility="{Binding IsCompilationSuccessful, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <!-- Error messages -->
                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Auto"
                                      Visibility="{Binding HasCompilationErrors, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ItemsControl ItemsSource="{Binding CompilationErrors}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="Red" BorderThickness="1" Margin="2" Padding="5" Background="LightPink">
                                            <StackPanel>
                                                <TextBlock Text="{Binding ErrorCode, StringFormat='錯誤代碼: {0}'}" 
                                                           Style="{StaticResource CompilationResultStyle}"
                                                           FontWeight="Bold"/>
                                                <TextBlock Text="{Binding LineNumber, StringFormat='行號: {0}'}" 
                                                           Style="{StaticResource CompilationResultStyle}"/>
                                                <TextBlock Text="{Binding ColumnNumber, StringFormat='欄位: {0}'}" 
                                                           Style="{StaticResource CompilationResultStyle}"/>
                                                <TextBlock Text="{Binding Message}" 
                                                           Style="{StaticResource CompilationResultStyle}"
                                                           TextWrapping="Wrap"
                                                           Foreground="DarkRed"
                                                           FontWeight="Bold"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- No compilation results message -->
                        <TextBlock Text="尚未執行編譯操作"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="Gray"
                                   FontSize="14">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsCompilationSuccessful}" Value="False"/>
                                                <Condition Binding="{Binding HasCompilationErrors}" Value="False"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- Referenced Objects Tab -->
            <TabItem Header="相關物件">
                <Grid>
                    <!-- Referenced Objects DataGrid -->
                    <DataGrid x:Name="ReferencedObjectsDataGrid"
                              ItemsSource="{Binding ReferencedObjects}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="物件名稱" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            <DataGridTextColumn Header="物件類型" 
                                                Binding="{Binding Type}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="擁有者" 
                                                Binding="{Binding Owner}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="關聯類型" 
                                                Binding="{Binding ReferenceType}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="描述" 
                                                Binding="{Binding Description}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding LastCompilationTime, StringFormat='編譯時間: {0:F2} 秒'}" 
                           Style="{StaticResource StatusTextStyle}"
                           Visibility="{Binding LastCompilationTime, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Text" Value="載入中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding HasUnsavedChanges}" Value="True">
                                    <Setter Property="Text" Value="已修改"/>
                                    <Setter Property="Foreground" Value="Green"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>