using System;
using System.Diagnostics;
using OracleMS.Interfaces;

namespace OracleMS.Services
{
    /// <summary>
    /// Extension methods for logging
    /// </summary>
    public static class LoggingExtensions
    {
        /// <summary>
        /// Measures the execution time of an action and logs it
        /// </summary>
        /// <param name="logger">The application logger</param>
        /// <param name="source">The source object</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="action">The action to measure</param>
        public static void MeasureAndLogPerformance(this IApplicationLogger logger, object source, string operationName, Action action)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                action();
            }
            finally
            {
                stopwatch.Stop();
                logger.LogPerformance(source, operationName, stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// Measures the execution time of a function and logs it
        /// </summary>
        /// <typeparam name="T">The return type of the function</typeparam>
        /// <param name="logger">The application logger</param>
        /// <param name="source">The source object</param>
        /// <param name="operationName">The name of the operation</param>
        /// <param name="func">The function to measure</param>
        /// <returns>The result of the function</returns>
        public static T MeasureAndLogPerformance<T>(this IApplicationLogger logger, object source, string operationName, Func<T> func)
        {
            var stopwatch = Stopwatch.StartNew();
            try
            {
                return func();
            }
            finally
            {
                stopwatch.Stop();
                logger.LogPerformance(source, operationName, stopwatch.ElapsedMilliseconds);
            }
        }

        /// <summary>
        /// Logs an exception and rethrows it
        /// </summary>
        /// <param name="logger">The application logger</param>
        /// <param name="source">The source object</param>
        /// <param name="message">The error message</param>
        /// <param name="exception">The exception to log</param>
        public static void LogAndRethrow(this IApplicationLogger logger, object source, string message, Exception exception)
        {
            logger.LogError(source, message, exception);
            throw exception;
        }

        /// <summary>
        /// Executes an action with exception handling and logging
        /// </summary>
        /// <param name="logger">The application logger</param>
        /// <param name="source">The source object</param>
        /// <param name="actionName">The name of the action</param>
        /// <param name="action">The action to execute</param>
        /// <param name="onError">Optional action to execute on error</param>
        public static void ExecuteWithLogging(this IApplicationLogger logger, object source, string actionName, Action action, Action<Exception> onError = null)
        {
            try
            {
                logger.LogDebug(source, $"Starting {actionName}");
                action();
                logger.LogDebug(source, $"Completed {actionName}");
            }
            catch (Exception ex)
            {
                logger.LogError(source, $"Error in {actionName}", ex);
                onError?.Invoke(ex);
                throw;
            }
        }

        /// <summary>
        /// Executes a function with exception handling and logging
        /// </summary>
        /// <typeparam name="T">The return type of the function</typeparam>
        /// <param name="logger">The application logger</param>
        /// <param name="source">The source object</param>
        /// <param name="actionName">The name of the action</param>
        /// <param name="func">The function to execute</param>
        /// <param name="onError">Optional action to execute on error</param>
        /// <returns>The result of the function</returns>
        public static T ExecuteWithLogging<T>(this IApplicationLogger logger, object source, string actionName, Func<T> func, Action<Exception> onError = null)
        {
            try
            {
                logger.LogDebug(source, $"Starting {actionName}");
                var result = func();
                logger.LogDebug(source, $"Completed {actionName}");
                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(source, $"Error in {actionName}", ex);
                onError?.Invoke(ex);
                throw;
            }
        }
    }
}