using System;
using System.Threading.Tasks;

namespace OracleMS
{
    /// <summary>
    /// 測試 Column 載入診斷
    /// </summary>
    public class TestColumnLoadingDiagnosis
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== Column 載入診斷測試 ===");
                Console.WriteLine();

                await TestColumnLoadingDiagnosis();

                Console.WriteLine();
                Console.WriteLine("測試完成！按任意鍵結束...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"測試過程中發生錯誤: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
                Console.ReadKey();
            }
        }

        private static async Task TestColumnLoadingDiagnosis()
        {
            Console.WriteLine("1. Column 載入問題診斷");
            Console.WriteLine("   狀態列顯示：'已載入0個欄位'");
            Console.WriteLine("   這表示 LoadColumnsForTableAsync 方法沒有成功載入欄位");
            Console.WriteLine();

            Console.WriteLine("=== 可能的問題原因 ===");
            Console.WriteLine();

            Console.WriteLine("1. SelectedTable 沒有被正確設定");
            Console.WriteLine("   - 檢查日誌：'SelectedTable 設定，舊值: XXX, 新值: XXX'");
            Console.WriteLine("   - 檢查日誌：'SelectedTable 已變更為: XXX，開始載入欄位'");
            Console.WriteLine();

            Console.WriteLine("2. LoadColumnsForTableAsync 方法的參數問題");
            Console.WriteLine("   - 檢查日誌：'開始載入欄位，Table: XXX, Schema: XXX'");
            Console.WriteLine("   - 如果 Table 或 Schema 為空，會看到警告：'載入欄位失敗：Table 或 Schema 為空'");
            Console.WriteLine();

            Console.WriteLine("3. 資料庫連線問題");
            Console.WriteLine("   - 檢查日誌：'載入欄位失敗：無法取得資料庫連線'");
            Console.WriteLine();

            Console.WriteLine("4. 資料庫查詢問題");
            Console.WriteLine("   - 檢查日誌：'開始查詢欄位資訊，Schema.Table: XXX.XXX'");
            Console.WriteLine("   - 檢查日誌：'查詢資料庫欄位資訊: XXX.XXX'");
            Console.WriteLine("   - 檢查日誌：'查詢完成，欄位數量: XXX'");
            Console.WriteLine();

            Console.WriteLine("5. 查詢結果為空");
            Console.WriteLine("   - 檢查日誌：'查詢結果為空，沒有找到欄位資訊'");
            Console.WriteLine("   - 可能原因：");
            Console.WriteLine("     * Schema.Table 名稱不正確");
            Console.WriteLine("     * 權限問題，無法查詢該表格");
            Console.WriteLine("     * 表格不存在");
            Console.WriteLine();

            Console.WriteLine("6. 快取問題");
            Console.WriteLine("   - 檢查日誌：'使用快取的欄位資料，欄位數量: XXX'");
            Console.WriteLine("   - 如果使用快取但欄位數量為0，表示快取中的資料有問題");
            Console.WriteLine();

            Console.WriteLine("=== 診斷步驟 ===");
            Console.WriteLine();

            Console.WriteLine("步驟1：檢查 SelectedTable 設定");
            Console.WriteLine("  - 在編輯模式下開啟索引");
            Console.WriteLine("  - 查看日誌中是否有 'SelectedTable 已變更為: XXX' 的訊息");
            Console.WriteLine("  - 確認 Table 名稱是正確的");
            Console.WriteLine();

            Console.WriteLine("步驟2：檢查載入參數");
            Console.WriteLine("  - 查看日誌中的 '開始載入欄位，Table: XXX, Schema: XXX'");
            Console.WriteLine("  - 確認 Schema 和 Table 都不為空");
            Console.WriteLine("  - 確認 Schema.Table 的組合是正確的");
            Console.WriteLine();

            Console.WriteLine("步驟3：檢查資料庫查詢");
            Console.WriteLine("  - 查看日誌中的 '查詢完成，欄位數量: XXX'");
            Console.WriteLine("  - 如果欄位數量為0，檢查：");
            Console.WriteLine("    * 表格是否存在");
            Console.WriteLine("    * 是否有查詢權限");
            Console.WriteLine("    * Schema.Table 名稱是否正確");
            Console.WriteLine();

            Console.WriteLine("步驟4：檢查 UpdateColumnsUI");
            Console.WriteLine("  - 查看日誌中的 'Columns UI 更新完成，可用 Column 數量: XXX'");
            Console.WriteLine("  - 如果可用欄位數量大於0但狀態列顯示0，檢查狀態訊息的計算邏輯");
            Console.WriteLine();

            Console.WriteLine("=== 修改內容總結 ===");
            Console.WriteLine();

            Console.WriteLine("已新增的診斷日誌：");
            Console.WriteLine("1. SelectedTable setter 中的詳細日誌");
            Console.WriteLine("2. LoadColumnsForTableAsync 方法的詳細日誌");
            Console.WriteLine("3. 資料庫查詢過程的日誌");
            Console.WriteLine("4. 錯誤處理的日誌");
            Console.WriteLine();

            Console.WriteLine("=== 建議的測試步驟 ===");
            Console.WriteLine();

            Console.WriteLine("1. 運行應用程式並開啟索引編輯模式");
            Console.WriteLine("2. 查看應用程式日誌，尋找上述的診斷訊息");
            Console.WriteLine("3. 根據日誌訊息確定問題出現在哪個步驟");
            Console.WriteLine("4. 如果需要，可以手動測試資料庫查詢：");
            Console.WriteLine("   SELECT COLUMN_NAME FROM ALL_TAB_COLUMNS");
            Console.WriteLine("   WHERE OWNER = 'HR' AND TABLE_NAME = 'EMPLOYEES'");
            Console.WriteLine("   ORDER BY COLUMN_ID");
            Console.WriteLine();

            Console.WriteLine("🔍 Column 載入診斷測試完成！");
            Console.WriteLine("請運行應用程式並檢查日誌輸出來診斷問題。");
        }
    }
}
