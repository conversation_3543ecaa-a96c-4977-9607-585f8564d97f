using System;
using System.Threading.Tasks;
using OracleMS.ViewModels;
using OracleMS.Models;

namespace OracleMS
{
    /// <summary>
    /// 測試 Table 選取修復的邏輯
    /// </summary>
    public class TestTableSelectionFix
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== Table 選取修復測試 ===");
                Console.WriteLine();

                await TestTableSelectionLogic();

                Console.WriteLine();
                Console.WriteLine("測試完成！按任意鍵結束...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"測試過程中發生錯誤: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
                Console.ReadKey();
            }
        }

        private static async Task TestTableSelectionLogic()
        {
            Console.WriteLine("1. 測試 UpdateTablesUI 方法的修改");
            Console.WriteLine("   - 檢查編輯模式下是否會自動選取正確的 Table");
            Console.WriteLine("   - 檢查創建模式下是否會自動選取正確的 Table");
            Console.WriteLine();

            // 模擬編輯模式的情況
            Console.WriteLine("=== 編輯模式測試 ===");
            
            var mockIndexDefinition = new IndexDefinition
            {
                Name = "PK_EMPLOYEES",
                TableName = "EMPLOYEES",
                Owner = "HR",
                Type = IndexType.Normal,
                IsUnique = true
            };

            var availableTables = new[] { "DEPARTMENTS", "EMPLOYEES", "JOBS", "LOCATIONS" };
            
            Console.WriteLine($"模擬索引定義: {mockIndexDefinition.Name}");
            Console.WriteLine($"索引所屬 Table: {mockIndexDefinition.TableName}");
            Console.WriteLine($"可用 Tables: [{string.Join(", ", availableTables)}]");
            
            bool tableInList = Array.Exists(availableTables, t => t == mockIndexDefinition.TableName);
            Console.WriteLine($"目標 Table 在可用清單中: {(tableInList ? "✓ 是" : "✗ 否")}");
            
            if (tableInList)
            {
                Console.WriteLine($"✓ 編輯模式：會自動設定 SelectedTable = '{mockIndexDefinition.TableName}'");
            }
            else
            {
                Console.WriteLine("⚠ 編輯模式：目標 Table 不在可用清單中");
            }

            Console.WriteLine();
            Console.WriteLine("=== 創建模式測試 ===");
            
            var mockIndexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "DEPARTMENTS",
                IsEditMode = false
            };
            
            Console.WriteLine($"模擬 IndexEditorInfo: Schema={mockIndexInfo.Schema}, Table={mockIndexInfo.TableName}");
            Console.WriteLine($"可用 Tables: [{string.Join(", ", availableTables)}]");
            
            bool createModeTableInList = Array.Exists(availableTables, t => t == mockIndexInfo.TableName);
            Console.WriteLine($"目標 Table 在可用清單中: {(createModeTableInList ? "✓ 是" : "✗ 否")}");
            
            if (createModeTableInList)
            {
                Console.WriteLine($"✓ 創建模式：會自動設定 SelectedTable = '{mockIndexInfo.TableName}'");
            }
            else
            {
                Console.WriteLine("⚠ 創建模式：目標 Table 不在可用清單中");
            }

            Console.WriteLine();
            Console.WriteLine("=== 修改內容總結 ===");
            Console.WriteLine("1. UpdateTablesUI 方法新增了模式檢查邏輯:");
            Console.WriteLine("   - 編輯模式：檢查 IndexDefinition.TableName");
            Console.WriteLine("   - 創建模式：檢查 _indexInfo.TableName");
            Console.WriteLine();
            Console.WriteLine("2. WaitForTablesLoadedAsync 方法簡化了等待邏輯:");
            Console.WriteLine("   - 不再手動設定 SelectedTable");
            Console.WriteLine("   - 只等待 Tables 載入完成");
            Console.WriteLine("   - 讓 UpdateTablesUI 負責自動選取");
            Console.WriteLine();
            Console.WriteLine("3. 新增了詳細的日誌記錄:");
            Console.WriteLine("   - 記錄可用 Tables 清單");
            Console.WriteLine("   - 記錄自動選取的過程");
            Console.WriteLine("   - 記錄警告訊息");

            Console.WriteLine();
            Console.WriteLine("=== 預期效果 ===");
            Console.WriteLine("修改後，編輯模式下的 IndexEditorView 應該能夠:");
            Console.WriteLine("✅ Schema 下拉選單自動選取索引所屬的 Schema");
            Console.WriteLine("✅ Table 下拉選單自動載入並選取索引所屬的 Table");
            Console.WriteLine("✅ Column 列表自動載入，顯示索引的欄位配置");
            Console.WriteLine("✅ 用戶可以立即看到完整的索引資訊並進行編輯");

            Console.WriteLine();
            Console.WriteLine("🎉 Table 選取修復邏輯測試完成！");
            Console.WriteLine("建議現在測試實際的應用程式來驗證修復效果。");
        }
    }
}
