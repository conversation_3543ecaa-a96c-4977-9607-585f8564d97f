# 交易管理重複邏輯修正

## 問題描述

您發現了一個重要的交易管理問題：

### 原始問題
1. **第 531-536 行** (`ExecuteSqlWithTransactionAsync`)：為單個 SQL 語句管理交易
2. **第 620 行** (`ExecuteMultipleSqlWithTransactionAsync`)：為批次 SQL 語句管理交易

當創建第二個 QueryEditorView 並執行 SELECT 語句時，會出現重複的交易檢查：
- 批次方法在第 620 行啟動交易
- 然後調用個別語句方法，該方法在第 531-536 行又檢查是否需要啟動交易

### 具體場景
```
1. 創建 DbSession，第1個 QueryEditorView 被創建
2. 執行 UPDATE 語句 → 啟動交易
3. 創建第2個 QueryEditorView
4. 執行 SELECT 語句 → 
   - ExecuteMultipleSqlWithTransactionAsync 檢查 !transactionManager.HasActiveTransaction (false)
   - 但仍然可能有重複的交易管理邏輯
```

## 解決方案

### 1. 新增 `skipTransactionManagement` 參數

修改 `ExecuteSqlWithTransactionAsync` 方法簽名：

```csharp
public async Task<QueryResult> ExecuteSqlWithTransactionAsync(
    IDbConnection connection, 
    string sql, 
    ITransactionManager transactionManager,
    CancellationToken cancellationToken = default,
    bool skipTransactionManagement = false)  // 新增參數
```

### 2. 修改個別語句的交易邏輯

```csharp
// 修改前
if (!isSelectQuery && !transactionManager.HasActiveTransaction)
{
    await transactionManager.BeginTransactionAsync(connection);
    transactionStarted = true;
    _logger.LogDebug("自動啟動交易用於非查詢語句");
}

// 修改後
if (!skipTransactionManagement && !isSelectQuery && !transactionManager.HasActiveTransaction)
{
    await transactionManager.BeginTransactionAsync(connection);
    transactionStarted = true;
    _logger.LogDebug("自動啟動交易用於非查詢語句");
}
```

### 3. 批次執行時跳過個別交易管理

```csharp
// 修改前
var result = await ExecuteSqlWithTransactionAsync(connection, sql, transactionManager, cancellationToken);

// 修改後
var result = await ExecuteSqlWithTransactionAsync(connection, sql, transactionManager, cancellationToken, skipTransactionManagement: true);
```

### 4. 更新介面定義

```csharp
// IDatabaseService.cs
Task<QueryResult> ExecuteSqlWithTransactionAsync(
    IDbConnection connection, 
    string sql, 
    ITransactionManager transactionManager,
    CancellationToken cancellationToken = default,
    bool skipTransactionManagement = false);
```

## 修正效果

### 之前的行為
- 批次執行和個別執行都有自己的交易管理邏輯
- 可能導致重複的交易檢查和潛在的衝突

### 修正後的行為
- **批次執行**：在批次層級管理交易，個別語句跳過交易管理
- **單獨執行**：保持原有的交易管理行為
- **清晰的責任分離**：避免重複的交易管理邏輯

## 執行流程

### 批次執行流程
```
ExecuteMultipleSqlWithTransactionAsync
├── 檢查並啟動批次交易 (第 623-627 行)
└── 對每個語句調用 ExecuteSqlWithTransactionAsync(skipTransactionManagement: true)
    └── 跳過個別交易管理，直接執行 SQL
```

### 單獨執行流程
```
ExecuteSqlWithTransactionAsync (skipTransactionManagement: false)
├── 檢查並啟動個別交易 (第 534-538 行)
└── 執行 SQL
```

## 測試建議

1. **批次執行測試**：
   - 執行多個 SQL 語句
   - 確認只在批次層級啟動一次交易

2. **單獨執行測試**：
   - 執行單個 SQL 語句
   - 確認交易管理正常運作

3. **混合場景測試**：
   - 在有活躍交易的情況下執行新的 SQL
   - 確認不會重複啟動交易

## 相關檔案

- `OracleMS\Services\DatabaseService.cs` - 主要修改
- `OracleMS\Interfaces\IDatabaseService.cs` - 介面更新
- `OracleMS\ViewModels\QueryEditorViewModel.cs` - 使用方（無需修改）

這個修正確保了交易管理的一致性和正確性，避免了重複的交易邏輯問題。
