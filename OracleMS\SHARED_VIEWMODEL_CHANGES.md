# 共用 TransactionManager 修改說明

## 修改目的
讓 DbSession 中的多個 QueryEditorView 共用一個 TransactionManager（但每個 QueryEditorView 有自己的 QueryEditorViewModel），使得不同的 QueryEditor 可以看到尚未 commit 之前的 update result。

## 設計理念
- **每個 QueryEditorView 有自己的 QueryEditorViewModel**：保持 SQL 文本、執行結果、UI 狀態等獨立
- **所有 QueryEditorViewModel 共用同一個 TransactionManager**：實現交易狀態共享，讓不同標籤頁看到未提交的變更

## 主要修改

### 1. DbSession.xaml.cs 修改

#### 新增共用實例欄位
```csharp
// 共用的 TransactionManager（但每個 QueryEditor 有自己的 ViewModel）
private Interfaces.ITransactionManager? _sharedTransactionManager;
```

#### 新增初始化方法
```csharp
private void EnsureSharedTransactionManagerInitialized()
{
    if (_sharedTransactionManager == null && App.ServiceProvider != null)
    {
        _sharedTransactionManager = App.ServiceProvider.GetService<Interfaces.ITransactionManager>();
        if (_sharedTransactionManager == null)
        {
            throw new InvalidOperationException("ITransactionManager service not registered");
        }
    }
}
```

#### 修改 CreateNewQueryTab 方法
- 每個 QueryEditor 創建自己的 QueryEditorViewModel
- 但所有 ViewModel 使用共用的 TransactionManager
- 確保每個 ViewModel 都正確設定連線和事件訂閱

#### 修改 SetDatabaseConnection 方法
- 遍歷所有現有的 QueryEditor TabItem
- 為每個 ViewModel 設定連線

#### 新增資源清理
```csharp
private void CleanupResources()
{
    // 清理所有 QueryEditor 的 ViewModel
    foreach (TabItem tabItem in MainTabControl.Items)
    {
        if (tabItem.Content is QueryEditorView queryEditor &&
            queryEditor.DataContext is ViewModels.QueryEditorViewModel viewModel)
        {
            // 取消訂閱事件
            viewModel.NewQueryTabRequested -= OnNewQueryTabRequested;
            // 釋放 ViewModel 資源
            viewModel.Dispose();
        }
    }

    // TransactionManager 是 Singleton，不需要在這裡釋放
    // 只清除本地參考
    _sharedTransactionManager = null;
}
```

### 2. QueryEditorViewModel.cs 修改

#### 新增 GetConnection 方法
```csharp
public IDbConnection? GetConnection()
{
    return _connection;
}
```

### 3. ServiceCollectionExtensions.cs 修改

#### 修改 TransactionManager 註冊方式
```csharp
// 從 Scoped 改為 Singleton，確保全應用程式共用同一個 TransactionManager
services.AddSingleton<ITransactionManager, TransactionManager>();
```

這個修改確保：
- 整個應用程式只有一個 TransactionManager 實例
- 所有 DbSession 和 QueryEditor 都使用同一個交易管理器
- 交易狀態在整個應用程式中保持一致

## 修改效果

### 之前的行為
- 每個 QueryEditorView 有自己的 QueryEditorViewModel
- 每個 QueryEditorView 有自己的 TransactionManager
- 不同的 QueryEditor 無法看到其他 QueryEditor 的未提交變更

### 修改後的行為
- 每個 QueryEditorView 有自己的 QueryEditorViewModel（保持 SQL 文本和結果獨立）
- 所有 QueryEditorView 共用一個 TransactionManager（實現交易共享）
- 不同的 QueryEditor 可以看到尚未 commit 的 update result
- 交易狀態在所有 QueryEditor 之間同步
- 每個標籤頁的 SQL 文本、執行結果等保持獨立

## 注意事項

1. **資源管理**: 確保在 DbSession 卸載時正確清理共用的資源
2. **事件訂閱**: 只需要訂閱一次事件，避免重複訂閱
3. **連線設定**: 連線變更時只需要設定共用的 ViewModel 一次
4. **交易同步**: 所有 QueryEditor 現在共用同一個交易狀態

## 測試

### 單元測試
由於 WPF UI 元件需要在 STA 執行緒中運行，UI 相關的單元測試需要特殊設置。目前已建立基本的測試框架，實際的 UI 測試建議在真實的 WPF 環境中進行。

### 手動測試建議

1. **基本功能測試**：
   - 開啟多個 QueryEditor 標籤頁
   - 驗證所有標籤頁都能正常運作

2. **共用交易測試**：
   - 在一個標籤頁中執行 UPDATE 語句（不 commit）
   - 在另一個標籤頁中執行 SELECT 語句
   - 驗證是否能看到未提交的變更

3. **交易同步測試**：
   - 測試 commit/rollback 操作在所有標籤頁中的同步效果
   - 驗證交易狀態在所有標籤頁中同步顯示

4. **連線管理測試**：
   - 測試資料庫連線變更時，所有標籤頁都能正確更新

## 重要修正

### 連線設定時機問題
**問題**：當 `new Views.DbSession()` 執行時，建構函式中會呼叫 `AddInitialQueryTab()`，此時 `_currentConnection` 還是 null，所以新創建的 QueryEditorViewModel 沒有設定到正確的連線。

**解決方案**：
1. 修改 DbSession 建構函式，不在建構函式中立即創建 QueryEditor
2. 在 `SetDatabaseConnection` 方法中檢查是否有 QueryEditor，如果沒有則創建初始的 QueryEditor
3. 確保 QueryEditor 創建時已經有正確的連線資訊

```csharp
// 修改前：建構函式中立即創建 QueryEditor（連線為 null）
public DbSession()
{
    InitializeComponent();
    AddInitialQueryTab(); // 此時 _currentConnection 為 null
}

// 修改後：延遲創建 QueryEditor 直到有連線
public void SetDatabaseConnection(IDbConnection connection, ConnectionInfo connectionInfo)
{
    _currentConnection = connection;
    _currentConnectionInfo = connectionInfo;

    // 如果還沒有任何 QueryEditor，創建初始的 QueryEditor
    if (CountQueryEditorTabs() == 0)
    {
        CreateNewQueryTab(); // 此時 _currentConnection 已設定
    }
    // ...
}
```

## 已知問題

1. **測試失敗**：將 TransactionManager 改為 Singleton 後，一些現有的整合測試失敗。這些測試需要更新以適應新的生命週期管理。

2. **UI 測試限制**：WPF UI 元件的單元測試需要特殊的 STA 執行緒設置，目前的測試框架不支援。

## 總結

✅ **已完成的修改**：
- 每個 QueryEditorView 有自己的 QueryEditorViewModel（保持 SQL 文本和結果獨立）
- 所有 QueryEditor 共用一個 TransactionManager（Singleton）
- 新增了適當的資源清理機制
- 修改了連線管理邏輯以支援多個 ViewModel
- 新增了 GetConnection 方法以支援測試

✅ **預期效果**：
- 不同的 QueryEditor 標籤頁可以看到尚未 commit 的 update result
- 交易狀態在所有標籤頁之間同步
- 每個標籤頁的 SQL 文本、執行結果等保持獨立
- 提高一致性（統一的交易管理）
- 避免了 SQL 文本混淆的問題

⚠️ **需要注意**：
- 現有的一些整合測試需要更新以適應 Singleton TransactionManager
- 建議進行充分的手動測試以確保功能正常運作
- 考慮在生產環境部署前進行完整的回歸測試
