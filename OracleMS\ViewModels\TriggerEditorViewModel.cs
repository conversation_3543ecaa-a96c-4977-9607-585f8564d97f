using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 觸發器編輯器 ViewModel
    /// </summary>
    public class TriggerEditorViewModel : BaseObjectEditorViewModel
    {
        #region 屬性

        private TriggerDefinition _triggerDefinition;
        private string _triggerSource = string.Empty;
        private string _ddlPreview = string.Empty;
        private ObservableCollection<CompilationError> _compilationErrors = new();
        private ObservableCollection<ReferencedObject> _referencedObjects = new();
        private string _compilationStatus = "尚未編譯";
        private bool _isCompilationSuccessful;
        private bool _hasCompilationErrors;
        private TimeSpan _lastCompilationTime;
        private bool _isInsertEvent;
        private bool _isUpdateEvent;
        private bool _isDeleteEvent;
        private bool _isRowLevel = true;
        private bool _isStatementLevel;
        private ObservableCollection<string> _triggerTimings = new();

        /// <summary>
        /// 觸發器定義
        /// </summary>
        public TriggerDefinition TriggerDefinition
        {
            get => _triggerDefinition;
            private set => SetProperty(ref _triggerDefinition, value);
        }

        /// <summary>
        /// 觸發器原始碼
        /// </summary>
        public string TriggerSource
        {
            get => _triggerSource;
            set
            {
                if (SetProperty(ref _triggerSource, value))
                {
                    if (!_isInitializing)
                    {
                        HasUnsavedChanges = true;
                        UpdateDdlPreview();
                    }
                }
            }
        }

        /// <summary>
        /// DDL 預覽
        /// </summary>
        public string DdlPreview
        {
            get => _ddlPreview;
            private set => SetProperty(ref _ddlPreview, value);
        }

        /// <summary>
        /// 編譯錯誤集合
        /// </summary>
        public ObservableCollection<CompilationError> CompilationErrors
        {
            get => _compilationErrors;
            private set => SetProperty(ref _compilationErrors, value);
        }

        /// <summary>
        /// 相關物件集合
        /// </summary>
        public ObservableCollection<ReferencedObject> ReferencedObjects
        {
            get => _referencedObjects;
            private set => SetProperty(ref _referencedObjects, value);
        }

        /// <summary>
        /// 編譯狀態
        /// </summary>
        public string CompilationStatus
        {
            get => _compilationStatus;
            private set => SetProperty(ref _compilationStatus, value);
        }

        /// <summary>
        /// 編譯是否成功
        /// </summary>
        public bool IsCompilationSuccessful
        {
            get => _isCompilationSuccessful;
            private set => SetProperty(ref _isCompilationSuccessful, value);
        }

        /// <summary>
        /// 是否有編譯錯誤
        /// </summary>
        public bool HasCompilationErrors
        {
            get => _hasCompilationErrors;
            private set => SetProperty(ref _hasCompilationErrors, value);
        }

        /// <summary>
        /// 最後編譯時間
        /// </summary>
        public TimeSpan LastCompilationTime
        {
            get => _lastCompilationTime;
            private set => SetProperty(ref _lastCompilationTime, value);
        }

        /// <summary>
        /// 是否為 INSERT 事件
        /// </summary>
        public bool IsInsertEvent
        {
            get => _isInsertEvent;
            set
            {
                if (SetProperty(ref _isInsertEvent, value))
                {
                    if (!_isInitializing)
                    {
                        UpdateTriggerEvent();
                        HasUnsavedChanges = true;
                        UpdateDdlPreview();
                    }
                }
            }
        }

        /// <summary>
        /// 是否為 UPDATE 事件
        /// </summary>
        public bool IsUpdateEvent
        {
            get => _isUpdateEvent;
            set
            {
                if (SetProperty(ref _isUpdateEvent, value))
                {
                    if (!_isInitializing)
                    {
                        UpdateTriggerEvent();
                        HasUnsavedChanges = true;
                        UpdateDdlPreview();
                    }
                }
            }
        }

        /// <summary>
        /// 是否為 DELETE 事件
        /// </summary>
        public bool IsDeleteEvent
        {
            get => _isDeleteEvent;
            set
            {
                if (SetProperty(ref _isDeleteEvent, value))
                {
                    if (!_isInitializing)
                    {
                        UpdateTriggerEvent();
                        HasUnsavedChanges = true;
                        UpdateDdlPreview();
                    }
                }
            }
        }

        /// <summary>
        /// 是否為行級觸發器
        /// </summary>
        public bool IsRowLevel
        {
            get => _isRowLevel;
            set
            {
                if (SetProperty(ref _isRowLevel, value))
                {
                    if (!_isInitializing)
                    {
                        _isStatementLevel = !value;
                        OnPropertyChanged(nameof(IsStatementLevel));
                        TriggerDefinition.Type = value ? TriggerType.Row : TriggerType.Statement;
                        HasUnsavedChanges = true;
                        UpdateDdlPreview();
                    }
                }
            }
        }

        /// <summary>
        /// 是否為語句級觸發器
        /// </summary>
        public bool IsStatementLevel
        {
            get => _isStatementLevel;
            set
            {
                if (SetProperty(ref _isStatementLevel, value))
                {
                    if (!_isInitializing)
                    {
                        _isRowLevel = !value;
                        OnPropertyChanged(nameof(IsRowLevel));
                        TriggerDefinition.Type = value ? TriggerType.Statement : TriggerType.Row;
                        HasUnsavedChanges = true;
                        UpdateDdlPreview();
                    }
                }
            }
        }

        /// <summary>
        /// 觸發時機選項
        /// </summary>
        public ObservableCollection<string> TriggerTimings
        {
            get => _triggerTimings;
            private set => SetProperty(ref _triggerTimings, value);
        }

        #endregion

        #region 命令

        /// <summary>
        /// 編譯命令
        /// </summary>
        public ICommand CompileCommand { get; }

        /// <summary>
        /// 驗證命令
        /// </summary>
        public ICommand ValidateCommand { get; }

        /// <summary>
        /// 清除編譯結果命令
        /// </summary>
        public ICommand ClearCompilationResultsCommand { get; }

        #endregion

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="triggerName">觸發器名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        public TriggerEditorViewModel(
            string triggerName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(triggerName, DatabaseObjectType.Trigger, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            // 初始化觸發器定義
            _triggerDefinition = new TriggerDefinition { Name = triggerName };

            // 初始化觸發時機選項
            TriggerTimings = new ObservableCollection<string>(
                Enum.GetNames(typeof(TriggerTiming))
            );

            // 初始化命令
            CompileCommand = new AsyncRelayCommand(OnCompileAsync, CanCompile);
            ValidateCommand = new AsyncRelayCommand(OnValidateAsync, CanValidate);
            ClearCompilationResultsCommand = new RelayCommand(OnClearCompilationResults, CanClearCompilationResults);
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 載入觸發器定義
            TriggerDefinition = await _objectEditorService.GetTriggerDefinitionAsync(connection, ObjectName);
            
            // 設定觸發器原始碼
            TriggerSource = TriggerDefinition.Body;
            
            // 設定觸發事件核取方塊
            _isInitializing = true;
            SetTriggerEventCheckboxes();
            SetTriggerTypeRadioButtons();
            _isInitializing = false;

            // 載入相關物件
            await LoadReferencedObjectsAsync(connection);
            
            // 更新 DDL 預覽
            UpdateDdlPreview();
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 更新觸發器主體
            TriggerDefinition.Body = TriggerSource;

            // 儲存觸發器定義
            var result = await _objectEditorService.SaveTriggerDefinitionAsync(connection, TriggerDefinition);
            
            if (!result.IsSuccess)
            {
                throw new Exception($"儲存觸發器失敗: {result.ErrorMessage}");
            }

            // 重新載入相關物件
            await LoadReferencedObjectsAsync(connection);
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected override string GenerateScript()
        {
            return GenerateTriggerDdl();
        }

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(TriggerDefinition.Name))
            {
                result.AddError("觸發器名稱不能為空");
            }

            if (string.IsNullOrWhiteSpace(TriggerDefinition.TableName))
            {
                result.AddError("關聯資料表不能為空");
            }

            if (!IsInsertEvent && !IsUpdateEvent && !IsDeleteEvent)
            {
                result.AddError("必須選擇至少一個觸發事件 (INSERT, UPDATE, DELETE)");
            }

            if (string.IsNullOrWhiteSpace(TriggerSource))
            {
                result.AddError("觸發器程式碼不能為空");
            }

            return result;
        }

        /// <summary>
        /// 載入相關物件
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadReferencedObjectsAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                        d.REFERENCED_TYPE AS Type,
                        d.REFERENCED_OWNER AS Owner,
                        'DEPENDS ON' AS ReferenceType,
                        'This trigger depends on this object' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.NAME = '{ObjectName}'
                        AND d.TYPE = 'TRIGGER'
                    UNION ALL
                    SELECT 
                        t.TABLE_NAME AS Name,
                        'TABLE' AS Type,
                        t.OWNER AS Owner,
                        'DEFINED ON' AS ReferenceType,
                        'This trigger is defined on this table' AS Description
                    FROM 
                        ALL_TRIGGERS t
                    WHERE 
                        t.TRIGGER_NAME = '{ObjectName}'
                    ORDER BY 
                        ReferenceType, Name";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                ReferencedObjects.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        ReferencedObjects.Add(new ReferencedObject
                        {
                            Name = row["Name"].ToString() ?? string.Empty,
                            Type = row["Type"].ToString() ?? string.Empty,
                            Owner = row["Owner"].ToString() ?? string.Empty,
                            ReferenceType = row["ReferenceType"].ToString() ?? string.Empty,
                            Description = row["Description"].ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入相關物件失敗：{ex}");
            }
        }

        /// <summary>
        /// 編譯觸發器
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnCompileAsync()
        {
            if (string.IsNullOrWhiteSpace(TriggerSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                CompilationStatus = "編譯中";
                HasError = false;
                ErrorMessage = string.Empty;
                CompilationErrors.Clear();
                IsCompilationSuccessful = false;
                HasCompilationErrors = false;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 更新觸發器主體
                TriggerDefinition.Body = TriggerSource;

                // 執行編譯
                var stopwatch = Stopwatch.StartNew();
                var result = await _objectEditorService.SaveTriggerDefinitionAsync(connection, TriggerDefinition);
                stopwatch.Stop();

                LastCompilationTime = stopwatch.Elapsed;

                if (result.IsSuccess)
                {
                    CompilationStatus = "編譯成功";
                    IsCompilationSuccessful = true;
                    HasCompilationErrors = false;
                    StatusMessage = $"觸發器編譯成功，耗時 {LastCompilationTime.TotalSeconds:F2} 秒";
                    
                    // 編譯成功後重新載入相關物件
                    await LoadReferencedObjectsAsync(connection);
                    
                    // 標記為已儲存
                    HasUnsavedChanges = false;
                }
                else
                {
                    CompilationStatus = "編譯失敗";
                    IsCompilationSuccessful = false;
                    HasCompilationErrors = true;
                    StatusMessage = $"觸發器編譯失敗：{result.ErrorMessage}";
                    
                    // 新增編譯錯誤
                    CompilationErrors.Add(new CompilationError
                    {
                        LineNumber = result.ErrorLine ?? 0,
                        ColumnNumber = result.ErrorPosition ?? 0,
                        Message = result.ErrorMessage,
                        ErrorCode = result.ErrorCode
                    });
                }
            }
            catch (Exception ex)
            {
                CompilationStatus = "編譯失敗";
                IsCompilationSuccessful = false;
                HasCompilationErrors = false;
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"觸發器編譯失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以編譯
        /// </summary>
        /// <returns>是否可以編譯</returns>
        private bool CanCompile()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(TriggerSource) && _getConnection() != null;
        }

        /// <summary>
        /// 驗證 PL/SQL 語法
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnValidateAsync()
        {
            if (string.IsNullOrWhiteSpace(TriggerSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                StatusMessage = "正在驗證觸發器語法...";
                HasError = false;
                ErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 執行語法驗證
                // 在 Oracle 中，可以使用 DBMS_SQL.PARSE 來驗證語法
                var validateSql = $@"
                    DECLARE
                        c INTEGER;
                        sql_text CLOB := '{GenerateTriggerDdl().Replace("'", "''")}';
                    BEGIN
                        c := DBMS_SQL.OPEN_CURSOR;
                        DBMS_SQL.PARSE(c, sql_text, DBMS_SQL.NATIVE);
                        DBMS_SQL.CLOSE_CURSOR(c);
                    END;";

                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(connection, validateSql);
                StatusMessage = "觸發器語法驗證通過";
            }
            catch (Exception ex)
            {
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"觸發器語法驗證失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以驗證語法
        /// </summary>
        /// <returns>是否可以驗證</returns>
        private bool CanValidate()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(TriggerSource) && _getConnection() != null;
        }

        /// <summary>
        /// 清除編譯結果
        /// </summary>
        private void OnClearCompilationResults()
        {
            CompilationErrors.Clear();
            CompilationStatus = "尚未編譯";
            IsCompilationSuccessful = false;
            HasCompilationErrors = false;
            StatusMessage = "編譯結果已清除";
        }

        /// <summary>
        /// 是否可以清除編譯結果
        /// </summary>
        /// <returns>是否可以清除</returns>
        private bool CanClearCompilationResults()
        {
            return CompilationErrors.Count > 0 || IsCompilationSuccessful;
        }

        /// <summary>
        /// 設定觸發事件核取方塊
        /// </summary>
        private void SetTriggerEventCheckboxes()
        {
            switch (TriggerDefinition.Event)
            {
                case TriggerEvent.Insert:
                    IsInsertEvent = true;
                    IsUpdateEvent = false;
                    IsDeleteEvent = false;
                    break;
                case TriggerEvent.Update:
                    IsInsertEvent = false;
                    IsUpdateEvent = true;
                    IsDeleteEvent = false;
                    break;
                case TriggerEvent.Delete:
                    IsInsertEvent = false;
                    IsUpdateEvent = false;
                    IsDeleteEvent = true;
                    break;
                case TriggerEvent.InsertOrUpdate:
                    IsInsertEvent = true;
                    IsUpdateEvent = true;
                    IsDeleteEvent = false;
                    break;
                case TriggerEvent.InsertOrDelete:
                    IsInsertEvent = true;
                    IsUpdateEvent = false;
                    IsDeleteEvent = true;
                    break;
                case TriggerEvent.UpdateOrDelete:
                    IsInsertEvent = false;
                    IsUpdateEvent = true;
                    IsDeleteEvent = true;
                    break;
                case TriggerEvent.InsertOrUpdateOrDelete:
                    IsInsertEvent = true;
                    IsUpdateEvent = true;
                    IsDeleteEvent = true;
                    break;
            }
        }

        /// <summary>
        /// 設定觸發器類型單選按鈕
        /// </summary>
        private void SetTriggerTypeRadioButtons()
        {
            IsRowLevel = TriggerDefinition.Type == TriggerType.Row;
            IsStatementLevel = TriggerDefinition.Type == TriggerType.Statement;
        }

        /// <summary>
        /// 更新觸發事件
        /// </summary>
        private void UpdateTriggerEvent()
        {
            if (IsInsertEvent && IsUpdateEvent && IsDeleteEvent)
            {
                TriggerDefinition.Event = TriggerEvent.InsertOrUpdateOrDelete;
            }
            else if (IsInsertEvent && IsUpdateEvent)
            {
                TriggerDefinition.Event = TriggerEvent.InsertOrUpdate;
            }
            else if (IsInsertEvent && IsDeleteEvent)
            {
                TriggerDefinition.Event = TriggerEvent.InsertOrDelete;
            }
            else if (IsUpdateEvent && IsDeleteEvent)
            {
                TriggerDefinition.Event = TriggerEvent.UpdateOrDelete;
            }
            else if (IsInsertEvent)
            {
                TriggerDefinition.Event = TriggerEvent.Insert;
            }
            else if (IsUpdateEvent)
            {
                TriggerDefinition.Event = TriggerEvent.Update;
            }
            else if (IsDeleteEvent)
            {
                TriggerDefinition.Event = TriggerEvent.Delete;
            }
        }

        /// <summary>
        /// 更新 DDL 預覽
        /// </summary>
        private void UpdateDdlPreview()
        {
            DdlPreview = GenerateTriggerDdl();
        }

        /// <summary>
        /// 產生觸發器 DDL
        /// </summary>
        /// <returns>觸發器 DDL</returns>
        private string GenerateTriggerDdl()
        {
            var eventClause = GetEventClause();
            var timingClause = GetTimingClause();
            var rowClause = TriggerDefinition.Type == TriggerType.Row ? "FOR EACH ROW" : string.Empty;
            var whenClause = !string.IsNullOrWhiteSpace(TriggerDefinition.Condition) && TriggerDefinition.Type == TriggerType.Row
                ? $"WHEN ({TriggerDefinition.Condition})"
                : string.Empty;

            var ddl = $@"CREATE OR REPLACE TRIGGER {TriggerDefinition.Name}
{timingClause} {eventClause} ON {TriggerDefinition.TableName}
{rowClause}
{whenClause}
{TriggerSource}";

            return ddl;
        }

        /// <summary>
        /// 取得事件子句
        /// </summary>
        /// <returns>事件子句</returns>
        private string GetEventClause()
        {
            switch (TriggerDefinition.Event)
            {
                case TriggerEvent.Insert:
                    return "INSERT";
                case TriggerEvent.Update:
                    return "UPDATE";
                case TriggerEvent.Delete:
                    return "DELETE";
                case TriggerEvent.InsertOrUpdate:
                    return "INSERT OR UPDATE";
                case TriggerEvent.InsertOrDelete:
                    return "INSERT OR DELETE";
                case TriggerEvent.UpdateOrDelete:
                    return "UPDATE OR DELETE";
                case TriggerEvent.InsertOrUpdateOrDelete:
                    return "INSERT OR UPDATE OR DELETE";
                default:
                    return string.Empty;
            }
        }

        /// <summary>
        /// 取得時機子句
        /// </summary>
        /// <returns>時機子句</returns>
        private string GetTimingClause()
        {
            return TriggerDefinition.Timing.ToString().ToUpper();
        }
    }

    /// <summary>
    /// 相關物件模型
    /// </summary>
    public class ReferencedObject : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _type = string.Empty;
        private string _owner = string.Empty;
        private string _referenceType = string.Empty;
        private string _description = string.Empty;

        /// <summary>
        /// 物件名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 物件類型
        /// </summary>
        public string Type
        {
            get => _type;
            set => SetProperty(ref _type, value);
        }

        /// <summary>
        /// 物件擁有者
        /// </summary>
        public string Owner
        {
            get => _owner;
            set => SetProperty(ref _owner, value);
        }

        /// <summary>
        /// 關聯類型
        /// </summary>
        public string ReferenceType
        {
            get => _referenceType;
            set => SetProperty(ref _referenceType, value);
        }

        /// <summary>
        /// 描述
        /// </summary>
        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }
    }
}