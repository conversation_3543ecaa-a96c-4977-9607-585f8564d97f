<UserControl x:Class="OracleMS.Views.DataGridView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:converters="clr-namespace:OracleMS.Views"
             xmlns:dgx="urn:tom-englert.de/DataGridExtensions"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800">
    
    <UserControl.Resources>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:StringToIntConverter x:Key="StringToIntConverter"/>
    </UserControl.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <ToolBar Grid.Row="0">
            <Button Content="新增" Command="{Binding AddRowCommand}" 
                    IsEnabled="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"
                    ToolTip="新增資料列 (Ctrl+N)"/>
            <Button Content="刪除" Command="{Binding DeleteRowCommand}" 
                    IsEnabled="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"
                    ToolTip="刪除選取的資料列 (Delete)"/>
            <Separator/>
            <Button Content="儲存" Command="{Binding SaveChangesCommand}" 
                    ToolTip="儲存所有變更 (Ctrl+S)">
                <Button.Style>
                    <Style TargetType="Button">
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding HasUnsavedChanges}" Value="True">
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="Red"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
            <Button Content="取消" Command="{Binding CancelChangesCommand}" 
                    ToolTip="取消所有變更"/>
            <Button Content="重新整理" Command="{Binding RefreshCommand}" 
                    ToolTip="重新載入資料 (F5)"/>
            <Separator/>
            <Button Content="匯出" Command="{Binding ExportCommand}" 
                    ToolTip="匯出資料到 CSV"/>
            <Button Content="匯入" Command="{Binding ImportCommand}" 
                    IsEnabled="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"
                    ToolTip="從 CSV 匯入資料"/>
        </ToolBar>
        
        <!-- Filter and Search -->
        <Grid Grid.Row="1" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="100"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0" Text="篩選:" VerticalAlignment="Center" Margin="0,0,5,0"/>
            <TextBox Grid.Column="1" Text="{Binding FilterText, UpdateSourceTrigger=PropertyChanged}" 
                     ToolTip="輸入文字以篩選資料"/>
            
            <TextBlock Grid.Column="3" Text="每頁筆數:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <ComboBox Grid.Column="4" ItemsSource="{Binding AvailablePageSizes}" 
                      SelectedItem="{Binding PageSize, Converter={StaticResource StringToIntConverter}}"
                      ToolTip="選擇每頁顯示的資料筆數"/>
        </Grid>
        
        <!-- Data Grid -->
        <DataGrid x:Name="MainDataGrid"
                  Grid.Row="2"
                  ItemsSource="{Binding DataView}"
                  SelectedItem="{Binding SelectedRow}"
                  AutoGenerateColumns="True"
                  CanUserAddRows="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"
                  CanUserDeleteRows="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"
                  CanUserResizeColumns="True"
                  CanUserSortColumns="True"
                  IsReadOnly="{Binding IsReadOnly}"
                  SelectionMode="Single"
                  GridLinesVisibility="All"
                  AlternatingRowBackground="LightGray"
                  RowHeaderWidth="25"
                  EnableRowVirtualization="True"
                  EnableColumnVirtualization="True"
                  VirtualizingPanel.VirtualizationMode="Recycling"
                  dgx:DataGridFilter.IsAutoFilterEnabled="True"
                  dgx:Tools.ApplyInitialSorting="True">

            <!-- Context Menu for rows -->
            <DataGrid.ContextMenu>
                <ContextMenu>
                    <MenuItem Header="新增資料列" Command="{Binding AddRowCommand}"
                              IsEnabled="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"/>
                    <MenuItem Header="刪除資料列" Command="{Binding DeleteRowCommand}"
                              IsEnabled="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"/>
                    <Separator/>
                    <MenuItem Header="複製資料列" Command="{Binding CopyRowCommand}"/>
                    <MenuItem Header="貼上資料列" Command="{Binding PasteRowCommand}"
                              IsEnabled="{Binding IsReadOnly, Converter={StaticResource InverseBooleanConverter}}"/>
                    <Separator/>
                    <MenuItem Header="重新整理" Command="{Binding RefreshCommand}"/>
                </ContextMenu>
            </DataGrid.ContextMenu>

            <!-- Row Style for indicating changes -->
            <DataGrid.RowStyle>
                <Style TargetType="DataGridRow">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding RowState}" Value="Added">
                            <Setter Property="Background" Value="LightGreen"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding RowState}" Value="Modified">
                            <Setter Property="Background" Value="LightYellow"/>
                        </DataTrigger>
                        <DataTrigger Binding="{Binding RowState}" Value="Deleted">
                            <Setter Property="Background" Value="LightPink"/>
                            <Setter Property="Opacity" Value="0.6"/>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </DataGrid.RowStyle>

            <!-- Input bindings for keyboard shortcuts -->
            <DataGrid.InputBindings>
                <KeyBinding Key="Delete" Command="{Binding DeleteRowCommand}"/>
                <KeyBinding Key="N" Modifiers="Ctrl" Command="{Binding AddRowCommand}"/>
                <KeyBinding Key="S" Modifiers="Ctrl" Command="{Binding SaveChangesCommand}"/>
                <KeyBinding Key="F5" Command="{Binding RefreshCommand}"/>
            </DataGrid.InputBindings>
        </DataGrid>
        
        <!-- Pagination Controls -->
        <Grid Grid.Row="3" Margin="5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" Content="第一頁" Command="{Binding FirstPageCommand}" 
                    Margin="0,0,5,0" Padding="10,2"/>
            <Button Grid.Column="1" Content="上一頁" Command="{Binding PreviousPageCommand}" 
                    Margin="0,0,5,0" Padding="10,2"/>
            <Button Grid.Column="2" Content="下一頁" Command="{Binding NextPageCommand}" 
                    Margin="0,0,5,0" Padding="10,2"/>
            <Button Grid.Column="3" Content="最後頁" Command="{Binding LastPageCommand}" 
                    Margin="0,0,10,0" Padding="10,2"/>
            
            <TextBlock Grid.Column="5" Text="{Binding PageInfo}" 
                       VerticalAlignment="Center" HorizontalAlignment="Right"/>
        </Grid>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="4">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" MaxWidth="400" TextTrimming="CharacterEllipsis"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="載入中..." Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBlock Text="唯讀模式" Visibility="{Binding IsReadOnly, Converter={StaticResource BooleanToVisibilityConverter}}" 
                               Foreground="Orange" Margin="10,0,0,0"/>
                    <TextBlock Text="有未儲存變更" Visibility="{Binding HasUnsavedChanges, Converter={StaticResource BooleanToVisibilityConverter}}" 
                               Foreground="Red" FontWeight="Bold" Margin="10,0,0,0"/>
                    <TextBlock Text="{Binding TotalRows, StringFormat='總計: {0} 筆'}" Margin="10,0,0,0"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>