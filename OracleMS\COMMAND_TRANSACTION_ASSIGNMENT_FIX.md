# Command Transaction Assignment 修正

## 問題描述

您正確地指出了一個關鍵問題：在 `OracleDatabaseRepository.cs` 中，當創建 database command 時，需要檢查 `transactionManager` 是否有活躍的交易，如果有，則需要將該交易指派給 command。

### 原始問題
```csharp
using var command = connection.CreateCommand();
command.CommandText = sql;
command.CommandTimeout = 300; // 5 分鐘逾時
// 缺少：command.Transaction = activeTransaction; // 如果有活躍交易
```

這會導致：
- SQL 命令在錯誤的交易上下文中執行（或沒有交易上下文）
- 不同的 QueryEditor 無法看到彼此的未提交變更
- 交易隔離性問題

## 解決方案

### 架構設計決策

我們選擇在 `DatabaseService` 層級處理交易指派，而不是修改整個 `IDatabaseRepository` 介面，因為：

1. **分層責任**：交易管理是業務邏輯層的責任
2. **向後相容**：不需要修改所有 repository 方法的簽名
3. **集中管理**：在一個地方處理所有交易相關邏輯

### 實現方式

#### 1. 新增交易感知的執行方法

```csharp
// 在 DatabaseService.cs 中新增
private async Task<DataTable> ExecuteQueryWithTransactionAsync(
    IDbConnection connection, 
    string sql, 
    ITransactionManager transactionManager, 
    CancellationToken cancellationToken)
{
    if (transactionManager.HasActiveTransaction)
    {
        return await ExecuteQueryInTransactionAsync(connection, sql, transactionManager.CurrentTransaction, cancellationToken);
    }
    else
    {
        return await ExecuteQueryWithCancellationAsync(connection, sql, cancellationToken);
    }
}
```

#### 2. 在交易中執行的具體實現

```csharp
private async Task<DataTable> ExecuteQueryInTransactionAsync(
    IDbConnection connection, 
    string sql, 
    IDbTransaction transaction, 
    CancellationToken cancellationToken)
{
    using var command = connection.CreateCommand();
    command.CommandText = sql;
    command.CommandTimeout = 300;
    command.Transaction = transaction; // 關鍵：指派交易

    // 執行查詢...
}
```

#### 3. 修改 ExecuteSqlWithTransactionAsync 調用

```csharp
// 修改前
if (isSelectQuery)
{
    var dataTable = await ExecuteQueryWithCancellationAsync(connection, sql, cancellationToken);
    // ...
}

// 修改後
if (isSelectQuery)
{
    var dataTable = await ExecuteQueryWithTransactionAsync(connection, sql, transactionManager, cancellationToken);
    // ...
}
```

## 修正效果

### 之前的行為
- SQL 命令沒有正確的交易上下文
- 不同 QueryEditor 的變更互相隔離
- 無法看到未提交的變更

### 修正後的行為
- **正確的交易指派**：所有 SQL 命令都在正確的交易上下文中執行
- **交易共享**：不同 QueryEditor 可以看到彼此的未提交變更
- **一致性保證**：確保 ACID 特性正確實現

## 關鍵程式碼

### 交易指派的核心邏輯
```csharp
// 檢查是否有活躍交易
if (transactionManager.HasActiveTransaction)
{
    // 在交易中執行
    command.Transaction = transactionManager.CurrentTransaction;
}
// 否則在自動提交模式下執行
```

### 支援的操作
- **SELECT 查詢**：在交易中執行，可以看到未提交的變更
- **INSERT/UPDATE/DELETE**：在交易中執行，變更對其他查詢可見
- **DDL 語句**：根據需要自動提交

## 測試建議

1. **基本交易測試**：
   - 在一個 QueryEditor 中執行 UPDATE（不 commit）
   - 在另一個 QueryEditor 中執行 SELECT
   - 確認可以看到未提交的變更

2. **交易隔離測試**：
   - 測試不同隔離級別下的行為
   - 確認交易邊界正確

3. **錯誤處理測試**：
   - 測試交易回復時的行為
   - 確認錯誤不會影響其他查詢

## 相關檔案

- `OracleMS\Services\DatabaseService.cs` - 主要修改
- `OracleMS\Interfaces\ITransactionManager.cs` - 使用 CurrentTransaction 屬性
- `OracleMS\Repositories\OracleDatabaseRepository.cs` - 原始問題所在（間接修正）

這個修正確保了所有 SQL 命令都在正確的交易上下文中執行，實現了真正的交易共享功能。
