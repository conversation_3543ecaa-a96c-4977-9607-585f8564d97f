using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;

namespace OracleMS
{
    /// <summary>
    /// Task 5 實作測試：索引創建和更新命令
    /// </summary>
    public class TestTask5Implementation
    {
        public static async Task RunTests()
        {
            Console.WriteLine("=== Task 5 實作測試：索引創建和更新命令 ===");
            Console.WriteLine();

            await TestCreateIndexCommand();
            await TestUpdateIndexCommand();
            await TestValidation();
            await TestCanExecuteConditions();

            Console.WriteLine("=== Task 5 測試完成 ===");
        }

        private static async Task TestCreateIndexCommand()
        {
            Console.WriteLine("測試 CreateIndexCommand...");

            try
            {
                // 準備測試資料
                var mockDatabaseService = new Mock<IDatabaseService>();
                var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
                var mockObjectEditorService = new Mock<IObjectEditorService>();
                var mockConnection = new Mock<IDbConnection>();
                var mockLogger = new Mock<ILogger>();

                // 設定 mock 行為
                mockConnection.Setup(c => c.State).Returns(ConnectionState.Open);
                mockObjectEditorService.Setup(s => s.CreateIndexAsync(It.IsAny<IDbConnection>(), It.IsAny<IndexDefinition>()))
                    .Returns(Task.CompletedTask);

                // 創建 IndexEditorInfo
                var indexInfo = new IndexEditorInfo
                {
                    Schema = "TEST_SCHEMA",
                    TableName = "TEST_TABLE",
                    IndexName = "TEST_INDEX",
                    IsUnique = true,
                    Columns = new List<string> { "COLUMN1", "COLUMN2" },
                    IsEditMode = false // 創建模式
                };

                // 創建 ViewModel
                var viewModel = new IndexEditorViewModel(
                    indexInfo,
                    mockDatabaseService.Object,
                    mockScriptGeneratorService.Object,
                    mockObjectEditorService.Object,
                    () => mockConnection.Object,
                    mockLogger.Object
                );

                // 設定必要的屬性
                viewModel.SelectedSchema = "TEST_SCHEMA";
                viewModel.SelectedTable = "TEST_TABLE";
                
                // 模擬選擇欄位
                viewModel.SelectedColumns.Add("COLUMN1");
                viewModel.SelectedColumns.Add("COLUMN2");

                // 檢查命令是否可執行
                var canExecute = (viewModel.CreateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                Console.WriteLine($"CreateIndexCommand CanExecute: {canExecute}");

                if (canExecute)
                {
                    // 執行創建命令
                    await (viewModel.CreateIndexCommand as System.Threading.Tasks.Task);
                    Console.WriteLine("CreateIndexCommand 執行成功");

                    // 驗證服務方法被調用
                    mockObjectEditorService.Verify(s => s.CreateIndexAsync(It.IsAny<IDbConnection>(), It.IsAny<IndexDefinition>()), Times.Once);
                    Console.WriteLine("CreateIndexAsync 服務方法被正確調用");
                }

                Console.WriteLine("✓ CreateIndexCommand 測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ CreateIndexCommand 測試失敗: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static async Task TestUpdateIndexCommand()
        {
            Console.WriteLine("測試 UpdateIndexCommand...");

            try
            {
                // 準備測試資料
                var mockDatabaseService = new Mock<IDatabaseService>();
                var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
                var mockObjectEditorService = new Mock<IObjectEditorService>();
                var mockConnection = new Mock<IDbConnection>();
                var mockLogger = new Mock<ILogger>();

                // 設定 mock 行為
                mockConnection.Setup(c => c.State).Returns(ConnectionState.Open);
                mockObjectEditorService.Setup(s => s.UpdateIndexAsync(It.IsAny<IDbConnection>(), It.IsAny<IndexDefinition>()))
                    .Returns(Task.CompletedTask);

                // 創建 IndexEditorInfo (編輯模式)
                var indexInfo = new IndexEditorInfo
                {
                    Schema = "TEST_SCHEMA",
                    TableName = "TEST_TABLE",
                    IndexName = "TEST_INDEX",
                    IsUnique = false,
                    Columns = new List<string> { "COLUMN1" },
                    IsEditMode = true // 編輯模式
                };

                // 創建 ViewModel
                var viewModel = new IndexEditorViewModel(
                    indexInfo,
                    mockDatabaseService.Object,
                    mockScriptGeneratorService.Object,
                    mockObjectEditorService.Object,
                    () => mockConnection.Object,
                    mockLogger.Object
                );

                // 模擬選擇欄位
                viewModel.SelectedColumns.Add("COLUMN1");
                viewModel.SelectedColumns.Add("COLUMN3");

                // 檢查命令是否可執行
                var canExecute = (viewModel.UpdateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                Console.WriteLine($"UpdateIndexCommand CanExecute: {canExecute}");

                if (canExecute)
                {
                    // 執行更新命令
                    await (viewModel.UpdateIndexCommand as System.Threading.Tasks.Task);
                    Console.WriteLine("UpdateIndexCommand 執行成功");

                    // 驗證服務方法被調用
                    mockObjectEditorService.Verify(s => s.UpdateIndexAsync(It.IsAny<IDbConnection>(), It.IsAny<IndexDefinition>()), Times.Once);
                    Console.WriteLine("UpdateIndexAsync 服務方法被正確調用");
                }

                Console.WriteLine("✓ UpdateIndexCommand 測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ UpdateIndexCommand 測試失敗: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static async Task TestValidation()
        {
            Console.WriteLine("測試驗證邏輯...");

            try
            {
                // 準備測試資料
                var mockDatabaseService = new Mock<IDatabaseService>();
                var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
                var mockObjectEditorService = new Mock<IObjectEditorService>();
                var mockConnection = new Mock<IDbConnection>();
                var mockLogger = new Mock<ILogger>();

                // 創建空的 IndexEditorInfo (應該驗證失敗)
                var indexInfo = new IndexEditorInfo
                {
                    Schema = "",
                    TableName = "",
                    IndexName = "",
                    IsUnique = false,
                    Columns = new List<string>(),
                    IsEditMode = false
                };

                // 創建 ViewModel
                var viewModel = new IndexEditorViewModel(
                    indexInfo,
                    mockDatabaseService.Object,
                    mockScriptGeneratorService.Object,
                    mockObjectEditorService.Object,
                    () => mockConnection.Object,
                    mockLogger.Object
                );

                // 檢查創建命令是否不可執行（因為缺少必要資訊）
                var canExecuteCreate = (viewModel.CreateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                Console.WriteLine($"空資料時 CreateIndexCommand CanExecute: {canExecuteCreate}");

                if (!canExecuteCreate)
                {
                    Console.WriteLine("✓ 驗證邏輯正確：空資料時命令不可執行");
                }
                else
                {
                    Console.WriteLine("✗ 驗證邏輯錯誤：空資料時命令仍可執行");
                }

                // 測試填入部分資料
                viewModel.SelectedSchema = "TEST_SCHEMA";
                viewModel.SelectedTable = "TEST_TABLE";
                viewModel.IndexDefinition.Name = "TEST_INDEX";

                var canExecutePartial = (viewModel.CreateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                Console.WriteLine($"部分資料時 CreateIndexCommand CanExecute: {canExecutePartial}");

                // 添加欄位後應該可以執行
                viewModel.SelectedColumns.Add("COLUMN1");
                var canExecuteComplete = (viewModel.CreateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                Console.WriteLine($"完整資料時 CreateIndexCommand CanExecute: {canExecuteComplete}");

                if (canExecuteComplete)
                {
                    Console.WriteLine("✓ 驗證邏輯正確：完整資料時命令可執行");
                }

                Console.WriteLine("✓ 驗證邏輯測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 驗證邏輯測試失敗: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static async Task TestCanExecuteConditions()
        {
            Console.WriteLine("測試 CanExecute 條件...");

            try
            {
                // 準備測試資料
                var mockDatabaseService = new Mock<IDatabaseService>();
                var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
                var mockObjectEditorService = new Mock<IObjectEditorService>();
                var mockConnection = new Mock<IDbConnection>();
                var mockLogger = new Mock<ILogger>();

                mockConnection.Setup(c => c.State).Returns(ConnectionState.Open);

                // 測試創建模式
                var createIndexInfo = new IndexEditorInfo
                {
                    Schema = "TEST_SCHEMA",
                    TableName = "TEST_TABLE",
                    IndexName = "TEST_INDEX",
                    IsUnique = false,
                    Columns = new List<string> { "COLUMN1" },
                    IsEditMode = false
                };

                var createViewModel = new IndexEditorViewModel(
                    createIndexInfo,
                    mockDatabaseService.Object,
                    mockScriptGeneratorService.Object,
                    mockObjectEditorService.Object,
                    () => mockConnection.Object,
                    mockLogger.Object
                );

                createViewModel.SelectedSchema = "TEST_SCHEMA";
                createViewModel.SelectedTable = "TEST_TABLE";
                createViewModel.SelectedColumns.Add("COLUMN1");

                var canCreateIndex = (createViewModel.CreateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                var canUpdateIndex = (createViewModel.UpdateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;

                Console.WriteLine($"創建模式 - CreateIndexCommand CanExecute: {canCreateIndex}");
                Console.WriteLine($"創建模式 - UpdateIndexCommand CanExecute: {canUpdateIndex}");

                // 測試編輯模式
                var editIndexInfo = new IndexEditorInfo
                {
                    Schema = "TEST_SCHEMA",
                    TableName = "TEST_TABLE",
                    IndexName = "TEST_INDEX",
                    IsUnique = false,
                    Columns = new List<string> { "COLUMN1" },
                    IsEditMode = true
                };

                var editViewModel = new IndexEditorViewModel(
                    editIndexInfo,
                    mockDatabaseService.Object,
                    mockScriptGeneratorService.Object,
                    mockObjectEditorService.Object,
                    () => mockConnection.Object,
                    mockLogger.Object
                );

                editViewModel.SelectedColumns.Add("COLUMN1");

                var canCreateIndexEdit = (editViewModel.CreateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;
                var canUpdateIndexEdit = (editViewModel.UpdateIndexCommand as System.Windows.Input.ICommand)?.CanExecute(null) ?? false;

                Console.WriteLine($"編輯模式 - CreateIndexCommand CanExecute: {canCreateIndexEdit}");
                Console.WriteLine($"編輯模式 - UpdateIndexCommand CanExecute: {canUpdateIndexEdit}");

                // 驗證結果
                if (canCreateIndex && !canUpdateIndex && !canCreateIndexEdit && canUpdateIndexEdit)
                {
                    Console.WriteLine("✓ CanExecute 條件測試通過");
                }
                else
                {
                    Console.WriteLine("✗ CanExecute 條件測試失敗");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ CanExecute 條件測試失敗: {ex.Message}");
            }

            Console.WriteLine();
        }
    }
}