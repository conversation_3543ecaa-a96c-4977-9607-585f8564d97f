using System.Windows;
using System.Windows.Controls;

namespace OracleMS.Controls
{
    /// <summary>
    /// 支援虛擬化的欄位列表控制項，用於處理大量資料
    /// </summary>
    public class VirtualizedColumnListBox : ListBox
    {
        static VirtualizedColumnListBox()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(VirtualizedColumnListBox), 
                new FrameworkPropertyMetadata(typeof(VirtualizedColumnListBox)));
        }

        public VirtualizedColumnListBox()
        {
            // 啟用虛擬化以提高大量資料的效能
            VirtualizingPanel.SetIsVirtualizing(this, true);
            VirtualizingPanel.SetVirtualizationMode(this, VirtualizationMode.Recycling);
            VirtualizingPanel.SetScrollUnit(this, ScrollUnit.Item);
            
            // 設定選擇模式
            SelectionMode = SelectionMode.Single;
            
            // 啟用鍵盤導航
            IsTabStop = true;
        }

        protected override void OnSelectionChanged(SelectionChangedEventArgs e)
        {
            base.OnSelectionChanged(e);
            
            // 確保選中的項目可見
            if (SelectedItem != null)
            {
                ScrollIntoView(SelectedItem);
            }
        }

        /// <summary>
        /// 搜尋並選擇指定的項目
        /// </summary>
        /// <param name="item">要選擇的項目</param>
        public void SelectAndScrollToItem(object item)
        {
            if (item != null && Items.Contains(item))
            {
                SelectedItem = item;
                ScrollIntoView(item);
                
                // 設定焦點到選中的項目
                var container = ItemContainerGenerator.ContainerFromItem(item) as ListBoxItem;
                container?.Focus();
            }
        }

        /// <summary>
        /// 清除選擇並滾動到頂部
        /// </summary>
        public void ClearSelectionAndScrollToTop()
        {
            SelectedItem = null;
            if (Items.Count > 0)
            {
                ScrollIntoView(Items[0]);
            }
        }
    }
}