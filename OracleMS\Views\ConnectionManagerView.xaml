<UserControl x:Class="OracleMS.Views.ConnectionManagerView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:vm="clr-namespace:OracleMS.ViewModels"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- Connection Status Converter -->
        <local:ConnectionStatusConverter x:Key="ConnectionStatusConverter"/>
        
        <!-- Connection Item Template -->
        <DataTemplate x:Key="ConnectionItemTemplate">
            <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                    Margin="2" Padding="8" CornerRadius="4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Connection Icon -->
                    <Ellipse Grid.Column="0" Width="12" Height="12" 
                             Fill="{Binding LastConnected, Converter={StaticResource ConnectionStatusConverter}}"
                             Margin="0,0,8,0" VerticalAlignment="Center"/>
                    
                    <!-- Connection Details -->
                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                        <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                        <TextBlock Text="{Binding Server}" FontSize="12" Foreground="Gray"/>
                        <TextBlock FontSize="11" Foreground="Gray">
                            <Run Text="{Binding Username}"/>
                            <Run Text="@"/>
                            <Run Text="{Binding ServiceName}"/>
                        </TextBlock>
                        <TextBlock Text="{Binding LastConnected, StringFormat='上次連線: {0:yyyy/MM/dd HH:mm}'}" 
                                   FontSize="10" Foreground="Gray"/>
                    </StackPanel>
                    
                    <!-- Connection Type Badge -->
                    <Border Grid.Column="2" Background="#F0F0F0" CornerRadius="10" 
                            Padding="6,2" VerticalAlignment="Top">
                        <TextBlock Text="{Binding Type}" FontSize="10" Foreground="#666"/>
                    </Border>
                </Grid>
            </Border>
        </DataTemplate>
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Toolbar -->
        <ToolBar Grid.Row="0" Background="#F8F8F8">
            <Button Command="{Binding AddConnectionCommand}" ToolTip="新增連線">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="➕" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="新增"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding EditConnectionCommand}" ToolTip="編輯連線">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="✏️" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="編輯"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding DeleteConnectionCommand}" ToolTip="刪除連線">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🗑️" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="刪除"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Command="{Binding TestConnectionCommand}" ToolTip="測試連線">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔧" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="測試"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding ConnectCommand}" ToolTip="連線">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔌" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="連線"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Command="{Binding RefreshCommand}" ToolTip="重新整理">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🔄" FontSize="14" Margin="0,0,4,0"/>
                    <TextBlock Text="重新整理"/>
                </StackPanel>
            </Button>
        </ToolBar>
        
        <!-- Connection List -->
        <ListBox Grid.Row="1" 
                 ItemsSource="{Binding Connections}"
                 SelectedItem="{Binding SelectedConnection}"
                 ItemTemplate="{StaticResource ConnectionItemTemplate}"
                 ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                 Background="#FAFAFA">
            <ListBox.ItemContainerStyle>
                <Style TargetType="ListBoxItem">
                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                    <Setter Property="Margin" Value="4"/>
                    <Setter Property="Padding" Value="0"/>
                    <EventSetter Event="MouseDoubleClick" Handler="OnConnectionDoubleClick"/>
                </Style>
            </ListBox.ItemContainerStyle>
        </ListBox>
        
        <!-- Connection Edit Panel -->
        <Border Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0"
                Visibility="{Binding IsEditing, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Expander Header="連線設定" IsExpanded="True" Margin="8">
                <Grid Margin="8">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="100"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Connection Name -->
                    <Label Grid.Row="0" Grid.Column="0" Content="連線名稱:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding EditingConnection.Name, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="4"/>
                    
                    <!-- Server -->
                    <Label Grid.Row="1" Grid.Column="0" Content="伺服器:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding EditingConnection.Server, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="4"/>
                    
                    <!-- Port -->
                    <Label Grid.Row="2" Grid.Column="0" Content="埠號:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding EditingConnection.Port, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="4"/>
                    
                    <!-- Service Name -->
                    <Label Grid.Row="3" Grid.Column="0" Content="服務名稱:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding EditingConnection.ServiceName, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="4"/>
                    
                    <!-- Username -->
                    <Label Grid.Row="4" Grid.Column="0" Content="使用者名稱:" VerticalAlignment="Center"/>
                    <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding EditingConnection.Username, UpdateSourceTrigger=PropertyChanged}" 
                             Margin="4"/>
                    
                    <!-- Password -->
                    <Label Grid.Row="5" Grid.Column="0" Content="密碼:" VerticalAlignment="Center"/>
                    <PasswordBox Grid.Row="5" Grid.Column="1" x:Name="PasswordBox" Margin="4"
                                 PasswordChanged="OnPasswordChanged"/>
                    <CheckBox Grid.Row="5" Grid.Column="2" Content="儲存密碼" 
                              IsChecked="{Binding EditingConnection.SavePassword}" Margin="8,0,0,0"/>
                    
                    <!-- Connection Type -->
                    <Label Grid.Row="6" Grid.Column="0" Content="連線類型:" VerticalAlignment="Center"/>
                    <ComboBox Grid.Row="6" Grid.Column="1" 
                              SelectedItem="{Binding EditingConnection.Type}" Margin="4">
                        <ComboBox.Items>
                            <ComboBoxItem Content="Basic" Tag="Basic"/>
                            <ComboBoxItem Content="TNS" Tag="TNS"/>
                            <ComboBoxItem Content="Advanced" Tag="Advanced"/>
                        </ComboBox.Items>
                    </ComboBox>
                    
                    <!-- Action Buttons -->
                    <StackPanel Grid.Row="7" Grid.Column="1" Orientation="Horizontal" 
                                HorizontalAlignment="Right" Margin="4,8,4,4">
                        <Button Content="測試連線" Command="{Binding TestConnectionCommand}" 
                                Margin="0,0,8,0" Padding="12,4" 
                                IsEnabled="{Binding IsTesting, Converter={StaticResource InverseBooleanConverter}}"/>
                        <Button Content="儲存" Command="{Binding SaveConnectionCommand}" 
                                Margin="0,0,8,0" Padding="12,4"/>
                        <Button Content="取消" Command="{Binding CancelEditCommand}" 
                                Padding="12,4"/>
                    </StackPanel>
                </Grid>
            </Expander>
        </Border>
        
        <!-- Status Bar -->
        <StatusBar Grid.Row="3" Background="#F0F0F0">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <!-- Loading Indicator -->
                    <ProgressBar Width="16" Height="16" IsIndeterminate="True" 
                                 Visibility="{Binding IsTesting, Converter={StaticResource BooleanToVisibilityConverter}}"
                                 Margin="0,0,8,0"/>
                    
                    <!-- Status Text -->
                    <TextBlock Text="{Binding TestResult}" VerticalAlignment="Center"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <Run Text="連線數量: "/>
                    <Run Text="{Binding Connections.Count}"/>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>