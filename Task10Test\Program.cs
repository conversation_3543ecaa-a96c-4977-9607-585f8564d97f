﻿using System.Data;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Repositories;
using OracleMS.Services;

Console.WriteLine("=== Task 10: 擴展資料庫服務以支援schema和table查詢 ===");
Console.WriteLine();

try
{
    // 測試 1: 驗證 IDatabaseRepository 介面擴展
    Console.WriteLine("1. 測試 IDatabaseRepository 介面擴展...");
    TestIDatabaseRepositoryInterface();
    Console.WriteLine("✓ IDatabaseRepository 介面擴展成功");
    Console.WriteLine();

    // 測試 2: 驗證 OracleDatabaseRepository 實作
    Console.WriteLine("2. 測試 OracleDatabaseRepository 實作...");
    await TestOracleDatabaseRepositoryImplementation();
    Console.WriteLine("✓ OracleDatabaseRepository 實作成功");
    Console.WriteLine();

    // 測試 3: 驗證 IDatabaseService 介面擴展
    Console.WriteLine("3. 測試 IDatabaseService 介面擴展...");
    TestIDatabaseServiceInterface();
    Console.WriteLine("✓ IDatabaseService 介面擴展成功");
    Console.WriteLine();

    // 測試 4: 驗證 DatabaseService 實作
    Console.WriteLine("4. 測試 DatabaseService 實作...");
    await TestDatabaseServiceImplementation();
    Console.WriteLine("✓ DatabaseService 實作成功");
    Console.WriteLine();

    // 測試 5: 驗證參數驗證
    Console.WriteLine("5. 測試參數驗證...");
    await TestParameterValidation();
    Console.WriteLine("✓ 參數驗證成功");
    Console.WriteLine();

    Console.WriteLine("=== 所有測試通過！Task 10 實作完成 ===");
}
catch (Exception ex)
{
    Console.WriteLine($"❌ 測試失敗: {ex.Message}");
    Console.WriteLine($"詳細錯誤: {ex}");
}

static void TestIDatabaseRepositoryInterface()
{
    // 驗證 IDatabaseRepository 介面包含新方法
    var repositoryType = typeof(IDatabaseRepository);
    
    var getSchemasMethod = repositoryType.GetMethod("GetSchemasAsync");
    if (getSchemasMethod == null)
        throw new Exception("IDatabaseRepository 缺少 GetSchemasAsync 方法");

    var getTablesBySchemaMethod = repositoryType.GetMethod("GetTablesBySchemaAsync");
    if (getTablesBySchemaMethod == null)
        throw new Exception("IDatabaseRepository 缺少 GetTablesBySchemaAsync 方法");

    // 驗證方法簽名
    if (getSchemasMethod.ReturnType != typeof(Task<IEnumerable<string>>))
        throw new Exception("GetSchemasAsync 方法返回類型不正確");

    if (getTablesBySchemaMethod.ReturnType != typeof(Task<IEnumerable<DatabaseObject>>))
        throw new Exception("GetTablesBySchemaAsync 方法返回類型不正確");

    Console.WriteLine("  - GetSchemasAsync 方法簽名正確");
    Console.WriteLine("  - GetTablesBySchemaAsync 方法簽名正確");
}

static async Task TestOracleDatabaseRepositoryImplementation()
{
    // 創建模擬的 logger
    var mockLogger = new Mock<ILogger<OracleDatabaseRepository>>();
    var repository = new OracleDatabaseRepository(mockLogger.Object);

    // 驗證方法存在且可以調用（雖然會因為沒有真實連線而失敗）
    var repositoryType = typeof(OracleDatabaseRepository);
    
    var getSchemasMethod = repositoryType.GetMethod("GetSchemasAsync");
    var getTablesBySchemaMethod = repositoryType.GetMethod("GetTablesBySchemaAsync");

    if (getSchemasMethod == null || getTablesBySchemaMethod == null)
        throw new Exception("OracleDatabaseRepository 缺少必要的方法實作");

    Console.WriteLine("  - GetSchemasAsync 方法實作存在");
    Console.WriteLine("  - GetTablesBySchemaAsync 方法實作存在");

    // 測試參數驗證（應該拋出 ArgumentException）
    try
    {
        await repository.GetTablesBySchemaAsync(null!, "TEST");
        throw new Exception("應該拋出 ArgumentNullException");
    }
    catch (ArgumentNullException)
    {
        Console.WriteLine("  - Null connection 參數驗證正確");
    }

    try
    {
        var mockConnection = new Mock<IDbConnection>();
        await repository.GetTablesBySchemaAsync(mockConnection.Object, "");
        throw new Exception("應該拋出 ArgumentException");
    }
    catch (ArgumentException)
    {
        Console.WriteLine("  - 空 schema 名稱參數驗證正確");
    }
}

static void TestIDatabaseServiceInterface()
{
    // 驗證 IDatabaseService 介面包含新方法
    var serviceType = typeof(IDatabaseService);
    
    var getSchemasMethod = serviceType.GetMethod("GetSchemasAsync");
    if (getSchemasMethod == null)
        throw new Exception("IDatabaseService 缺少 GetSchemasAsync 方法");

    var getTablesBySchemaMethod = serviceType.GetMethod("GetTablesBySchemaAsync");
    if (getTablesBySchemaMethod == null)
        throw new Exception("IDatabaseService 缺少 GetTablesBySchemaAsync 方法");

    // 驗證方法簽名
    if (getSchemasMethod.ReturnType != typeof(Task<IEnumerable<string>>))
        throw new Exception("GetSchemasAsync 方法返回類型不正確");

    if (getTablesBySchemaMethod.ReturnType != typeof(Task<IEnumerable<DatabaseObject>>))
        throw new Exception("GetTablesBySchemaAsync 方法返回類型不正確");

    Console.WriteLine("  - GetSchemasAsync 方法簽名正確");
    Console.WriteLine("  - GetTablesBySchemaAsync 方法簽名正確");
}

static async Task TestDatabaseServiceImplementation()
{
    // 創建模擬的依賴項
    var mockRepository = new Mock<IDatabaseRepository>();
    var mockLogger = new Mock<ILogger<DatabaseService>>();
    
    var service = new DatabaseService(mockRepository.Object, mockLogger.Object);

    // 驗證方法存在且可以調用
    var serviceType = typeof(DatabaseService);
    
    var getSchemasMethod = serviceType.GetMethod("GetSchemasAsync");
    var getTablesBySchemaMethod = serviceType.GetMethod("GetTablesBySchemaAsync");

    if (getSchemasMethod == null || getTablesBySchemaMethod == null)
        throw new Exception("DatabaseService 缺少必要的方法實作");

    Console.WriteLine("  - GetSchemasAsync 方法實作存在");
    Console.WriteLine("  - GetTablesBySchemaAsync 方法實作存在");

    // 測試參數驗證
    try
    {
        await service.GetSchemasAsync(null!);
        throw new Exception("應該拋出 ArgumentNullException");
    }
    catch (ArgumentNullException)
    {
        Console.WriteLine("  - Null connection 參數驗證正確");
    }

    try
    {
        await service.GetTablesBySchemaAsync(null!, "TEST");
        throw new Exception("應該拋出 ArgumentNullException");
    }
    catch (ArgumentNullException)
    {
        Console.WriteLine("  - Null connection 參數驗證正確");
    }

    try
    {
        var mockConnection = new Mock<IDbConnection>();
        await service.GetTablesBySchemaAsync(mockConnection.Object, "");
        throw new Exception("應該拋出 ArgumentException");
    }
    catch (ArgumentException)
    {
        Console.WriteLine("  - 空 schema 名稱參數驗證正確");
    }
}

static async Task TestParameterValidation()
{
    var mockRepository = new Mock<IDatabaseRepository>();
    var mockLogger = new Mock<ILogger<DatabaseService>>();
    var service = new DatabaseService(mockRepository.Object, mockLogger.Object);

    var mockConnection = new Mock<IDbConnection>();
    mockConnection.Setup(c => c.State).Returns(ConnectionState.Closed);

    // 測試連線狀態驗證
    try
    {
        await service.GetSchemasAsync(mockConnection.Object);
        throw new Exception("應該拋出 InvalidOperationException");
    }
    catch (InvalidOperationException)
    {
        Console.WriteLine("  - 連線狀態驗證正確");
    }

    try
    {
        await service.GetTablesBySchemaAsync(mockConnection.Object, "TEST");
        throw new Exception("應該拋出 InvalidOperationException");
    }
    catch (InvalidOperationException)
    {
        Console.WriteLine("  - 連線狀態驗證正確");
    }

    // 測試成功情況的模擬
    mockConnection.Setup(c => c.State).Returns(ConnectionState.Open);
    mockRepository.Setup(r => r.GetSchemasAsync(It.IsAny<IDbConnection>()))
                 .ReturnsAsync(new List<string> { "SCHEMA1", "SCHEMA2" });
    
    var schemas = await service.GetSchemasAsync(mockConnection.Object);
    if (schemas.Count() != 2)
        throw new Exception("Schema 查詢結果不正確");

    Console.WriteLine("  - 成功情況模擬正確");
}
