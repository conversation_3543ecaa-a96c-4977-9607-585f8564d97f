using OracleMS.Models;
using System.Collections.Generic;
using Xunit;

namespace OracleMS.Tests.Models
{
    public class IndexEditorInfoTests
    {
        [Fact]
        public void IndexEditorInfo_DefaultConstructor_SetsDefaultValues()
        {
            // Arrange & Act
            var indexEditorInfo = new IndexEditorInfo();

            // Assert
            Assert.Null(indexEditorInfo.Schema);
            Assert.Null(indexEditorInfo.TableName);
            Assert.Null(indexEditorInfo.IndexName);
            Assert.False(indexEditorInfo.IsUnique);
            Assert.False(indexEditorInfo.IsEditMode);
            Assert.NotNull(indexEditorInfo.Columns);
            Assert.Equal(0, indexEditorInfo.Columns.Count);
        }

        [Fact]
        public void IndexEditorInfo_SetProperties_PropertiesAreSetCorrectly()
        {
            // Arrange
            var indexEditorInfo = new IndexEditorInfo();
            var expectedColumns = new List<string> { "COLUMN1", "COLUMN2" };

            // Act
            indexEditorInfo.Schema = "TEST_SCHEMA";
            indexEditorInfo.TableName = "TEST_TABLE";
            indexEditorInfo.IndexName = "TEST_INDEX";
            indexEditorInfo.IsUnique = true;
            indexEditorInfo.IsEditMode = true;
            indexEditorInfo.Columns = expectedColumns;

            // Assert
            Assert.Equal("TEST_SCHEMA", indexEditorInfo.Schema);
            Assert.Equal("TEST_TABLE", indexEditorInfo.TableName);
            Assert.Equal("TEST_INDEX", indexEditorInfo.IndexName);
            Assert.True(indexEditorInfo.IsUnique);
            Assert.True(indexEditorInfo.IsEditMode);
            Assert.Equal(expectedColumns, indexEditorInfo.Columns);
            Assert.Equal(2, indexEditorInfo.Columns.Count);
        }

        [Fact]
        public void IndexEditorInfo_CreateMode_IsEditModeIsFalse()
        {
            // Arrange & Act
            var indexEditorInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "NEW_INDEX",
                IsEditMode = false
            };

            // Assert
            Assert.False(indexEditorInfo.IsEditMode);
        }

        [Fact]
        public void IndexEditorInfo_EditMode_IsEditModeIsTrue()
        {
            // Arrange & Act
            var indexEditorInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "EXISTING_INDEX",
                IsEditMode = true
            };

            // Assert
            Assert.True(indexEditorInfo.IsEditMode);
        }

        [Fact]
        public void IndexEditorInfo_ColumnsCollection_CanAddAndRemoveItems()
        {
            // Arrange
            var indexEditorInfo = new IndexEditorInfo();

            // Act
            indexEditorInfo.Columns.Add("COLUMN1");
            indexEditorInfo.Columns.Add("COLUMN2");
            indexEditorInfo.Columns.Add("COLUMN3");

            // Assert
            Assert.Equal(3, indexEditorInfo.Columns.Count);
            Assert.Equal("COLUMN1", indexEditorInfo.Columns[0]);
            Assert.Equal("COLUMN2", indexEditorInfo.Columns[1]);
            Assert.Equal("COLUMN3", indexEditorInfo.Columns[2]);

            // Act - Remove item
            indexEditorInfo.Columns.Remove("COLUMN2");

            // Assert
            Assert.Equal(2, indexEditorInfo.Columns.Count);
            Assert.Equal("COLUMN1", indexEditorInfo.Columns[0]);
            Assert.Equal("COLUMN3", indexEditorInfo.Columns[1]);
        }
    }
}