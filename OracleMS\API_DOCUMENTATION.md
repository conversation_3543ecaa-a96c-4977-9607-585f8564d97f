# Oracle 管理系統 API 文件

*最後更新: 2025-07-20*

## 物件編輯器 API 文件

本文件提供了 Oracle 管理系統中物件編輯器相關 API 的詳細說明，供開發人員參考。

### 核心介面和類別

#### IObjectEditorService

`IObjectEditorService` 是物件編輯器功能的核心介面，提供了與資料庫物件互動的方法。

```csharp
public interface IObjectEditorService
{
    // Table operations
    Task<TableDefinition> GetTableDefinitionAsync(IDbConnection connection, string tableName);
    Task SaveTableDefinitionAsync(IDbConnection connection, TableDefinition definition);
    
    // View operations
    Task<string> GetViewDefinitionAsync(IDbConnection connection, string viewName);
    Task SaveViewDefinitionAsync(IDbConnection connection, string viewName, string definition);
    
    // Procedure/Function operations
    Task<string> GetProcedureSourceAsync(IDbConnection connection, string procedureName);
    Task<string> GetFunctionSourceAsync(IDbConnection connection, string functionName);
    Task CompileProcedureAsync(IDbConnection connection, string procedureName, string source);
    Task CompileFunctionAsync(IDbConnection connection, string functionName, string source);
    
    // Package operations
    Task<PackageDefinition> GetPackageDefinitionAsync(IDbConnection connection, string packageName);
    Task CompilePackageAsync(IDbConnection connection, string packageName, string spec, string body);
    
    // Sequence operations
    Task<SequenceDefinition> GetSequenceDefinitionAsync(IDbConnection connection, string sequenceName);
    Task SaveSequenceDefinitionAsync(IDbConnection connection, SequenceDefinition definition);
    
    // Trigger operations
    Task<TriggerDefinition> GetTriggerDefinitionAsync(IDbConnection connection, string triggerName);
    Task SaveTriggerDefinitionAsync(IDbConnection connection, TriggerDefinition definition);
    
    // Index operations
    Task<IndexDefinition> GetIndexDefinitionAsync(IDbConnection connection, string indexName);
    Task RebuildIndexAsync(IDbConnection connection, string indexName);
}
```

#### BaseObjectEditorViewModel

`BaseObjectEditorViewModel` 是所有物件編輯器 ViewModel 的基底類別，提供了共用的屬性和方法。

```csharp
public abstract class BaseObjectEditorViewModel : ViewModelBase, ISaveable, IDisposable
{
    // 建構函式
    protected BaseObjectEditorViewModel(
        string objectName,
        IDatabaseService databaseService,
        IScriptGeneratorService scriptGeneratorService,
        IObjectEditorService objectEditorService,
        Func<IDbConnection?> getConnection);

    // 共用屬性
    public string ObjectName { get; protected set; }
    public DatabaseObjectType ObjectType { get; protected set; }
    public bool HasUnsavedChanges { get; protected set; }
    public bool IsLoading { get; protected set; }
    public string StatusMessage { get; protected set; }
    
    // 共用命令
    public ICommand SaveCommand { get; }
    public ICommand RefreshCommand { get; }
    public ICommand GenerateScriptCommand { get; }
    
    // 共用方法
    public Task InitializeAsync();
    public Task LoadAsync();
    public Task SaveAsync();
    public Task RefreshAsync();
    public void Dispose();
    
    // 抽象方法（子類別必須實作）
    protected abstract Task LoadObjectAsync();
    protected abstract Task SaveObjectAsync();
    protected abstract string GenerateScript();
}
```

#### ObjectEditorFactory

`ObjectEditorFactory` 是一個靜態工廠類別，用於創建物件編輯器的實例。

```csharp
public static class ObjectEditorFactory
{
    // 創建物件編輯器
    public static UserControl CreateEditor(
        DatabaseObjectType objectType,
        string objectName,
        IDatabaseService databaseService,
        IScriptGeneratorService scriptGeneratorService,
        IObjectEditorService objectEditorService,
        Func<IDbConnection?> getConnection);
    
    // 取得編輯器標題
    public static string GetEditorTitle(
        DatabaseObjectType objectType,
        string objectName);
}
```

### 資料模型

#### TableDefinition

```csharp
public class TableDefinition
{
    public string Name { get; set; }
    public string Owner { get; set; }
    public List<ColumnDefinition> Columns { get; set; } = new();
    public List<IndexDefinition> Indexes { get; set; } = new();
    public List<ConstraintDefinition> Constraints { get; set; } = new();
    public List<TriggerDefinition> Triggers { get; set; } = new();
    public string Comments { get; set; }
}
```

#### ColumnDefinition

```csharp
public class ColumnDefinition
{
    public string Name { get; set; }
    public string DataType { get; set; }
    public int? Length { get; set; }
    public int? Precision { get; set; }
    public int? Scale { get; set; }
    public bool IsNullable { get; set; }
    public string DefaultValue { get; set; }
    public string Comments { get; set; }
}
```

#### ConstraintDefinition

```csharp
public class ConstraintDefinition
{
    public string Name { get; set; }
    public ConstraintType Type { get; set; }
    public List<string> Columns { get; set; } = new();
    public string ReferenceTable { get; set; }
    public List<string> ReferenceColumns { get; set; } = new();
    public string CheckCondition { get; set; }
    public bool IsEnabled { get; set; }
}

public enum ConstraintType
{
    PrimaryKey,
    ForeignKey,
    Unique,
    Check
}
```

#### IndexDefinition

```csharp
public class IndexDefinition
{
    public string Name { get; set; }
    public string Owner { get; set; }
    public string TableName { get; set; }
    public IndexType Type { get; set; }
    public List<IndexColumnDefinition> Columns { get; set; } = new();
    public bool IsUnique { get; set; }
    public string Tablespace { get; set; }
    public IndexStatus Status { get; set; }
}

public enum IndexType
{
    Normal,
    Bitmap,
    Function,
    Domain
}

public enum IndexStatus
{
    Valid,
    Invalid,
    Unusable
}

public class IndexColumnDefinition
{
    public string Name { get; set; }
    public bool IsDescending { get; set; }
    public int Position { get; set; }
}
```

#### PackageDefinition

```csharp
public class PackageDefinition
{
    public string Name { get; set; }
    public string Owner { get; set; }
    public string Specification { get; set; }
    public string Body { get; set; }
    public DateTime SpecCreated { get; set; }
    public DateTime BodyCreated { get; set; }
    public string SpecStatus { get; set; }
    public string BodyStatus { get; set; }
}
```

#### SequenceDefinition

```csharp
public class SequenceDefinition
{
    public string Name { get; set; }
    public string Owner { get; set; }
    public long MinValue { get; set; }
    public long MaxValue { get; set; }
    public long IncrementBy { get; set; }
    public long CacheSize { get; set; }
    public bool IsCycling { get; set; }
    public bool IsOrdered { get; set; }
    public long LastNumber { get; set; }
}
```

#### TriggerDefinition

```csharp
public class TriggerDefinition
{
    public string Name { get; set; }
    public string Owner { get; set; }
    public string TableOwner { get; set; }
    public string TableName { get; set; }
    public TriggerType Type { get; set; }
    public TriggerTiming Timing { get; set; }
    public List<TriggerEvent> Events { get; set; } = new();
    public string WhenClause { get; set; }
    public string Body { get; set; }
    public bool IsEnabled { get; set; }
}

public enum TriggerType
{
    Row,
    Statement
}

public enum TriggerTiming
{
    Before,
    After,
    InsteadOf
}

public enum TriggerEvent
{
    Insert,
    Update,
    Delete
}
```

### 使用範例

#### 註冊服務

在應用程式啟動時，需要註冊物件編輯器相關的服務：

```csharp
public static IServiceCollection AddOracleManagementStudioServices(this IServiceCollection services)
{
    // 註冊物件編輯器服務
    services.AddScoped<IObjectEditorService, ObjectEditorService>();
    
    // 註冊物件編輯器 ViewModel
    services.AddTransient<TableEditorViewModel>();
    services.AddTransient<ViewEditorViewModel>();
    services.AddTransient<ProcedureEditorViewModel>();
    services.AddTransient<FunctionEditorViewModel>();
    services.AddTransient<PackageEditorViewModel>();
    services.AddTransient<SequenceEditorViewModel>();
    services.AddTransient<TriggerEditorViewModel>();
    services.AddTransient<IndexEditorViewModel>();
    
    return services;
}
```

#### 創建物件編輯器

使用 `ObjectEditorFactory` 創建物件編輯器：

```csharp
// 創建資料表編輯器
var tableEditor = ObjectEditorFactory.CreateEditor(
    DatabaseObjectType.Table,
    "EMPLOYEES",
    databaseService,
    scriptGeneratorService,
    objectEditorService,
    () => connection);

// 創建檢視表編輯器
var viewEditor = ObjectEditorFactory.CreateEditor(
    DatabaseObjectType.View,
    "EMP_DETAILS_VIEW",
    databaseService,
    scriptGeneratorService,
    objectEditorService,
    () => connection);
```

#### 在 DbSession 中開啟物件編輯器

```csharp
// 在 DbSession 中開啟資料表編輯器
dbSession.CreateObjectEditorTab(DatabaseObjectType.Table, "EMPLOYEES");

// 在 DbSession 中開啟檢視表編輯器
dbSession.CreateObjectEditorTab(DatabaseObjectType.View, "EMP_DETAILS_VIEW");
```

#### 處理物件編輯器事件

在 ObjectExplorerViewModel 中處理物件編輯器開啟事件：

```csharp
// 訂閱物件編輯器開啟事件
objectExplorerViewModel.OpenTableDataRequested += OnOpenTableDataRequested;
objectExplorerViewModel.OpenViewEditorRequested += OnOpenViewEditorRequested;
objectExplorerViewModel.OpenProcedureEditorRequested += OnOpenProcedureEditorRequested;

// 處理開啟檢視表編輯器的事件
private void OnOpenViewEditorRequested(object? sender, OpenObjectEditorEventArgs e)
{
    CreateObjectEditorTab(DatabaseObjectType.View, e.ObjectName);
}
```

### 錯誤處理

物件編輯器使用 `ObjectEditorErrorHandler` 類別處理錯誤：

```csharp
public class ObjectEditorErrorHandler
{
    public static void HandleError(Exception ex, string operation, string objectName)
    {
        var errorMessage = ex switch
        {
            OracleException oracleEx => FormatOracleError(oracleEx, operation, objectName),
            UnauthorizedAccessException => $"沒有權限執行 {operation} 操作於物件 {objectName}",
            TimeoutException => $"執行 {operation} 操作逾時",
            _ => $"執行 {operation} 時發生未預期的錯誤: {ex.Message}"
        };
        
        // 記錄錯誤
        LogError(ex, operation, objectName);
        
        // 顯示使用者友善的錯誤訊息
        ShowErrorMessage(errorMessage);
    }
}
```

### 擴展指南

#### 新增自訂物件編輯器

若要新增自訂物件編輯器，需要執行以下步驟：

1. 創建物件定義類別（如果需要）
2. 在 `IObjectEditorService` 中新增相關方法
3. 實作 `ObjectEditorService` 中的方法
4. 創建 ViewModel 類別（繼承 `BaseObjectEditorViewModel`）
5. 創建 View 類別
6. 在 `ObjectEditorFactory` 中新增創建方法
7. 在 `DatabaseObjectType` 中新增物件類型
8. 在 `ServiceCollectionExtensions` 中註冊 ViewModel

#### 自訂物件編輯器範例

```csharp
// 1. 創建物件定義類別
public class CustomObjectDefinition
{
    public string Name { get; set; }
    public string Owner { get; set; }
    public string Definition { get; set; }
}

// 2. 在 IObjectEditorService 中新增相關方法
public interface IObjectEditorService
{
    // 現有方法...
    
    // 新增方法
    Task<CustomObjectDefinition> GetCustomObjectDefinitionAsync(IDbConnection connection, string objectName);
    Task SaveCustomObjectDefinitionAsync(IDbConnection connection, CustomObjectDefinition definition);
}

// 3. 實作 ObjectEditorService 中的方法
public class ObjectEditorService : IObjectEditorService
{
    // 現有方法...
    
    // 新增方法實作
    public async Task<CustomObjectDefinition> GetCustomObjectDefinitionAsync(IDbConnection connection, string objectName)
    {
        // 實作取得自訂物件定義的邏輯
    }
    
    public async Task SaveCustomObjectDefinitionAsync(IDbConnection connection, CustomObjectDefinition definition)
    {
        // 實作儲存自訂物件定義的邏輯
    }
}

// 4. 創建 ViewModel 類別
public class CustomObjectEditorViewModel : BaseObjectEditorViewModel
{
    public CustomObjectDefinition CustomObjectDefinition { get; private set; }
    
    public CustomObjectEditorViewModel(
        string objectName,
        IDatabaseService databaseService,
        IScriptGeneratorService scriptGeneratorService,
        IObjectEditorService objectEditorService,
        Func<IDbConnection?> getConnection)
        : base(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection)
    {
        ObjectType = DatabaseObjectType.CustomObject;
    }
    
    protected override async Task LoadObjectAsync()
    {
        // 實作載入自訂物件的邏輯
        CustomObjectDefinition = await _objectEditorService.GetCustomObjectDefinitionAsync(
            _getConnection(), ObjectName);
    }
    
    protected override async Task SaveObjectAsync()
    {
        // 實作儲存自訂物件的邏輯
        await _objectEditorService.SaveCustomObjectDefinitionAsync(
            _getConnection(), CustomObjectDefinition);
    }
    
    protected override string GenerateScript()
    {
        // 實作產生腳本的邏輯
        return $"CREATE OR REPLACE CUSTOM_OBJECT {ObjectName} AS\n{CustomObjectDefinition.Definition}";
    }
}

// 5. 創建 View 類別（XAML 部分省略）
public partial class CustomObjectEditorView : UserControl
{
    public CustomObjectEditorView()
    {
        InitializeComponent();
    }
}

// 6. 在 ObjectEditorFactory 中新增創建方法
public static class ObjectEditorFactory
{
    public static UserControl CreateEditor(
        DatabaseObjectType objectType,
        string objectName,
        IDatabaseService databaseService,
        IScriptGeneratorService scriptGeneratorService,
        IObjectEditorService objectEditorService,
        Func<IDbConnection?> getConnection)
    {
        return objectType switch
        {
            // 現有物件類型...
            
            // 新增自訂物件類型
            DatabaseObjectType.CustomObject => new CustomObjectEditorView
            {
                DataContext = new CustomObjectEditorViewModel(
                    objectName,
                    databaseService,
                    scriptGeneratorService,
                    objectEditorService,
                    getConnection)
            },
            
            _ => throw new NotSupportedException($"Object type {objectType} is not supported")
        };
    }
}

// 7. 在 DatabaseObjectType 中新增物件類型
public enum DatabaseObjectType
{
    Table,
    View,
    Procedure,
    Function,
    Package,
    Sequence,
    Trigger,
    Index,
    CustomObject // 新增自訂物件類型
}

// 8. 在 ServiceCollectionExtensions 中註冊 ViewModel
public static IServiceCollection AddOracleManagementStudioServices(this IServiceCollection services)
{
    // 現有註冊...
    
    // 註冊自訂物件編輯器 ViewModel
    services.AddTransient<CustomObjectEditorViewModel>();
    
    return services;
}
```

## 結語

本文件提供了 Oracle 管理系統中物件編輯器相關 API 的詳細說明。開發人員可以使用這些 API 來擴展系統功能，或者整合到其他應用程式中。

如有任何問題或建議，請聯繫系統開發團隊。