using System;
using System.Data;
using System.Linq;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using Xunit;

namespace OracleMS.Tests.ViewModels
{
    /// <summary>
    /// IndexEditorViewModel 欄位選擇命令測試
    /// </summary>
    public class IndexEditorViewModelCommandTests
    {
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<IScriptGeneratorService> _mockScriptGeneratorService;
        private readonly Mock<IObjectEditorService> _mockObjectEditorService;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<ILogger> _mockLogger;
        private readonly IndexEditorViewModel _viewModel;
        private readonly IndexEditorInfo _indexInfo;

        public IndexEditorViewModelCommandTests()
        {
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            _mockObjectEditorService = new Mock<IObjectEditorService>();
            _mockConnection = new Mock<IDbConnection>();
            _mockLogger = new Mock<ILogger>();

            _indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsUnique = false,
                IsEditMode = false,
                Columns = new System.Collections.Generic.List<string>()
            };

            _viewModel = new IndexEditorViewModel(
                _indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            // 設定測試資料
            SetupTestColumns();
        }

        private void SetupTestColumns()
        {
            // 模擬可用欄位
            _viewModel.AvailableColumns.Clear();
            _viewModel.AvailableColumns.Add("COLUMN1");
            _viewModel.AvailableColumns.Add("COLUMN2");
            _viewModel.AvailableColumns.Add("COLUMN3");

            // 清空已選欄位
            _viewModel.SelectedColumns.Clear();
        }

        [Fact]
        public void AddColumnCommand_WithValidColumn_ShouldMoveColumnToSelected()
        {
            // Arrange
            var columnName = "COLUMN1";
            Assert.True(_viewModel.AvailableColumns.Contains(columnName));
            Assert.False(_viewModel.SelectedColumns.Contains(columnName));

            // Act
            _viewModel.AddColumnCommand.Execute(columnName);

            // Assert
            Assert.False(_viewModel.AvailableColumns.Contains(columnName));
            Assert.True(_viewModel.SelectedColumns.Contains(columnName));
            Assert.Equal(1, _viewModel.SelectedColumns.Count);
        }

        [Fact]
        public void AddColumnCommand_WithNullColumn_ShouldNotChangeCollections()
        {
            // Arrange
            var initialAvailableCount = _viewModel.AvailableColumns.Count;
            var initialSelectedCount = _viewModel.SelectedColumns.Count;

            // Act
            _viewModel.AddColumnCommand.Execute(null);

            // Assert
            Assert.Equal(initialAvailableCount, _viewModel.AvailableColumns.Count);
            Assert.Equal(initialSelectedCount, _viewModel.SelectedColumns.Count);
        }

        [Fact]
        public void AddColumnCommand_WithEmptyColumn_ShouldNotChangeCollections()
        {
            // Arrange
            var initialAvailableCount = _viewModel.AvailableColumns.Count;
            var initialSelectedCount = _viewModel.SelectedColumns.Count;

            // Act
            _viewModel.AddColumnCommand.Execute(string.Empty);

            // Assert
            Assert.Equal(initialAvailableCount, _viewModel.AvailableColumns.Count);
            Assert.Equal(initialSelectedCount, _viewModel.SelectedColumns.Count);
        }

        [Fact]
        public void RemoveColumnCommand_WithValidColumn_ShouldMoveColumnToAvailable()
        {
            // Arrange
            var columnName = "COLUMN1";
            _viewModel.AddColumnCommand.Execute(columnName); // 先新增到已選欄位
            Assert.True(_viewModel.SelectedColumns.Contains(columnName));
            Assert.False(_viewModel.AvailableColumns.Contains(columnName));

            // Act
            _viewModel.RemoveColumnCommand.Execute(columnName);

            // Assert
            Assert.False(_viewModel.SelectedColumns.Contains(columnName));
            Assert.True(_viewModel.AvailableColumns.Contains(columnName));
        }

        [Fact]
        public void RemoveColumnCommand_WithNullColumn_ShouldNotChangeCollections()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            var initialAvailableCount = _viewModel.AvailableColumns.Count;
            var initialSelectedCount = _viewModel.SelectedColumns.Count;

            // Act
            _viewModel.RemoveColumnCommand.Execute(null);

            // Assert
            Assert.Equal(initialAvailableCount, _viewModel.AvailableColumns.Count);
            Assert.Equal(initialSelectedCount, _viewModel.SelectedColumns.Count);
        }

        [Fact]
        public void MoveColumnUpCommand_WithValidColumn_ShouldMoveColumnUp()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");
            _viewModel.AddColumnCommand.Execute("COLUMN3");
            
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[1]);
            Assert.Equal("COLUMN3", _viewModel.SelectedColumns[2]);

            // Act - 將 COLUMN2 上移
            _viewModel.MoveColumnUpCommand.Execute("COLUMN2");

            // Assert
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[1]);
            Assert.Equal("COLUMN3", _viewModel.SelectedColumns[2]);
        }

        [Fact]
        public void MoveColumnUpCommand_WithFirstColumn_ShouldNotMove()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");
            
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[1]);

            // Act - 嘗試將第一個欄位上移
            _viewModel.MoveColumnUpCommand.Execute("COLUMN1");

            // Assert - 順序應該不變
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[1]);
        }

        [Fact]
        public void MoveColumnDownCommand_WithValidColumn_ShouldMoveColumnDown()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");
            _viewModel.AddColumnCommand.Execute("COLUMN3");
            
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[1]);
            Assert.Equal("COLUMN3", _viewModel.SelectedColumns[2]);

            // Act - 將 COLUMN2 下移
            _viewModel.MoveColumnDownCommand.Execute("COLUMN2");

            // Assert
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN3", _viewModel.SelectedColumns[1]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[2]);
        }

        [Fact]
        public void MoveColumnDownCommand_WithLastColumn_ShouldNotMove()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");
            
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[1]);

            // Act - 嘗試將最後一個欄位下移
            _viewModel.MoveColumnDownCommand.Execute("COLUMN2");

            // Assert - 順序應該不變
            Assert.Equal("COLUMN1", _viewModel.SelectedColumns[0]);
            Assert.Equal("COLUMN2", _viewModel.SelectedColumns[1]);
        }

        [Fact]
        public void CanAddColumn_WithValidColumn_ShouldReturnTrue()
        {
            // Arrange
            var columnName = "COLUMN1";

            // Act
            var canExecute = _viewModel.AddColumnCommand.CanExecute(columnName);

            // Assert
            Assert.True(canExecute);
        }

        [Fact]
        public void CanAddColumn_WithNullColumn_ShouldReturnFalse()
        {
            // Act
            var canExecute = _viewModel.AddColumnCommand.CanExecute(null);

            // Assert
            Assert.False(canExecute);
        }

        [Fact]
        public void CanAddColumn_WithColumnNotInAvailable_ShouldReturnFalse()
        {
            // Arrange
            var columnName = "NON_EXISTENT_COLUMN";

            // Act
            var canExecute = _viewModel.AddColumnCommand.CanExecute(columnName);

            // Assert
            Assert.False(canExecute);
        }

        [Fact]
        public void CanRemoveColumn_WithValidColumn_ShouldReturnTrue()
        {
            // Arrange
            var columnName = "COLUMN1";
            _viewModel.AddColumnCommand.Execute(columnName); // 先新增到已選欄位

            // Act
            var canExecute = _viewModel.RemoveColumnCommand.CanExecute(columnName);

            // Assert
            Assert.True(canExecute);
        }

        [Fact]
        public void CanRemoveColumn_WithColumnNotInSelected_ShouldReturnFalse()
        {
            // Arrange
            var columnName = "COLUMN1"; // 在可用欄位中，但不在已選欄位中

            // Act
            var canExecute = _viewModel.RemoveColumnCommand.CanExecute(columnName);

            // Assert
            Assert.False(canExecute);
        }

        [Fact]
        public void CanMoveColumnUp_WithSecondColumn_ShouldReturnTrue()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");

            // Act
            var canExecute = _viewModel.MoveColumnUpCommand.CanExecute("COLUMN2");

            // Assert
            Assert.True(canExecute);
        }

        [Fact]
        public void CanMoveColumnUp_WithFirstColumn_ShouldReturnFalse()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");

            // Act
            var canExecute = _viewModel.MoveColumnUpCommand.CanExecute("COLUMN1");

            // Assert
            Assert.False(canExecute);
        }

        [Fact]
        public void CanMoveColumnDown_WithFirstColumn_ShouldReturnTrue()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");

            // Act
            var canExecute = _viewModel.MoveColumnDownCommand.CanExecute("COLUMN1");

            // Assert
            Assert.True(canExecute);
        }

        [Fact]
        public void CanMoveColumnDown_WithLastColumn_ShouldReturnFalse()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");

            // Act
            var canExecute = _viewModel.MoveColumnDownCommand.CanExecute("COLUMN2");

            // Assert
            Assert.False(canExecute);
        }

        [Fact]
        public void AddColumn_ShouldUpdateIndexDefinitionColumns()
        {
            // Arrange
            var columnName = "COLUMN1";
            var initialColumnCount = _viewModel.IndexDefinition.Columns.Count;

            // Act
            _viewModel.AddColumnCommand.Execute(columnName);

            // Assert
            Assert.Equal(initialColumnCount + 1, _viewModel.IndexDefinition.Columns.Count);
            Assert.True(_viewModel.IndexDefinition.Columns.Any(c => c.ColumnName == columnName));
        }

        [Fact]
        public void RemoveColumn_ShouldUpdateIndexDefinitionColumns()
        {
            // Arrange
            var columnName = "COLUMN1";
            _viewModel.AddColumnCommand.Execute(columnName);
            var initialColumnCount = _viewModel.IndexDefinition.Columns.Count;

            // Act
            _viewModel.RemoveColumnCommand.Execute(columnName);

            // Assert
            Assert.Equal(initialColumnCount - 1, _viewModel.IndexDefinition.Columns.Count);
            Assert.False(_viewModel.IndexDefinition.Columns.Any(c => c.ColumnName == columnName));
        }

        [Fact]
        public void MoveColumnUp_ShouldUpdateIndexDefinitionColumnsOrder()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");
            
            Assert.Equal("COLUMN1", _viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal("COLUMN2", _viewModel.IndexDefinition.Columns[1].ColumnName);

            // Act
            _viewModel.MoveColumnUpCommand.Execute("COLUMN2");

            // Assert
            Assert.Equal("COLUMN2", _viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal("COLUMN1", _viewModel.IndexDefinition.Columns[1].ColumnName);
        }

        [Fact]
        public void MoveColumnDown_ShouldUpdateIndexDefinitionColumnsOrder()
        {
            // Arrange
            _viewModel.AddColumnCommand.Execute("COLUMN1");
            _viewModel.AddColumnCommand.Execute("COLUMN2");
            
            Assert.Equal("COLUMN1", _viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal("COLUMN2", _viewModel.IndexDefinition.Columns[1].ColumnName);

            // Act
            _viewModel.MoveColumnDownCommand.Execute("COLUMN1");

            // Assert
            Assert.Equal("COLUMN2", _viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal("COLUMN1", _viewModel.IndexDefinition.Columns[1].ColumnName);
        }
    }
}