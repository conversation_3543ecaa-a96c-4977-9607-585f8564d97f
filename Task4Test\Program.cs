﻿using System;
using System.Data;
using System.Linq;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;

Console.WriteLine("=== Task 4: 實作欄位選擇相關命令 - 驗證測試 ===");

try
{
    // 設定 Mock 物件
    var mockDatabaseService = new Mock<IDatabaseService>();
    var mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
    var mockObjectEditorService = new Mock<IObjectEditorService>();
    var mockConnection = new Mock<IDbConnection>();
    var mockLogger = new Mock<ILogger>();

    var indexInfo = new IndexEditorInfo
    {
        Schema = "TEST_SCHEMA",
        TableName = "TEST_TABLE",
        IndexName = "TEST_INDEX",
        IsUnique = false,
        IsEditMode = false,
        Columns = new System.Collections.Generic.List<string>()
    };

    var viewModel = new IndexEditorViewModel(
        indexInfo,
        mockDatabaseService.Object,
        mockScriptGeneratorService.Object,
        mockObjectEditorService.Object,
        () => mockConnection.Object,
        mockLogger.Object
    );

    // 設定測試資料
    viewModel.AvailableColumns.Clear();
    viewModel.AvailableColumns.Add("COLUMN1");
    viewModel.AvailableColumns.Add("COLUMN2");
    viewModel.AvailableColumns.Add("COLUMN3");
    viewModel.SelectedColumns.Clear();

    Console.WriteLine("✓ IndexEditorViewModel 初始化成功");

    // 測試 1: AddColumnCommand
    Console.WriteLine("\n--- 測試 AddColumnCommand ---");
    Console.WriteLine($"初始狀態 - 可用欄位: {viewModel.AvailableColumns.Count}, 已選欄位: {viewModel.SelectedColumns.Count}");
    
    if (viewModel.AddColumnCommand.CanExecute("COLUMN1"))
    {
        viewModel.AddColumnCommand.Execute("COLUMN1");
        Console.WriteLine($"新增 COLUMN1 後 - 可用欄位: {viewModel.AvailableColumns.Count}, 已選欄位: {viewModel.SelectedColumns.Count}");
        Console.WriteLine($"COLUMN1 在已選欄位中: {viewModel.SelectedColumns.Contains("COLUMN1")}");
        Console.WriteLine($"COLUMN1 在可用欄位中: {viewModel.AvailableColumns.Contains("COLUMN1")}");
        Console.WriteLine("✓ AddColumnCommand 測試通過");
    }
    else
    {
        Console.WriteLine("✗ AddColumnCommand CanExecute 失敗");
    }

    // 測試 2: RemoveColumnCommand
    Console.WriteLine("\n--- 測試 RemoveColumnCommand ---");
    if (viewModel.RemoveColumnCommand.CanExecute("COLUMN1"))
    {
        viewModel.RemoveColumnCommand.Execute("COLUMN1");
        Console.WriteLine($"移除 COLUMN1 後 - 可用欄位: {viewModel.AvailableColumns.Count}, 已選欄位: {viewModel.SelectedColumns.Count}");
        Console.WriteLine($"COLUMN1 在已選欄位中: {viewModel.SelectedColumns.Contains("COLUMN1")}");
        Console.WriteLine($"COLUMN1 在可用欄位中: {viewModel.AvailableColumns.Contains("COLUMN1")}");
        Console.WriteLine("✓ RemoveColumnCommand 測試通過");
    }
    else
    {
        Console.WriteLine("✗ RemoveColumnCommand CanExecute 失敗");
    }

    // 測試 3: MoveColumnUpCommand 和 MoveColumnDownCommand
    Console.WriteLine("\n--- 測試 MoveColumnUpCommand 和 MoveColumnDownCommand ---");
    viewModel.AddColumnCommand.Execute("COLUMN1");
    viewModel.AddColumnCommand.Execute("COLUMN2");
    viewModel.AddColumnCommand.Execute("COLUMN3");
    
    Console.WriteLine($"新增三個欄位後順序: {string.Join(", ", viewModel.SelectedColumns)}");
    
    if (viewModel.MoveColumnUpCommand.CanExecute("COLUMN2"))
    {
        viewModel.MoveColumnUpCommand.Execute("COLUMN2");
        Console.WriteLine($"COLUMN2 上移後順序: {string.Join(", ", viewModel.SelectedColumns)}");
        Console.WriteLine("✓ MoveColumnUpCommand 測試通過");
    }
    
    if (viewModel.MoveColumnDownCommand.CanExecute("COLUMN2"))
    {
        viewModel.MoveColumnDownCommand.Execute("COLUMN2");
        Console.WriteLine($"COLUMN2 下移後順序: {string.Join(", ", viewModel.SelectedColumns)}");
        Console.WriteLine("✓ MoveColumnDownCommand 測試通過");
    }

    // 測試 4: IndexDefinition 更新
    Console.WriteLine("\n--- 測試 IndexDefinition 更新 ---");
    Console.WriteLine($"IndexDefinition.Columns 數量: {viewModel.IndexDefinition.Columns.Count}");
    Console.WriteLine($"IndexDefinition.Columns 內容: {string.Join(", ", viewModel.IndexDefinition.Columns.Select(c => c.ColumnName))}");
    Console.WriteLine("✓ IndexDefinition 更新測試通過");

    // 測試 5: CanExecute 條件測試
    Console.WriteLine("\n--- 測試 CanExecute 條件 ---");
    Console.WriteLine($"AddColumnCommand.CanExecute(null): {viewModel.AddColumnCommand.CanExecute(null)}");
    Console.WriteLine($"AddColumnCommand.CanExecute(\"NON_EXISTENT\"): {viewModel.AddColumnCommand.CanExecute("NON_EXISTENT")}");
    Console.WriteLine($"RemoveColumnCommand.CanExecute(\"COLUMN1\"): {viewModel.RemoveColumnCommand.CanExecute("COLUMN1")}");
    Console.WriteLine($"MoveColumnUpCommand.CanExecute(\"COLUMN1\"): {viewModel.MoveColumnUpCommand.CanExecute("COLUMN1")}");
    Console.WriteLine($"MoveColumnDownCommand.CanExecute(\"COLUMN3\"): {viewModel.MoveColumnDownCommand.CanExecute("COLUMN3")}");
    Console.WriteLine("✓ CanExecute 條件測試通過");

    Console.WriteLine("\n=== Task 4 實作驗證完成 - 所有測試通過 ===");
}
catch (Exception ex)
{
    Console.WriteLine($"✗ 測試失敗: {ex.Message}");
    Console.WriteLine($"堆疊追蹤: {ex.StackTrace}");
}
