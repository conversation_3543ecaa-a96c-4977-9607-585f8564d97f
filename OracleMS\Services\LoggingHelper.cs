using System;
using System.Collections.Generic;
using System.Text;
using OracleMS.Interfaces;

namespace OracleMS.Services
{
    /// <summary>
    /// Helper class for structured logging
    /// </summary>
    public static class LoggingHelper
    {
        /// <summary>
        /// Creates a structured log message for database operations
        /// </summary>
        /// <param name="operation">The database operation</param>
        /// <param name="objectName">The name of the database object</param>
        /// <param name="objectType">The type of the database object</param>
        /// <param name="additionalInfo">Additional information</param>
        /// <returns>A structured log message</returns>
        public static string FormatDatabaseOperation(string operation, string objectName, string objectType, Dictionary<string, object> additionalInfo = null)
        {
            var sb = new StringBuilder();
            sb.Append($"Database Operation: {operation} | Object: {objectName} | Type: {objectType}");
            
            if (additionalInfo != null && additionalInfo.Count > 0)
            {
                sb.Append(" | Details: ");
                var first = true;
                foreach (var kvp in additionalInfo)
                {
                    if (!first)
                    {
                        sb.Append(", ");
                    }
                    sb.Append($"{kvp.Key}={kvp.Value}");
                    first = false;
                }
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Creates a structured log message for user actions
        /// </summary>
        /// <param name="action">The user action</param>
        /// <param name="component">The component where the action occurred</param>
        /// <param name="additionalInfo">Additional information</param>
        /// <returns>A structured log message</returns>
        public static string FormatUserAction(string action, string component, Dictionary<string, object> additionalInfo = null)
        {
            var sb = new StringBuilder();
            sb.Append($"User Action: {action} | Component: {component}");
            
            if (additionalInfo != null && additionalInfo.Count > 0)
            {
                sb.Append(" | Details: ");
                var first = true;
                foreach (var kvp in additionalInfo)
                {
                    if (!first)
                    {
                        sb.Append(", ");
                    }
                    sb.Append($"{kvp.Key}={kvp.Value}");
                    first = false;
                }
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Creates a structured log message for application events
        /// </summary>
        /// <param name="eventName">The name of the event</param>
        /// <param name="component">The component where the event occurred</param>
        /// <param name="additionalInfo">Additional information</param>
        /// <returns>A structured log message</returns>
        public static string FormatApplicationEvent(string eventName, string component, Dictionary<string, object> additionalInfo = null)
        {
            var sb = new StringBuilder();
            sb.Append($"Application Event: {eventName} | Component: {component}");
            
            if (additionalInfo != null && additionalInfo.Count > 0)
            {
                sb.Append(" | Details: ");
                var first = true;
                foreach (var kvp in additionalInfo)
                {
                    if (!first)
                    {
                        sb.Append(", ");
                    }
                    sb.Append($"{kvp.Key}={kvp.Value}");
                    first = false;
                }
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Creates a structured log message for exceptions
        /// </summary>
        /// <param name="exception">The exception</param>
        /// <param name="component">The component where the exception occurred</param>
        /// <param name="operation">The operation that caused the exception</param>
        /// <returns>A structured log message</returns>
        public static string FormatException(Exception exception, string component, string operation)
        {
            return $"Exception: {exception.GetType().Name} | Component: {component} | Operation: {operation} | Message: {exception.Message}";
        }
    }
}