using System;
using System.T**eading.Tasks;
using System.Windows;
using OracleMS.ViewModels;
using OracleMS.Models;
using OracleMS.Services;
using OracleMS.Repositories;
using OracleMS.Interfaces;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.DependencyInjection;
using System.Data;
using Oracle.ManagedDataAccess.Client;

namespace IndexEditorEditModeTest
{
    /// <summary>
    /// 測試 IndexEditorView 編輯模式的自動載入功能
    /// </summary>
    public class Program
    {
        private static IServiceProvider _serviceProvider;
        private static ILogger<Program> _logger;

        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== IndexEditorView 編輯模式測試 ===");
                Console.WriteLine();

                // 設定服務
                SetupServices();

                // 測試編輯模式的自動載入
                await TestEditModeAutoLoading();

                Console.WriteLine();
                Console.WriteLine("測試完成！按任意鍵結束...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"測試過程中發生錯誤: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
                Console.ReadKey();
            }
        }

        private static void SetupServices()
        {
            var services = new ServiceCollection();

            // 註冊日誌服務
            services.AddLogging(builder =>
            {
                builder.AddConsole();
                builder.SetMinimumLevel(LogLevel.Information);
            });

            // 註冊服務
            services.AddSingleton<IDatabaseRepository, OracleDatabaseRepository>();
            services.AddSingleton<IDatabaseService, DatabaseService>();
            services.AddSingleton<IObjectEditorService, ObjectEditorService>();
            services.AddSingleton<IScriptGeneratorService, ScriptGeneratorService>();

            _serviceProvider = services.BuildServiceProvider();
            _logger = _serviceProvider.GetRequiredService<ILogger<Program>>();
        }

        private static async Task TestEditModeAutoLoading()
        {
            Console.WriteLine("1. 測試編輯模式的自動載入功能");
            Console.WriteLine("   - 檢查 Schema 下拉選單是否自動選取索引所屬的 Schema");
            Console.WriteLine("   - 檢查 Table 下拉選單是否自動載入並選取索引所屬的 Table");
            Console.WriteLine("   - 檢查 Column 列表是否自動載入，顯示索引的欄位配置");
            Console.WriteLine();

            // 模擬資料庫連線
            var connectionString = "Data Source=localhost:1521/XE;User Id=**;Password=**;";
            
            try
            {
                using var connection = new OracleConnection(connectionString);
                await connection.OpenAsync();
                Console.WriteLine("✓ 資料庫連線成功");

                // 創建 IndexEditorViewModel 進行編輯模式測試
                var databaseService = _serviceProvider.GetRequiredService<IDatabaseService>();
                var scriptGeneratorService = _serviceProvider.GetRequiredService<IScriptGeneratorService>();
                var objectEditorService = _serviceProvider.GetRequiredService<IObjectEditorService>();
                var logger = _serviceProvider.GetRequiredService<ILogger<IndexEditorViewModel>>();

                // 創建 IndexEditorInfo 用於編輯模式
                var indexInfo = new IndexEditorInfo
                {
                    IndexName = "PK_EMPLOYEES", // 假設這是一個存在的索引
                    IsEditMode = true
                };

                var viewModel = new IndexEditorViewModel(
                    indexInfo,
                    databaseService,
                    scriptGeneratorService,
                    objectEditorService,
                    () => connection,
                    logger
                );

                Console.WriteLine($"✓ IndexEditorViewModel 創建成功 (編輯模式: {indexInfo.IndexName})");

                // 初始化 ViewModel
                Console.WriteLine("正在初始化 ViewModel...");
                await viewModel.InitializeAsync();

                // 檢查初始化結果
                Console.WriteLine();
                Console.WriteLine("=== 初始化結果檢查 ===");
                
                Console.WriteLine($"可用 Schema 數量: {viewModel.AvailableSchemas.Count}");
                Console.WriteLine($"選取的 Schema: {viewModel.SelectedSchema ?? "未選取"}");
                
                Console.WriteLine($"可用 Table 數量: {viewModel.AvailableTables.Count}");
                Console.WriteLine($"選取的 Table: {viewModel.SelectedTable ?? "未選取"}");
                
                Console.WriteLine($"可用 Column 數量: {viewModel.AvailableColumns.Count}");
                
                if (viewModel.IndexDefinition != null)
                {
                    Console.WriteLine($"索引定義載入成功:");
                    Console.WriteLine($"  - 索引名稱: {viewModel.IndexDefinition.Name}");
                    Console.WriteLine($"  - 所屬 Schema: {viewModel.IndexDefinition.Owner}");
                    Console.WriteLine($"  - 所屬 Table: {viewModel.IndexDefinition.TableName}");
                    Console.WriteLine($"  - 索引類型: {viewModel.IndexDefinition.Type}");
                    Console.WriteLine($"  - 是否唯一: {viewModel.IndexDefinition.IsUnique}");
                    Console.WriteLine($"  - 索引欄位數量: {viewModel.IndexDefinition.Columns.Count}");
                }
                else
                {
                    Console.WriteLine("⚠ 索引定義未載入");
                }

                // 驗證自動載入是否成功
                Console.WriteLine();
                Console.WriteLine("=== 自動載入驗證 ===");
                
                bool schemaAutoSelected = !string.IsNullOrEmpty(viewModel.SelectedSchema);
                bool tableAutoSelected = !string.IsNullOrEmpty(viewModel.SelectedTable);
                bool columnsLoaded = viewModel.AvailableColumns.Count > 0;

                Console.WriteLine($"Schema 自動選取: {(schemaAutoSelected ? "✓ 成功" : "✗ 失敗")}");
                Console.WriteLine($"Table 自動選取: {(tableAutoSelected ? "✓ 成功" : "✗ 失敗")}");
                Console.WriteLine($"Column 自動載入: {(columnsLoaded ? "✓ 成功" : "✗ 失敗")}");

                if (schemaAutoSelected && tableAutoSelected && columnsLoaded)
                {
                    Console.WriteLine();
                    Console.WriteLine("🎉 所有自動載入功能都正常工作！");
                }
                else
                {
                    Console.WriteLine();
                    Console.WriteLine("⚠ 部分自動載入功能未正常工作，需要進一步檢查");
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 測試失敗: {ex.Message}");
                if (ex.Message.Contains("ORA-"))
                {
                    Console.WriteLine("提示: 請確保 Oracle 資料庫正在運行且連線資訊正確");
                }
            }
        }
    }
}
