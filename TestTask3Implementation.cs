using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using OracleMS.Models;
using OracleMS.ViewModels;
using OracleMS.Interfaces;
using System.Data;

namespace OracleMS
{
    /// <summary>
    /// Task 3 實作測試：驗證 IndexEditorViewModel 的 PopulateColumnsFromIndexDefinition 方法
    /// </summary>
    public class TestTask3Implementation
    {
        public static void RunTests()
        {
            Console.WriteLine("=== Task 3 Implementation Tests ===");
            Console.WriteLine("測試 IndexEditorViewModel 的 PopulateColumnsFromIndexDefinition 方法");
            Console.WriteLine();

            try
            {
                TestPopulateColumnsFromIndexDefinition();
                TestPopulateColumnsWithNullIndexDefinition();
                TestPopulateColumnsWithEmptyColumns();
                TestPopulateColumnsWithValidColumns();
                
                Console.WriteLine("✅ 所有測試通過！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 測試失敗: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        private static void TestPopulateColumnsFromIndexDefinition()
        {
            Console.WriteLine("測試 1: PopulateColumnsFromIndexDefinition 方法存在性");
            
            // 創建測試用的 IndexEditorViewModel
            var indexInfo = new IndexEditorInfo
            {
                IndexName = "TEST_INDEX",
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IsEditMode = true
            };

            var mockLogger = new TestLogger();
            var mockDatabaseService = new MockDatabaseService();
            var mockScriptGeneratorService = new MockScriptGeneratorService();
            var mockObjectEditorService = new MockObjectEditorService();
            
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                mockDatabaseService,
                mockScriptGeneratorService,
                mockObjectEditorService,
                () => null,
                mockLogger
            );

            // 驗證 ViewModel 創建成功
            Console.WriteLine("✅ IndexEditorViewModel 創建成功");
            
            // 驗證初始狀態
            Console.WriteLine($"   - 初始 SelectedColumns 數量: {viewModel.SelectedColumns.Count}");
            Console.WriteLine($"   - 初始 AvailableColumns 數量: {viewModel.AvailableColumns.Count}");
        }

        private static void TestPopulateColumnsWithNullIndexDefinition()
        {
            Console.WriteLine("\n測試 2: IndexDefinition 為 null 時的處理");
            
            var indexInfo = new IndexEditorInfo
            {
                IndexName = "TEST_INDEX",
                Schema = "TEST_SCHEMA", 
                TableName = "TEST_TABLE",
                IsEditMode = true
            };

            var mockLogger = new TestLogger();
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                new MockDatabaseService(),
                new MockScriptGeneratorService(),
                new MockObjectEditorService(),
                () => null,
                mockLogger
            );

            // 設定 IndexDefinition 為 null（透過反射調用私有方法）
            var indexDefinitionProperty = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            indexDefinitionProperty?.SetValue(viewModel, null);

            Console.WriteLine("✅ IndexDefinition 設為 null 時不會拋出例外");
        }

        private static void TestPopulateColumnsWithEmptyColumns()
        {
            Console.WriteLine("\n測試 3: IndexDefinition 有空的 Columns 集合時的處理");
            
            var indexInfo = new IndexEditorInfo
            {
                IndexName = "TEST_INDEX",
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE", 
                IsEditMode = true
            };

            var mockLogger = new TestLogger();
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                new MockDatabaseService(),
                new MockScriptGeneratorService(),
                new MockObjectEditorService(),
                () => null,
                mockLogger
            );

            // 創建有空 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>()
            };

            // 設定 IndexDefinition
            var indexDefinitionProperty = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            indexDefinitionProperty?.SetValue(viewModel, indexDefinition);

            Console.WriteLine("✅ 空的 Columns 集合處理正確");
            Console.WriteLine($"   - SelectedColumns 數量: {viewModel.SelectedColumns.Count}");
        }

        private static void TestPopulateColumnsWithValidColumns()
        {
            Console.WriteLine("\n測試 4: IndexDefinition 有有效 Columns 時的處理");
            
            var indexInfo = new IndexEditorInfo
            {
                IndexName = "TEST_INDEX",
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IsEditMode = true
            };

            var mockLogger = new TestLogger();
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                new MockDatabaseService(),
                new MockScriptGeneratorService(), 
                new MockObjectEditorService(),
                () => null,
                mockLogger
            );

            // 先添加一些可用欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("NAME");
            viewModel.AvailableColumns.Add("EMAIL");
            viewModel.AvailableColumns.Add("CREATED_DATE");

            // 創建有有效 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "ID", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "NAME", Position = 2 }
                }
            };

            Console.WriteLine($"   - 設定前 SelectedColumns 數量: {viewModel.SelectedColumns.Count}");
            Console.WriteLine($"   - 設定前 AvailableColumns 數量: {viewModel.AvailableColumns.Count}");

            // 設定 IndexDefinition（這會觸發 PopulateColumnsFromIndexDefinition）
            var indexDefinitionProperty = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            indexDefinitionProperty?.SetValue(viewModel, indexDefinition);

            Console.WriteLine($"   - 設定後 SelectedColumns 數量: {viewModel.SelectedColumns.Count}");
            Console.WriteLine($"   - 設定後 AvailableColumns 數量: {viewModel.AvailableColumns.Count}");

            // 驗證欄位順序
            if (viewModel.SelectedColumns.Count >= 2)
            {
                Console.WriteLine($"   - 第一個選中欄位: {viewModel.SelectedColumns[0]}");
                Console.WriteLine($"   - 第二個選中欄位: {viewModel.SelectedColumns[1]}");
                
                if (viewModel.SelectedColumns[0] == "ID" && viewModel.SelectedColumns[1] == "NAME")
                {
                    Console.WriteLine("✅ 欄位順序正確（按 Position 排序）");
                }
                else
                {
                    Console.WriteLine("❌ 欄位順序不正確");
                }
            }

            // 驗證欄位從 AvailableColumns 中移除
            if (!viewModel.AvailableColumns.Contains("ID") && !viewModel.AvailableColumns.Contains("NAME"))
            {
                Console.WriteLine("✅ 選中的欄位已從 AvailableColumns 中移除");
            }
            else
            {
                Console.WriteLine("❌ 選中的欄位未從 AvailableColumns 中移除");
            }
        }
    }

    // 測試用的 Mock 類別
    public class TestLogger : ILogger
    {
        public IDisposable BeginScope<TState>(TState state) => null;
        public bool IsEnabled(LogLevel logLevel) => true;
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception exception, Func<TState, Exception, string> formatter)
        {
            Console.WriteLine($"[LOG {logLevel}] {formatter(state, exception)}");
        }
    }

    public class MockDatabaseService : IDatabaseService
    {
        public Task<IEnumerable<string>> GetSchemasAsync(IDbConnection connection) => Task.FromResult(Enumerable.Empty<string>());
        public Task<IEnumerable<TableInfo>> GetTablesBySchemaAsync(IDbConnection connection, string schemaName) => Task.FromResult(Enumerable.Empty<TableInfo>());
        public Task<IEnumerable<string>> GetTableColumnsAsync(IDbConnection connection, string schemaName, string tableName) => Task.FromResult(Enumerable.Empty<string>());
        public Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string query) => Task.FromResult(new DataTable());
        public Task<int> ExecuteNonQueryAsync(IDbConnection connection, string query) => Task.FromResult(0);
        public Task<object> ExecuteScalarAsync(IDbConnection connection, string query) => Task.FromResult<object>(null);
    }

    public class MockScriptGeneratorService : IScriptGeneratorService
    {
        public string GenerateCreateTableScript(TableDefinition tableDefinition) => "";
        public string GenerateAlterTableScript(TableDefinition originalTable, TableDefinition modifiedTable) => "";
        public string GenerateDropTableScript(string schemaName, string tableName) => "";
        public string GenerateCreateIndexScript(IndexDefinition indexDefinition) => "";
        public string GenerateDropIndexScript(string schemaName, string indexName) => "";
        public string GenerateCreateViewScript(ViewDefinition viewDefinition) => "";
        public string GenerateDropViewScript(string schemaName, string viewName) => "";
        public string GenerateCreateProcedureScript(ProcedureDefinition procedureDefinition) => "";
        public string GenerateDropProcedureScript(string schemaName, string procedureName) => "";
        public string GenerateCreateFunctionScript(FunctionDefinition functionDefinition) => "";
        public string GenerateDropFunctionScript(string schemaName, string functionName) => "";
        public string GenerateCreatePackageScript(PackageDefinition packageDefinition) => "";
        public string GenerateDropPackageScript(string schemaName, string packageName) => "";
        public string GenerateCreateTriggerScript(TriggerDefinition triggerDefinition) => "";
        public string GenerateDropTriggerScript(string schemaName, string triggerName) => "";
    }

    public class MockObjectEditorService : IObjectEditorService
    {
        public Task<TableDefinition> GetTableDefinitionAsync(IDbConnection connection, string tableName, string schemaName = null) => Task.FromResult(new TableDefinition());
        public Task<IndexDefinition> GetIndexDefinitionAsync(IDbConnection connection, string indexName, string schemaName = null) => Task.FromResult(new IndexDefinition());
        public Task<ViewDefinition> GetViewDefinitionAsync(IDbConnection connection, string viewName, string schemaName = null) => Task.FromResult(new ViewDefinition());
        public Task<ProcedureDefinition> GetProcedureDefinitionAsync(IDbConnection connection, string procedureName, string schemaName = null) => Task.FromResult(new ProcedureDefinition());
        public Task<FunctionDefinition> GetFunctionDefinitionAsync(IDbConnection connection, string functionName, string schemaName = null) => Task.FromResult(new FunctionDefinition());
        public Task<PackageDefinition> GetPackageDefinitionAsync(IDbConnection connection, string packageName, string schemaName = null) => Task.FromResult(new PackageDefinition());
        public Task<TriggerDefinition> GetTriggerDefinitionAsync(IDbConnection connection, string triggerName, string schemaName = null) => Task.FromResult(new TriggerDefinition());
        public Task<bool> CreateTableAsync(IDbConnection connection, TableDefinition tableDefinition) => Task.FromResult(true);
        public Task<bool> AlterTableAsync(IDbConnection connection, TableDefinition originalTable, TableDefinition modifiedTable) => Task.FromResult(true);
        public Task<bool> DropTableAsync(IDbConnection connection, string tableName, string schemaName = null) => Task.FromResult(true);
        public Task<bool> CreateIndexAsync(IDbConnection connection, IndexDefinition indexDefinition) => Task.FromResult(true);
        public Task<bool> DropIndexAsync(IDbConnection connection, string indexName, string schemaName = null) => Task.FromResult(true);
        public Task<bool> CreateViewAsync(IDbConnection connection, ViewDefinition viewDefinition) => Task.FromResult(true);
        public Task<bool> DropViewAsync(IDbConnection connection, string viewName, string schemaName = null) => Task.FromResult(true);
        public Task<bool> CreateProcedureAsync(IDbConnection connection, ProcedureDefinition procedureDefinition) => Task.FromResult(true);
        public Task<bool> DropProcedureAsync(IDbConnection connection, string procedureName, string schemaName = null) => Task.FromResult(true);
        public Task<bool> CreateFunctionAsync(IDbConnection connection, FunctionDefinition functionDefinition) => Task.FromResult(true);
        public Task<bool> DropFunctionAsync(IDbConnection connection, string functionName, string schemaName = null) => Task.FromResult(true);
        public Task<bool> CreatePackageAsync(IDbConnection connection, PackageDefinition packageDefinition) => Task.FromResult(true);
        public Task<bool> DropPackageAsync(IDbConnection connection, string packageName, string schemaName = null) => Task.FromResult(true);
        public Task<bool> CreateTriggerAsync(IDbConnection connection, TriggerDefinition triggerDefinition) => Task.FromResult(true);
        public Task<bool> DropTriggerAsync(IDbConnection connection, string triggerName, string schemaName = null) => Task.FromResult(true);
    }
}