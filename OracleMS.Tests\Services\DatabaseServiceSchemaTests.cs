using System.Data;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Services;
using Xunit;

namespace OracleMS.Tests.Services;

/// <summary>
/// DatabaseService Schema 和 Table 查詢功能的單元測試
/// </summary>
public class DatabaseServiceSchemaTests
{
    private readonly Mock<IDatabaseRepository> _mockRepository;
    private readonly Mock<ILogger<DatabaseService>> _mockLogger;
    private readonly DatabaseService _databaseService;
    private readonly Mock<IDbConnection> _mockConnection;

    public DatabaseServiceSchemaTests()
    {
        _mockRepository = new Mock<IDatabaseRepository>();
        _mockLogger = new Mock<ILogger<DatabaseService>>();
        _databaseService = new DatabaseService(_mockRepository.Object, _mockLogger.Object);
        _mockConnection = new Mock<IDbConnection>();
        
        // 設定連線狀態為開啟
        _mockConnection.Setup(c => c.State).Returns(ConnectionState.Open);
    }

    [Fact]
    public async Task GetSchemasAsync_WithValidConnection_ReturnsSchemaList()
    {
        // Arrange
        var expectedSchemas = new List<string> { "SCHEMA1", "SCHEMA2", "SCHEMA3" };
        _mockRepository.Setup(r => r.GetSchemasAsync(_mockConnection.Object))
                      .ReturnsAsync(expectedSchemas);

        // Act
        var result = await _databaseService.GetSchemasAsync(_mockConnection.Object);

        // Assert
        Assert.NotNull(result);
        var schemaList = result.ToList();
        Assert.Equal(3, schemaList.Count);
        Assert.Contains("SCHEMA1", schemaList);
        Assert.Contains("SCHEMA2", schemaList);
        Assert.Contains("SCHEMA3", schemaList);
        
        _mockRepository.Verify(r => r.GetSchemasAsync(_mockConnection.Object), Times.Once);
    }

    [Fact]
    public async Task GetSchemasAsync_WithNullConnection_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _databaseService.GetSchemasAsync(null!));
    }

    [Fact]
    public async Task GetSchemasAsync_WithClosedConnection_ThrowsInvalidOperationException()
    {
        // Arrange
        _mockConnection.Setup(c => c.State).Returns(ConnectionState.Closed);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _databaseService.GetSchemasAsync(_mockConnection.Object));
    }

    [Fact]
    public async Task GetSchemasAsync_WhenRepositoryThrowsException_ThrowsOracleManagementException()
    {
        // Arrange
        _mockRepository.Setup(r => r.GetSchemasAsync(_mockConnection.Object))
                      .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<OracleManagementException>(() => 
            _databaseService.GetSchemasAsync(_mockConnection.Object));
        
        Assert.Contains("取得 Schema 清單失敗", exception.Message);
    }

    [Fact]
    public async Task GetTablesBySchemaAsync_WithValidParameters_ReturnsTableList()
    {
        // Arrange
        var schemaName = "TEST_SCHEMA";
        var expectedTables = new List<DatabaseObject>
        {
            new DatabaseObject
            {
                Name = "TABLE1",
                Owner = schemaName,
                Type = DatabaseObjectType.Table,
                Status = "VALID"
            },
            new DatabaseObject
            {
                Name = "TABLE2",
                Owner = schemaName,
                Type = DatabaseObjectType.Table,
                Status = "VALID"
            }
        };

        _mockRepository.Setup(r => r.GetTablesBySchemaAsync(_mockConnection.Object, schemaName))
                      .ReturnsAsync(expectedTables);

        // Act
        var result = await _databaseService.GetTablesBySchemaAsync(_mockConnection.Object, schemaName);

        // Assert
        Assert.NotNull(result);
        var tableList = result.ToList();
        Assert.Equal(2, tableList.Count);
        Assert.All(tableList, table => Assert.Equal(schemaName, table.Owner));
        Assert.All(tableList, table => Assert.Equal(DatabaseObjectType.Table, table.Type));
        
        _mockRepository.Verify(r => r.GetTablesBySchemaAsync(_mockConnection.Object, schemaName), Times.Once);
    }

    [Fact]
    public async Task GetTablesBySchemaAsync_WithNullConnection_ThrowsArgumentNullException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() => 
            _databaseService.GetTablesBySchemaAsync(null!, "SCHEMA"));
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public async Task GetTablesBySchemaAsync_WithInvalidSchemaName_ThrowsArgumentException(string schemaName)
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => 
            _databaseService.GetTablesBySchemaAsync(_mockConnection.Object, schemaName));
    }

    [Fact]
    public async Task GetTablesBySchemaAsync_WithClosedConnection_ThrowsInvalidOperationException()
    {
        // Arrange
        _mockConnection.Setup(c => c.State).Returns(ConnectionState.Closed);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _databaseService.GetTablesBySchemaAsync(_mockConnection.Object, "SCHEMA"));
    }

    [Fact]
    public async Task GetTablesBySchemaAsync_WhenRepositoryThrowsException_ThrowsOracleManagementException()
    {
        // Arrange
        var schemaName = "TEST_SCHEMA";
        _mockRepository.Setup(r => r.GetTablesBySchemaAsync(_mockConnection.Object, schemaName))
                      .ThrowsAsync(new Exception("Database error"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<OracleManagementException>(() => 
            _databaseService.GetTablesBySchemaAsync(_mockConnection.Object, schemaName));
        
        Assert.Contains($"取得 Schema {schemaName} 下的資料表清單失敗", exception.Message);
    }

    [Fact]
    public async Task GetTablesBySchemaAsync_WithEmptyResult_ReturnsEmptyList()
    {
        // Arrange
        var schemaName = "EMPTY_SCHEMA";
        var emptyTables = new List<DatabaseObject>();
        
        _mockRepository.Setup(r => r.GetTablesBySchemaAsync(_mockConnection.Object, schemaName))
                      .ReturnsAsync(emptyTables);

        // Act
        var result = await _databaseService.GetTablesBySchemaAsync(_mockConnection.Object, schemaName);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
        
        _mockRepository.Verify(r => r.GetTablesBySchemaAsync(_mockConnection.Object, schemaName), Times.Once);
    }
}