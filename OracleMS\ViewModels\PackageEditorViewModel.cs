using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Media;
using CommunityToolkit.Mvvm.Input;

using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 套件編輯器 ViewModel
    /// </summary>
    public class PackageEditorViewModel : BaseObjectEditorViewModel
    {
        private PackageDefinition _packageDefinition;
        private string _specificationSource = string.Empty;
        private string _bodySource = string.Empty;
        private int _selectedTabIndex;
        private string _specificationStatus = "未知";
        private string _bodyStatus = "未知";
        private DateTime _specificationLastCompiled;
        private DateTime _bodyLastCompiled;
        private bool _hasSpecificationError;
        private bool _hasBodyError;
        private string _specificationErrorMessage = string.Empty;
        private string _bodyErrorMessage = string.Empty;
        private string _compilationResults = string.Empty;
        private Brush _specificationStatusColor = Brushes.Gray;
        private Brush _bodyStatusColor = Brushes.Gray;
        private string _loadingMessage = "正在載入套件...";
        private bool _isSpecificationModified;
        private bool _isBodyModified;

        /// <summary>
        /// 套件定義
        /// </summary>
        public PackageDefinition PackageDefinition
        {
            get => _packageDefinition;
            private set => SetProperty(ref _packageDefinition, value);
        }

        /// <summary>
        /// 規格原始碼
        /// </summary>
        public string SpecificationSource
        {
            get => _specificationSource;
            set
            {
                if (SetProperty(ref _specificationSource, value))
                {
                    OnSpecificationTextChanged();
                }
            }
        }

        /// <summary>
        /// 主體原始碼
        /// </summary>
        public string BodySource
        {
            get => _bodySource;
            set
            {
                if (SetProperty(ref _bodySource, value))
                {
                    OnBodyTextChanged();
                }
            }
        }

        /// <summary>
        /// 選擇的分頁索引
        /// </summary>
        public int SelectedTabIndex
        {
            get => _selectedTabIndex;
            set => SetProperty(ref _selectedTabIndex, value);
        }

        /// <summary>
        /// 規格狀態
        /// </summary>
        public string SpecificationStatus
        {
            get => _specificationStatus;
            private set => SetProperty(ref _specificationStatus, value);
        }

        /// <summary>
        /// 主體狀態
        /// </summary>
        public string BodyStatus
        {
            get => _bodyStatus;
            private set => SetProperty(ref _bodyStatus, value);
        }

        /// <summary>
        /// 規格最後編譯時間
        /// </summary>
        public DateTime SpecificationLastCompiled
        {
            get => _specificationLastCompiled;
            private set => SetProperty(ref _specificationLastCompiled, value);
        }

        /// <summary>
        /// 主體最後編譯時間
        /// </summary>
        public DateTime BodyLastCompiled
        {
            get => _bodyLastCompiled;
            private set => SetProperty(ref _bodyLastCompiled, value);
        }

        /// <summary>
        /// 是否有規格錯誤
        /// </summary>
        public bool HasSpecificationError
        {
            get => _hasSpecificationError;
            private set => SetProperty(ref _hasSpecificationError, value);
        }

        /// <summary>
        /// 是否有主體錯誤
        /// </summary>
        public bool HasBodyError
        {
            get => _hasBodyError;
            private set => SetProperty(ref _hasBodyError, value);
        }

        /// <summary>
        /// 規格錯誤訊息
        /// </summary>
        public string SpecificationErrorMessage
        {
            get => _specificationErrorMessage;
            private set => SetProperty(ref _specificationErrorMessage, value);
        }

        /// <summary>
        /// 主體錯誤訊息
        /// </summary>
        public string BodyErrorMessage
        {
            get => _bodyErrorMessage;
            private set => SetProperty(ref _bodyErrorMessage, value);
        }

        /// <summary>
        /// 編譯結果
        /// </summary>
        public string CompilationResults
        {
            get => _compilationResults;
            private set => SetProperty(ref _compilationResults, value);
        }

        /// <summary>
        /// 規格狀態顏色
        /// </summary>
        public Brush SpecificationStatusColor
        {
            get => _specificationStatusColor;
            private set => SetProperty(ref _specificationStatusColor, value);
        }

        /// <summary>
        /// 主體狀態顏色
        /// </summary>
        public Brush BodyStatusColor
        {
            get => _bodyStatusColor;
            private set => SetProperty(ref _bodyStatusColor, value);
        }

        /// <summary>
        /// 載入訊息
        /// </summary>
        public string LoadingMessage
        {
            get => _loadingMessage;
            private set => SetProperty(ref _loadingMessage, value);
        }

        /// <summary>
        /// 編譯規格命令
        /// </summary>
        public ICommand CompileSpecCommand { get; }

        /// <summary>
        /// 編譯主體命令
        /// </summary>
        public ICommand CompileBodyCommand { get; }

        /// <summary>
        /// 編譯全部命令
        /// </summary>
        public ICommand CompileAllCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="packageName">套件名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public PackageEditorViewModel(
            string packageName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(packageName, DatabaseObjectType.Package, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            // 初始化套件定義
            _packageDefinition = new PackageDefinition { Name = packageName };

            // 初始化命令
            CompileSpecCommand = new AsyncRelayCommand(OnCompileSpecAsync, CanCompileSpec);
            CompileBodyCommand = new AsyncRelayCommand(OnCompileBodyAsync, CanCompileBody);
            CompileAllCommand = new AsyncRelayCommand(OnCompileAllAsync, CanCompileAll);
        }

        /// <summary>
        /// 規格文字變更事件處理
        /// </summary>
        public void OnSpecificationTextChanged()
        {
            if (!_isInitializing)
            {
                _isSpecificationModified = true;
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// 主體文字變更事件處理
        /// </summary>
        public void OnBodyTextChanged()
        {
            if (!_isInitializing)
            {
                _isBodyModified = true;
                HasUnsavedChanges = true;
            }
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 載入套件定義
            LoadingMessage = "正在載入套件定義...";
            _logger.LogInformation("開始載入套件定義: {PackageName}", ObjectName);

            var packageDefinition = await _objectEditorService.GetPackageDefinitionAsync(connection, ObjectName);
            _logger.LogInformation("從服務取得套件定義: {PackageName}, 規格長度: {SpecLength}, 主體長度: {BodyLength}",
                ObjectName, packageDefinition?.Specification?.Length ?? 0, packageDefinition?.Body?.Length ?? 0);

            PackageDefinition = packageDefinition;
            _logger.LogInformation("PackageDefinition 屬性已設置: {PackageName}, 規格長度: {SpecLength}, 主體長度: {BodyLength}",
                ObjectName, PackageDefinition?.Specification?.Length ?? 0, PackageDefinition?.Body?.Length ?? 0);

            // 更新原始碼內容
            _isInitializing = true;
            _logger.LogInformation("開始設置原始碼內容，_isInitializing = {IsInitializing}", _isInitializing);

            var specSource = PackageDefinition?.Specification ?? string.Empty;
            var bodySource = PackageDefinition?.Body ?? string.Empty;
            _logger.LogInformation("準備設置原始碼內容 - 規格長度: {SpecLength}, 主體長度: {BodyLength}", specSource.Length, bodySource.Length);

            SpecificationSource = specSource;
            _logger.LogInformation("規格原始碼已設置，實際長度: {ActualLength}", SpecificationSource?.Length ?? 0);

            BodySource = bodySource;
            _logger.LogInformation("主體原始碼已設置，實際長度: {ActualLength}", BodySource?.Length ?? 0);

            _isInitializing = false;
            _logger.LogInformation("原始碼內容設置完成，_isInitializing = {IsInitializing}", _isInitializing);

            // 更新狀態
            UpdateStatusColors();
            
            // 重置修改標記
            _isSpecificationModified = false;
            _isBodyModified = false;
            
            // 載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 更新套件定義
            PackageDefinition.Specification = SpecificationSource;
            PackageDefinition.Body = BodySource;

            // 編譯並儲存套件
            var result = await _objectEditorService.CompilePackageAsync(
                connection, 
                ObjectName, 
                PackageDefinition.Specification, 
                PackageDefinition.Body);

            if (result.IsSuccess)
            {
                // 重新載入套件定義以獲取最新狀態
                PackageDefinition = await _objectEditorService.GetPackageDefinitionAsync(connection, ObjectName);
                
                // 更新狀態
                UpdateStatusColors();
                
                // 更新編譯結果
                CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件儲存成功\n";
                
                // 重置修改標記
                _isSpecificationModified = false;
                _isBodyModified = false;
            }
            else
            {
                // 更新錯誤訊息
                if (result.ErrorMessage.Contains("PACKAGE SPEC"))
                {
                    HasSpecificationError = true;
                    SpecificationErrorMessage = result.ErrorMessage;
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件規格編譯失敗：{result.ErrorMessage}\n";
                }
                else if (result.ErrorMessage.Contains("PACKAGE BODY"))
                {
                    HasBodyError = true;
                    BodyErrorMessage = result.ErrorMessage;
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件主體編譯失敗：{result.ErrorMessage}\n";
                }
                else
                {
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件儲存失敗：{result.ErrorMessage}\n";
                }
                
                throw new Exception(result.ErrorMessage);
            }
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected override string GenerateScript()
        {
            var script = "";
            
            // 產生規格腳本
            if (!string.IsNullOrWhiteSpace(SpecificationSource))
            {
                script += $"-- Package Specification: {ObjectName}\n";
                script += SpecificationSource;
                script += "\n/\n\n";
            }

            // 產生主體腳本
            if (!string.IsNullOrWhiteSpace(BodySource))
            {
                script += $"-- Package Body: {ObjectName}\n";
                script += BodySource;
                script += "\n/\n";
            }
            
            return script;
        }

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            var result = new ValidationResult();

            // 驗證規格
            if (string.IsNullOrWhiteSpace(SpecificationSource))
            {
                result.AddError("套件規格不能為空");
            }
            else
            {
                var upperSpec = SpecificationSource.Trim().ToUpper();
                if (!upperSpec.StartsWith("CREATE OR REPLACE PACKAGE") &&
                    !upperSpec.StartsWith("CREATE PACKAGE"))
                {
                    result.AddError("套件規格必須以 CREATE [OR REPLACE] PACKAGE 開頭");
                }
            }

            // 驗證主體 (主體可以為空)
            if (!string.IsNullOrWhiteSpace(BodySource))
            {
                var upperBody = BodySource.Trim().ToUpper();
                if (!upperBody.StartsWith("CREATE OR REPLACE PACKAGE BODY") &&
                    !upperBody.StartsWith("CREATE PACKAGE BODY"))
                {
                    result.AddError("套件主體必須以 CREATE [OR REPLACE] PACKAGE BODY 開頭");
                }
            }

            return result;
        }

        /// <summary>
        /// 編譯套件規格
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnCompileSpecAsync()
        {
            if (string.IsNullOrWhiteSpace(SpecificationSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                LoadingMessage = "正在編譯套件規格...";
                HasSpecificationError = false;
                SpecificationErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 更新套件規格
                PackageDefinition.Specification = SpecificationSource;

                // 執行編譯
                var stopwatch = Stopwatch.StartNew();
                var result = await _objectEditorService.CompilePackageAsync(
                    connection, 
                    ObjectName, 
                    PackageDefinition.Specification, 
                    null);
                stopwatch.Stop();

                if (result.IsSuccess)
                {
                    // 重新載入套件定義以獲取最新狀態
                    PackageDefinition = await _objectEditorService.GetPackageDefinitionAsync(connection, ObjectName);
                    
                    // 更新狀態
                    UpdateStatusColors();
                    
                    // 更新編譯結果
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件規格編譯成功，耗時 {stopwatch.Elapsed.TotalSeconds:F2} 秒\n";
                    StatusMessage = "套件規格編譯成功";
                    
                    // 重置修改標記
                    _isSpecificationModified = false;
                    
                    // 如果主體也沒有修改，則整個套件都沒有未儲存的變更
                    HasUnsavedChanges = _isBodyModified;
                }
                else
                {
                    // 更新錯誤訊息
                    HasSpecificationError = true;
                    SpecificationErrorMessage = result.ErrorMessage;
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件規格編譯失敗：{result.ErrorMessage}\n";
                    StatusMessage = "套件規格編譯失敗";
                }
            }
            catch (Exception ex)
            {
                HasSpecificationError = true;
                SpecificationErrorMessage = ex.Message;
                CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件規格編譯失敗：{ex.Message}\n";
                StatusMessage = $"套件規格編譯失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以編譯規格
        /// </summary>
        /// <returns>是否可以編譯</returns>
        private bool CanCompileSpec()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(SpecificationSource) && _getConnection() != null;
        }

        /// <summary>
        /// 編譯套件主體
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnCompileBodyAsync()
        {
            if (string.IsNullOrWhiteSpace(BodySource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                LoadingMessage = "正在編譯套件主體...";
                HasBodyError = false;
                BodyErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 更新套件主體
                PackageDefinition.Body = BodySource;

                // 執行編譯
                var stopwatch = Stopwatch.StartNew();
                var result = await _objectEditorService.CompilePackageAsync(
                    connection, 
                    ObjectName, 
                    null, 
                    PackageDefinition.Body);
                stopwatch.Stop();

                if (result.IsSuccess)
                {
                    // 重新載入套件定義以獲取最新狀態
                    PackageDefinition = await _objectEditorService.GetPackageDefinitionAsync(connection, ObjectName);
                    
                    // 更新狀態
                    UpdateStatusColors();
                    
                    // 更新編譯結果
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件主體編譯成功，耗時 {stopwatch.Elapsed.TotalSeconds:F2} 秒\n";
                    StatusMessage = "套件主體編譯成功";
                    
                    // 重置修改標記
                    _isBodyModified = false;
                    
                    // 如果規格也沒有修改，則整個套件都沒有未儲存的變更
                    HasUnsavedChanges = _isSpecificationModified;
                }
                else
                {
                    // 更新錯誤訊息
                    HasBodyError = true;
                    BodyErrorMessage = result.ErrorMessage;
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件主體編譯失敗：{result.ErrorMessage}\n";
                    StatusMessage = "套件主體編譯失敗";
                }
            }
            catch (Exception ex)
            {
                HasBodyError = true;
                BodyErrorMessage = ex.Message;
                CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件主體編譯失敗：{ex.Message}\n";
                StatusMessage = $"套件主體編譯失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以編譯主體
        /// </summary>
        /// <returns>是否可以編譯</returns>
        private bool CanCompileBody()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(BodySource) && _getConnection() != null;
        }

        /// <summary>
        /// 編譯套件規格和主體
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnCompileAllAsync()
        {
            if ((string.IsNullOrWhiteSpace(SpecificationSource) && string.IsNullOrWhiteSpace(BodySource)) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                LoadingMessage = "正在編譯套件...";
                HasSpecificationError = false;
                HasBodyError = false;
                SpecificationErrorMessage = string.Empty;
                BodyErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 更新套件定義
                PackageDefinition.Specification = SpecificationSource;
                PackageDefinition.Body = BodySource;

                // 執行編譯
                var stopwatch = Stopwatch.StartNew();
                var result = await _objectEditorService.CompilePackageAsync(
                    connection, 
                    ObjectName, 
                    PackageDefinition.Specification, 
                    PackageDefinition.Body);
                stopwatch.Stop();

                if (result.IsSuccess)
                {
                    // 重新載入套件定義以獲取最新狀態
                    PackageDefinition = await _objectEditorService.GetPackageDefinitionAsync(connection, ObjectName);
                    
                    // 更新狀態
                    UpdateStatusColors();
                    
                    // 更新編譯結果
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件編譯成功，耗時 {stopwatch.Elapsed.TotalSeconds:F2} 秒\n";
                    StatusMessage = "套件編譯成功";
                    
                    // 重置修改標記
                    _isSpecificationModified = false;
                    _isBodyModified = false;
                    HasUnsavedChanges = false;
                }
                else
                {
                    // 更新錯誤訊息
                    if (result.ErrorMessage.Contains("PACKAGE SPEC"))
                    {
                        HasSpecificationError = true;
                        SpecificationErrorMessage = result.ErrorMessage;
                        CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件規格編譯失敗：{result.ErrorMessage}\n";
                    }
                    else if (result.ErrorMessage.Contains("PACKAGE BODY"))
                    {
                        HasBodyError = true;
                        BodyErrorMessage = result.ErrorMessage;
                        CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件主體編譯失敗：{result.ErrorMessage}\n";
                    }
                    else
                    {
                        CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件編譯失敗：{result.ErrorMessage}\n";
                    }
                    
                    StatusMessage = "套件編譯失敗";
                }
            }
            catch (Exception ex)
            {
                CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件編譯失敗：{ex.Message}\n";
                StatusMessage = $"套件編譯失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以編譯全部
        /// </summary>
        /// <returns>是否可以編譯</returns>
        private bool CanCompileAll()
        {
            return !IsLoading && !IsSaving &&
                   (!string.IsNullOrWhiteSpace(SpecificationSource) || !string.IsNullOrWhiteSpace(BodySource)) &&
                   _getConnection() != null;
        }

        /// <summary>
        /// 更新狀態顏色
        /// </summary>
        private void UpdateStatusColors()
        {
            // 更新規格狀態
            SpecificationStatus = PackageDefinition.SpecStatus;
            SpecificationLastCompiled = PackageDefinition.SpecCreated;
            
            if (string.IsNullOrWhiteSpace(SpecificationStatus) || SpecificationStatus == "未知")
            {
                SpecificationStatusColor = Brushes.Gray;
            }
            else if (SpecificationStatus.ToUpper() == "VALID")
            {
                SpecificationStatusColor = Brushes.Green;
            }
            else
            {
                SpecificationStatusColor = Brushes.Red;
            }
            
            // 更新主體狀態
            BodyStatus = PackageDefinition.BodyStatus;
            BodyLastCompiled = PackageDefinition.BodyCreated;
            
            if (string.IsNullOrWhiteSpace(BodyStatus) || BodyStatus == "未知")
            {
                BodyStatusColor = Brushes.Gray;
            }
            else if (BodyStatus.ToUpper() == "VALID")
            {
                BodyStatusColor = Brushes.Green;
            }
            else
            {
                BodyStatusColor = Brushes.Red;
            }
        }

        /// <summary>
        /// 載入相依性資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadDependenciesAsync(IDbConnection connection)
        {
            try
            {
                LoadingMessage = "正在載入相依性資訊...";
                
                var sql = $@"
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                        d.REFERENCED_TYPE AS Type,
                        d.REFERENCED_OWNER AS Owner,
                        'DEPENDS ON' AS DependencyType,
                        'This package depends on this object' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.NAME = '{ObjectName}'
                        AND d.TYPE = 'PACKAGE'
                    UNION ALL
                    SELECT 
                        d.NAME AS Name,
                        d.TYPE AS Type,
                        d.OWNER AS Owner,
                        'DEPENDENT' AS DependencyType,
                        'This object depends on this package' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.REFERENCED_NAME = '{ObjectName}'
                        AND d.REFERENCED_TYPE = 'PACKAGE'
                    ORDER BY 
                        DependencyType, Name";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                if (result.IsSuccess && result.Data != null)
                {
                    CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 套件相依性資訊：\n";
                    CompilationResults += "----------------------------------------------------\n";
                    CompilationResults += "物件名稱\t類型\t擁有者\t相依類型\n";
                    CompilationResults += "----------------------------------------------------\n";
                    
                    foreach (DataRow row in result.Data.Rows)
                    {
                        var name = row["Name"].ToString() ?? string.Empty;
                        var type = row["Type"].ToString() ?? string.Empty;
                        var owner = row["Owner"].ToString() ?? string.Empty;
                        var dependencyType = row["DependencyType"].ToString() ?? string.Empty;
                        
                        CompilationResults += $"{name}\t{type}\t{owner}\t{dependencyType}\n";
                    }
                    
                    CompilationResults += "----------------------------------------------------\n\n";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入相依性資訊失敗：{ex}");
                CompilationResults += $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 載入相依性資訊失敗：{ex.Message}\n";
            }
        }
    }
}