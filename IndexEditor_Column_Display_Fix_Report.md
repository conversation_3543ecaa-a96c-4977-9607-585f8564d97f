# IndexEditor 編輯模式欄位顯示修復報告

## 問題描述
**問題**：line 1141: IndexDefinition = definition; IndexDefinition已經成功取得索引欄位資料，但是卻沒有顯示在SelectedColumnsListBox之中

## 問題分析

### 根本原因
在 `IndexEditorViewModel` 的 `LoadIndexDefinitionAsync` 方法中，雖然成功設定了 `IndexDefinition`，但索引欄位沒有正確顯示在 `SelectedColumnsListBox` 中。

### 問題流程分析
1. `LoadIndexDefinitionAsync` 設定 `IndexDefinition`
2. 設定 `SelectedSchema`，觸發 `LoadTablesForSchemaAsync`
3. 背景執行 `LoadRelatedDataInBackground`
4. `LoadRelatedDataInBackground` 調用 `LoadColumnsForTableAsync`
5. **問題點**：`LoadColumnsForTableAsync` 需要 `SelectedSchema` 和 `SelectedTable` 都不為空
6. 當時 `SelectedTable` 可能還沒有被設定，導致方法提前返回
7. 因此 `UpdateColumnsUI` 中的編輯模式邏輯沒有被執行

### 關鍵代碼檢查
在 `LoadColumnsForTableAsync` 方法開始有這個檢查：
```csharp
if (string.IsNullOrEmpty(tableName) || string.IsNullOrEmpty(SelectedSchema))
{
    _logger.LogWarning("載入欄位失敗：Table 或 Schema 為空，Table: {TableName}, Schema: {Schema}", tableName, SelectedSchema);
    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
    {
        _columnSelectionManager.AvailableColumns.Clear();
        _columnSelectionManager.SelectedColumns.Clear();
        UpdateIndexDefinitionColumns();
    });
    return;
}
```

## 修復方案

### 修復位置
文件：`OracleMS/OracleMS/ViewModels/IndexEditorViewModel.cs`
方法：`LoadRelatedDataInBackground`
行數：約 2210-2220

### 修復內容
在 `LoadRelatedDataInBackground` 方法中，調用 `LoadColumnsForTableAsync` 之前，確保 `SelectedTable` 被正確設定：

```csharp
// 背景載入Columns (80-100%)
if (!string.IsNullOrEmpty(tableName))
{
    progress?.Report(("背景載入Columns...", 80));
    
    // 確保SelectedTable被設定，這樣LoadColumnsForTableAsync才能正常工作
    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
    {
        if (AvailableTables.Contains(tableName))
        {
            SelectedTable = tableName;
            _logger.LogInformation("背景載入：設定SelectedTable為 {TableName}", tableName);
        }
        else
        {
            _logger.LogWarning("背景載入：Table {TableName} 不在可用清單中，可用Tables: [{Tables}]", 
                tableName, string.Join(", ", AvailableTables.Take(5)));
        }
    });
    
    await LoadColumnsForTableAsync(tableName);
    progress?.Report(("Columns載入完成", 95));
}
```

### 修復邏輯說明
1. **檢查 Table 可用性**：確認 `tableName` 是否在 `AvailableTables` 中
2. **設定 SelectedTable**：如果 Table 可用，設定 `SelectedTable = tableName`
3. **日誌記錄**：記錄設定過程，便於調試
4. **錯誤處理**：如果 Table 不可用，記錄警告信息

## 修復後的完整流程
1. `LoadIndexDefinitionAsync` 設定 `IndexDefinition`
2. 設定 `SelectedSchema`，觸發 `LoadTablesForSchemaAsync`
3. 背景執行 `LoadRelatedDataInBackground`
4. **修復點**：確保 `SelectedTable` 被正確設定
5. 調用 `LoadColumnsForTableAsync`（現在 `SelectedSchema` 和 `SelectedTable` 都不為空）
6. 執行 `UpdateColumnsUI`
7. `UpdateColumnsUI` 中的編輯模式邏輯被正確執行：
   ```csharp
   // 如果是編輯模式且有預設的欄位，將它們移到已選欄位
   if (IsEditMode && IndexDefinition?.Columns?.Any() == true)
   {
       // 按照原始順序恢復已選欄位
       var orderedColumns = IndexDefinition.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName);
       foreach (var columnName in orderedColumns)
       {
           if (_columnSelectionManager.AvailableColumns.Contains(columnName))
           {
               _columnSelectionManager.MoveToSelected(columnName);
           }
       }
   }
   ```

## 驗證結果
修復後，編輯模式下：
- ✅ `IndexDefinition` 成功載入索引欄位資料
- ✅ `SelectedTable` 被正確設定
- ✅ `LoadColumnsForTableAsync` 正常執行
- ✅ 索引欄位正確顯示在 `SelectedColumnsListBox` 中

## 相關文件
- 主要修復文件：`OracleMS/OracleMS/ViewModels/IndexEditorViewModel.cs`
- 測試文件：`OracleMS/TestIndexEditorColumnFix.cs`
- 相關邏輯：`UpdateColumnsUI` 方法中的編輯模式處理

## 總結
這個修復解決了編輯模式下索引欄位不顯示的問題，確保了 `IndexDefinition` 中的欄位資料能夠正確填充到 `SelectedColumnsListBox` 中，提升了用戶體驗。