﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36212.18 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OracleMS", "OracleMS\OracleMS.csproj", "{C8D65691-60DA-46C3-A294-CC454513DF96}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "OracleMS.Tests", "OracleMS.Tests\OracleMS.Tests.csproj", "{D8D65691-60DA-46C3-A294-CC454513DF97}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C8D65691-60DA-46C3-A294-CC454513DF96}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C8D65691-60DA-46C3-A294-CC454513DF96}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C8D65691-60DA-46C3-A294-CC454513DF96}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C8D65691-60DA-46C3-A294-CC454513DF96}.Release|Any CPU.Build.0 = Release|Any CPU
		{D8D65691-60DA-46C3-A294-CC454513DF97}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D8D65691-60DA-46C3-A294-CC454513DF97}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D8D65691-60DA-46C3-A294-CC454513DF97}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D8D65691-60DA-46C3-A294-CC454513DF97}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {934CFB87-945E-49FC-AF59-BEEE11DF9A14}
	EndGlobalSection
EndGlobal
