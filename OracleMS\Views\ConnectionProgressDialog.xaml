<Window x:Class="OracleMS.Views.ConnectionProgressDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="連線中"
        Width="400"
        Height="250"
        WindowStartupLocation="CenterOwner"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        ResizeMode="NoResize"
        ShowInTaskbar="False"
        Topmost="True">
    
    <Border Background="White" 
            BorderBrush="#E0E0E0" 
            BorderThickness="1" 
            CornerRadius="8"
            Effect="{DynamicResource DropShadowEffect}">
        
        <Border.Resources>
            <DropShadowEffect x:Key="DropShadowEffect" 
                              Color="Black" 
                              Direction="270" 
                              ShadowDepth="3" 
                              BlurRadius="10" 
                              Opacity="0.3"/>
        </Border.Resources>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <Border Grid.Row="0" 
                    CornerRadius="8,8,0,0">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#007ACC" Offset="0"/>
                        <GradientStop Color="#005A9E" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Grid>
                    <TextBlock Text="正在連線到資料庫" 
                               Foreground="White" 
                               FontSize="16" 
                               FontWeight="SemiBold"
                               HorizontalAlignment="Center" 
                               VerticalAlignment="Center"/>
                    
                    <!-- Close button (hidden by default, can be shown for cancellation) -->
                    <Button x:Name="CloseButton"
                            Content="×"
                            Width="30"
                            Height="30"
                            FontSize="16"
                            FontWeight="Bold"
                            Background="Transparent"
                            Foreground="White"
                            BorderThickness="0"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Center"
                            Margin="0,0,10,0"
                            Cursor="Hand"
                            Visibility="Collapsed"
                            Click="CloseButton_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#40FFFFFF"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>
            </Border>
            
            <!-- Content -->
            <Grid Grid.Row="1" Margin="30,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="20"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="15"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Connection Icon -->
                <Viewbox Grid.Row="0" Width="48" Height="48" HorizontalAlignment="Center">
                    <Canvas Width="24" Height="24">
                        <!-- Database icon -->
                        <Path Fill="#007ACC" 
                              Data="M12,3C7.58,3 4,4.79 4,7V17C4,19.21 7.58,21 12,21C16.42,21 20,19.21 20,17V7C20,4.79 16.42,3 12,3M12,5C15.87,5 18,6.5 18,7C18,7.5 15.87,9 12,9C8.13,9 6,7.5 6,7C6,6.5 8.13,5 12,5M6,9.5C7.5,10.5 9.75,11 12,11C14.25,11 16.5,10.5 18,9.5V12C18,12.5 15.87,14 12,14C8.13,14 6,12.5 6,12V9.5M6,14.5C7.5,15.5 9.75,16 12,16C14.25,16 16.5,15.5 18,14.5V17C18,17.5 15.87,19 12,19C8.13,19 6,17.5 6,17V14.5Z"/>
                        
                        <!-- Animated connection waves -->
                        <Ellipse x:Name="Wave1" 
                                 Width="6" Height="6" 
                                 Fill="#007ACC" 
                                 Opacity="0.6"
                                 Canvas.Left="9" Canvas.Top="9">
                            <Ellipse.RenderTransform>
                                <ScaleTransform x:Name="Wave1Scale" ScaleX="1" ScaleY="1" CenterX="3" CenterY="3"/>
                            </Ellipse.RenderTransform>
                        </Ellipse>
                        
                        <Ellipse x:Name="Wave2" 
                                 Width="6" Height="6" 
                                 Fill="#007ACC" 
                                 Opacity="0.4"
                                 Canvas.Left="9" Canvas.Top="9">
                            <Ellipse.RenderTransform>
                                <ScaleTransform x:Name="Wave2Scale" ScaleX="1" ScaleY="1" CenterX="3" CenterY="3"/>
                            </Ellipse.RenderTransform>
                        </Ellipse>
                        
                        <Ellipse x:Name="Wave3" 
                                 Width="6" Height="6" 
                                 Fill="#007ACC" 
                                 Opacity="0.2"
                                 Canvas.Left="9" Canvas.Top="9">
                            <Ellipse.RenderTransform>
                                <ScaleTransform x:Name="Wave3Scale" ScaleX="1" ScaleY="1" CenterX="3" CenterY="3"/>
                            </Ellipse.RenderTransform>
                        </Ellipse>
                    </Canvas>
                </Viewbox>
                
                <!-- Connection Info -->
                <TextBlock x:Name="ConnectionNameText" 
                           Grid.Row="2"
                           Text="連線名稱"
                           FontSize="14"
                           FontWeight="SemiBold"
                           HorizontalAlignment="Center"
                           TextTrimming="CharacterEllipsis"/>
                
                <!-- Status Text -->
                <TextBlock x:Name="StatusText" 
                           Grid.Row="4"
                           Text="正在建立連線..."
                           FontSize="12"
                           Foreground="#666666"
                           HorizontalAlignment="Center"/>
            </Grid>
            
            <!-- Progress Bar -->
            <Grid Grid.Row="2" Margin="30,0,30,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="10"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- Progress Bar with Simple Animation -->
                <Grid x:Name="ProgressContainer" 
                      Grid.Row="0"
                      Width="300" 
                      Height="10" 
                      Background="LightGray" 
                      ClipToBounds="True">
                    <Rectangle x:Name="ProgressBar" 
                               Width="300" 
                               Height="10" 
                               Fill="DodgerBlue" 
                               Opacity="0">
                        <Rectangle.RenderTransform>
                            <TranslateTransform x:Name="ProgressTransform" X="-300"/>
                        </Rectangle.RenderTransform>
                    </Rectangle>
                    <Grid.Triggers>
                        <EventTrigger RoutedEvent="Grid.Loaded">
                            <BeginStoryboard>
                                <Storyboard RepeatBehavior="Forever">
                                    <!-- 位移動畫 -->
                                    <DoubleAnimation RepeatBehavior="Forever" Storyboard.TargetName="ProgressTransform" 
                                                     Storyboard.TargetProperty="X" 
                                                     From="-300" To="0" 
                                                     Duration="0:0:20" 
                                                     AccelerationRatio="0.4" 
                                                     DecelerationRatio="0.3"/>
                                    <!-- 透明度動畫 -->
                                    <DoubleAnimation Storyboard.TargetName="ProgressBar" 
                                                     Storyboard.TargetProperty="Opacity" 
                                                     From="0" To="1" 
                                                     Duration="0:0:2"/>
                                </Storyboard>
                            </BeginStoryboard>
                        </EventTrigger>
                    </Grid.Triggers>
                </Grid>
                
                <!-- Cancel Button (initially hidden) -->
                <Button x:Name="CancelButton"
                        Grid.Row="2"
                        Content="取消"
                        Width="80"
                        Height="28"
                        HorizontalAlignment="Center"
                        Background="#F5F5F5"
                        BorderBrush="#D0D0D0"
                        BorderThickness="1"
                        Cursor="Hand"
                        Visibility="Collapsed"
                        Click="CancelButton_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#E5E5E5"/>
                                    <Setter Property="BorderBrush" Value="#C0C0C0"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Grid>
    </Border>
    
    <!-- Animations -->
    <Window.Resources>
        <Storyboard x:Key="WaveAnimation" RepeatBehavior="Forever">
            <!-- Wave 1 -->
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave1Scale" 
                                           Storyboard.TargetProperty="ScaleX">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave1Scale" 
                                           Storyboard.TargetProperty="ScaleY">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave1" 
                                           Storyboard.TargetProperty="Opacity">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="0.6"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="0.6"/>
            </DoubleAnimationUsingKeyFrames>
            
            <!-- Wave 2 -->
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave2Scale" 
                                           Storyboard.TargetProperty="ScaleX"
                                           BeginTime="0:0:0.3">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave2Scale" 
                                           Storyboard.TargetProperty="ScaleY"
                                           BeginTime="0:0:0.3">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave2" 
                                           Storyboard.TargetProperty="Opacity"
                                           BeginTime="0:0:0.3">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="0.4"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="0.4"/>
            </DoubleAnimationUsingKeyFrames>
            
            <!-- Wave 3 -->
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave3Scale" 
                                           Storyboard.TargetProperty="ScaleX"
                                           BeginTime="0:0:0.6">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave3Scale" 
                                           Storyboard.TargetProperty="ScaleY"
                                           BeginTime="0:0:0.6">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="1"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="1"/>
            </DoubleAnimationUsingKeyFrames>
            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="Wave3" 
                                           Storyboard.TargetProperty="Opacity"
                                           BeginTime="0:0:0.6">
                <EasingDoubleKeyFrame KeyTime="0:0:0" Value="0.2"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:2" Value="0.2"/>
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
        
        <!-- Fade in animation -->
        <Storyboard x:Key="FadeInAnimation">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="0" To="1" Duration="0:0:0.3"/>
        </Storyboard>
        
        <!-- Fade out animation -->
        <Storyboard x:Key="FadeOutAnimation" Completed="FadeOutAnimation_Completed">
            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                             From="1" To="0" Duration="0:0:0.2"/>
        </Storyboard>
        

    </Window.Resources>
</Window>