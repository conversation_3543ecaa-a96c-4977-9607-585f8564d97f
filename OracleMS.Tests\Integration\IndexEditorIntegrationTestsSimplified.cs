using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using Xunit;

namespace OracleMS.Tests.Integration
{
    /// <summary>
    /// IndexEditor 整合測試 - 簡化版本，專注於核心業務邏輯測試
    /// </summary>
    public class IndexEditorIntegrationTestsSimplified
    {
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<IScriptGeneratorService> _mockScriptGeneratorService;
        private readonly Mock<IObjectEditorService> _mockObjectEditorService;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<ILogger> _mockLogger;

        public IndexEditorIntegrationTestsSimplified()
        {
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            _mockObjectEditorService = new Mock<IObjectEditorService>();
            _mockConnection = new Mock<IDbConnection>();
            _mockLogger = new Mock<ILogger>();

            SetupMockServices();
        }

        private void SetupMockServices()
        {
            // 設定資料庫服務模擬
            _mockDatabaseService.Setup(x => x.GetSchemasAsync(It.IsAny<IDbConnection>()))
                .ReturnsAsync(new List<string> { "SCHEMA1", "SCHEMA2", "TEST_SCHEMA" });

            _mockDatabaseService.Setup(x => x.GetTablesBySchemaAsync(It.IsAny<IDbConnection>(), "TEST_SCHEMA"))
                .ReturnsAsync(new List<DatabaseObject> 
                { 
                    new DatabaseObject { Name = "TABLE1", Type = DatabaseObjectType.Table },
                    new DatabaseObject { Name = "TABLE2", Type = DatabaseObjectType.Table },
                    new DatabaseObject { Name = "USERS", Type = DatabaseObjectType.Table }
                });

            // 設定物件編輯服務模擬
            _mockObjectEditorService.Setup(x => x.CreateIndexAsync(It.IsAny<IDbConnection>(), It.IsAny<IndexDefinition>()))
                .Returns(Task.CompletedTask);

            _mockObjectEditorService.Setup(x => x.UpdateIndexAsync(It.IsAny<IDbConnection>(), It.IsAny<IndexDefinition>()))
                .Returns(Task.CompletedTask);
        }

        /// <summary>
        /// 測試創建模式的初始化和基本狀態
        /// Requirements: 1.1, 4.1
        /// </summary>
        [Fact]
        public async Task CreateModeInitialization_ShouldSetCorrectInitialState()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = null,
                TableName = null,
                IndexName = null,
                IsEditMode = false,
                Columns = new List<string>()
            };

            // Act
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50); // 等待初始化

            // Assert
            Assert.True(viewModel.IsCreateMode);
            Assert.False(viewModel.IsEditMode);
            Assert.Empty(viewModel.SelectedSchema);
            Assert.Empty(viewModel.SelectedTable);
            Assert.Empty(viewModel.IndexDefinition.Name);
            Assert.NotNull(viewModel.AvailableSchemas);
            Assert.NotNull(viewModel.AvailableTables);
            Assert.NotNull(viewModel.AvailableColumns);
            Assert.NotNull(viewModel.SelectedColumns);
        }

        /// <summary>
        /// 測試編輯模式的初始化和基本狀態
        /// Requirements: 1.2, 4.2
        /// </summary>
        [Fact]
        public async Task EditModeInitialization_ShouldSetCorrectInitialState()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "USERS",
                IndexName = "IDX_EXISTING_INDEX",
                IsEditMode = true,
                IsUnique = false,
                Columns = new List<string> { "USERNAME", "EMAIL" }
            };

            // Act
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50); // 等待初始化

            // Assert
            Assert.False(viewModel.IsCreateMode);
            Assert.True(viewModel.IsEditMode);
            Assert.Equal("TEST_SCHEMA", viewModel.SelectedSchema);
            Assert.Equal("USERS", viewModel.SelectedTable);
            Assert.Equal("IDX_EXISTING_INDEX", viewModel.IndexDefinition.Name);
            Assert.Equal("TEST_SCHEMA", viewModel.IndexDefinition.Owner);
            Assert.Equal("USERS", viewModel.IndexDefinition.TableName);
            Assert.False(viewModel.IndexDefinition.IsUnique);
        }

        /// <summary>
        /// 測試欄位選擇操作的基本功能
        /// Requirements: 2.1, 2.2, 2.3, 2.4
        /// </summary>
        [Fact]
        public async Task ColumnSelectionOperations_ShouldWorkCorrectly()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "USERS",
                IndexName = "IDX_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 手動設定一些測試欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("USERNAME");
            viewModel.AvailableColumns.Add("EMAIL");

            // Act & Assert - 測試新增欄位
            Assert.True(viewModel.AddColumnCommand.CanExecute("USERNAME"));
            viewModel.AddColumnCommand.Execute("USERNAME");

            Assert.Contains("USERNAME", viewModel.SelectedColumns);
            Assert.DoesNotContain("USERNAME", viewModel.AvailableColumns);
            Assert.Single(viewModel.SelectedColumns);

            // 測試新增第二個欄位
            viewModel.AddColumnCommand.Execute("EMAIL");
            Assert.Equal(2, viewModel.SelectedColumns.Count);
            Assert.Equal("USERNAME", viewModel.SelectedColumns[0]);
            Assert.Equal("EMAIL", viewModel.SelectedColumns[1]);

            // 測試移除欄位
            Assert.True(viewModel.RemoveColumnCommand.CanExecute("USERNAME"));
            viewModel.RemoveColumnCommand.Execute("USERNAME");

            Assert.DoesNotContain("USERNAME", viewModel.SelectedColumns);
            Assert.Contains("USERNAME", viewModel.AvailableColumns);
            Assert.Single(viewModel.SelectedColumns);

            // 測試欄位順序調整（需要至少兩個欄位）
            viewModel.AddColumnCommand.Execute("ID");
            Assert.Equal(2, viewModel.SelectedColumns.Count);

            // 測試上移（第二個欄位可以上移）
            Assert.True(viewModel.MoveColumnUpCommand.CanExecute("ID"));
            viewModel.MoveColumnUpCommand.Execute("ID");

            Assert.Equal("ID", viewModel.SelectedColumns[0]);
            Assert.Equal("EMAIL", viewModel.SelectedColumns[1]);

            // 測試下移
            Assert.True(viewModel.MoveColumnDownCommand.CanExecute("ID"));
            viewModel.MoveColumnDownCommand.Execute("ID");

            Assert.Equal("EMAIL", viewModel.SelectedColumns[0]);
            Assert.Equal("ID", viewModel.SelectedColumns[1]);
        }

        /// <summary>
        /// 測試索引定義驗證功能
        /// Requirements: 3.1, 3.2
        /// </summary>
        [Fact]
        public async Task IndexDefinitionValidation_ShouldWorkCorrectly()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "USERS",
                IndexName = "",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // Act & Assert - 測試空名稱驗證
            var validationResult = viewModel.IndexDefinition.Validate();
            Assert.False(validationResult.IsValid);
            Assert.Contains("索引名稱不能為空", validationResult.Errors);

            // 設定名稱但沒有欄位
            viewModel.IndexDefinition.Name = "IDX_TEST";
            validationResult = viewModel.IndexDefinition.Validate();
            Assert.False(validationResult.IsValid);
            Assert.Contains("至少需要選擇一個欄位", validationResult.Errors);

            // 新增欄位後應該通過驗證
            viewModel.AvailableColumns.Add("USERNAME");
            viewModel.AddColumnCommand.Execute("USERNAME");
            
            validationResult = viewModel.IndexDefinition.Validate();
            Assert.True(validationResult.IsValid);

            // 測試警告情況（名稱不符合慣例）
            viewModel.IndexDefinition.Name = "BADNAME";
            validationResult = viewModel.IndexDefinition.Validate();
            Assert.True(validationResult.IsValid); // 仍然有效
            Assert.True(validationResult.HasWarnings); // 但有警告
        }

        /// <summary>
        /// 測試命令可用性狀態
        /// Requirements: 4.1, 4.2, 4.3
        /// </summary>
        [Fact]
        public async Task CommandAvailability_ShouldReflectCurrentState()
        {
            // 測試創建模式
            var createModeInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "USERS",
                IndexName = "IDX_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var createViewModel = new IndexEditorViewModel(
                createModeInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 創建模式下，更新命令應該不可用
            Assert.False(createViewModel.UpdateIndexCommand.CanExecute(null));

            // 測試編輯模式
            var editModeInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "USERS",
                IndexName = "IDX_EXISTING",
                IsEditMode = true,
                Columns = new List<string> { "USERNAME" }
            };

            var editViewModel = new IndexEditorViewModel(
                editModeInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 編輯模式下，創建命令應該不可用
            Assert.False(editViewModel.CreateIndexCommand.CanExecute(null));

            // 欄位選擇命令的可用性測試
            editViewModel.AvailableColumns.Add("EMAIL");
            Assert.True(editViewModel.AddColumnCommand.CanExecute("EMAIL"));
            Assert.False(editViewModel.AddColumnCommand.CanExecute("NON_EXISTENT"));
            Assert.False(editViewModel.AddColumnCommand.CanExecute(null));
            Assert.False(editViewModel.AddColumnCommand.CanExecute(""));
        }

        /// <summary>
        /// 測試Schema和Table選擇的聯動效果
        /// Requirements: 1.3
        /// </summary>
        [Fact]
        public async Task SchemaTableSelection_ShouldCascadeCorrectly()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = null,
                TableName = null,
                IndexName = "IDX_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // Act & Assert - 測試Schema選擇
            Assert.Contains("TEST_SCHEMA", viewModel.AvailableSchemas);
            
            viewModel.SelectedSchema = "TEST_SCHEMA";
            Assert.Equal("TEST_SCHEMA", viewModel.SelectedSchema);

            // 等待非同步載入
            await Task.Delay(100);

            // 驗證Table列表更新（這取決於實際的非同步載入實作）
            // 在實際測試中，這裡會驗證AvailableTables是否正確更新
        }

        /// <summary>
        /// 測試索引定義與UI狀態的同步
        /// Requirements: 2.1, 2.2, 2.3, 2.4
        /// </summary>
        [Fact]
        public async Task IndexDefinitionUISync_ShouldMaintainConsistency()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "USERS",
                IndexName = "IDX_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 手動設定測試欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("USERNAME");
            viewModel.AvailableColumns.Add("EMAIL");

            // Act - 新增欄位並驗證IndexDefinition同步更新
            viewModel.AddColumnCommand.Execute("USERNAME");
            viewModel.AddColumnCommand.Execute("EMAIL");

            // Assert - 驗證IndexDefinition的Columns集合正確更新
            Assert.Equal(2, viewModel.IndexDefinition.Columns.Count);
            Assert.Equal("USERNAME", viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal("EMAIL", viewModel.IndexDefinition.Columns[1].ColumnName);
            Assert.Equal(1, viewModel.IndexDefinition.Columns[0].Position);
            Assert.Equal(2, viewModel.IndexDefinition.Columns[1].Position);

            // 測試欄位順序調整後的同步
            viewModel.MoveColumnUpCommand.Execute("EMAIL");

            Assert.Equal("EMAIL", viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal("USERNAME", viewModel.IndexDefinition.Columns[1].ColumnName);
            Assert.Equal(1, viewModel.IndexDefinition.Columns[0].Position);
            Assert.Equal(2, viewModel.IndexDefinition.Columns[1].Position);

            // 測試移除欄位後的同步
            viewModel.RemoveColumnCommand.Execute("USERNAME");

            Assert.Single(viewModel.IndexDefinition.Columns);
            Assert.Equal("EMAIL", viewModel.IndexDefinition.Columns[0].ColumnName);
            Assert.Equal(1, viewModel.IndexDefinition.Columns[0].Position);
        }
    }
}