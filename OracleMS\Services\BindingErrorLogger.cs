using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Windows;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;

namespace OracleMS.Services
{
    /// <summary>
    /// Implementation of IBindingErrorLogger that logs binding errors to debug output and a log file
    /// </summary>
    public class BindingErrorLogger : IBindingErrorLogger
    {
        private readonly ILogger _logger;
        private const string LOG_FILE_NAME = "binding_errors.log";
        private static readonly string LOG_FILE_PATH = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", LOG_FILE_NAME);

        public BindingErrorLogger(ILogger logger = null)
        {
            _logger = logger;
            
            // Ensure logs directory exists
            var logsDir = Path.GetDirectoryName(LOG_FILE_PATH);
            if (!Directory.Exists(logsDir))
            {
                Directory.CreateDirectory(logsDir);
            }
        }

        /// <summary>
        /// Log a binding error
        /// </summary>
        /// <param name="source">The source object where the binding error occurred</param>
        /// <param name="errorMessage">The error message</param>
        public void LogBindingError(object source, string errorMessage)
        {
            var sourceType = source?.GetType().Name ?? "Unknown";
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var message = $"[{timestamp}] [ERROR] [{sourceType}] {errorMessage}";
            
            // Log to debug output
            Debug.WriteLine(message);
            
            // Log to file
            LogToFile(message);
            
            // Log to ILogger if available
            _logger?.LogError(errorMessage);
        }

        /// <summary>
        /// Log a binding error with exception details
        /// </summary>
        /// <param name="source">The source object where the binding error occurred</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="exception">The exception that occurred</param>
        public void LogBindingError(object source, string errorMessage, Exception exception)
        {
            var sourceType = source?.GetType().Name ?? "Unknown";
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var message = $"[{timestamp}] [ERROR] [{sourceType}] {errorMessage}\nException: {exception.GetType().Name}: {exception.Message}\nStack Trace: {exception.StackTrace}";
            
            // Log to debug output
            Debug.WriteLine(message);
            
            // Log to file
            LogToFile(message);
            
            // Log to ILogger if available
            _logger?.LogError(exception, errorMessage);
        }

        /// <summary>
        /// Log a binding success
        /// </summary>
        /// <param name="source">The source object where the binding succeeded</param>
        /// <param name="message">The success message</param>
        public void LogBindingSuccess(object source, string message)
        {
            var sourceType = source?.GetType().Name ?? "Unknown";
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logMessage = $"[{timestamp}] [INFO] [{sourceType}] {message}";
            
            // Log to debug output
            Debug.WriteLine(logMessage);
            
            // Log to file
            LogToFile(logMessage);
            
            // Log to ILogger if available
            _logger?.LogInformation(message);
        }

        /// <summary>
        /// Log a message to the binding errors log file
        /// </summary>
        /// <param name="message">The message to log</param>
        private void LogToFile(string message)
        {
            try
            {
                // Append to log file
                using (var writer = new StreamWriter(LOG_FILE_PATH, true, Encoding.UTF8))
                {
                    writer.WriteLine(message);
                }
            }
            catch (Exception ex)
            {
                // If we can't write to the log file, at least output to debug
                Debug.WriteLine($"Failed to write to binding error log file: {ex.Message}");
            }
        }
    }
}