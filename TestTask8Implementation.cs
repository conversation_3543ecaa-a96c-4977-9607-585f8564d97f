using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using OracleMS.Models;
using OracleMS.ViewModels;
using OracleMS.Views;

namespace OracleMS
{
    /// <summary>
    /// 測試任務8：實作模式切換的UI狀態控制
    /// </summary>
    public class TestTask8Implementation
    {
        public static async Task RunTests()
        {
            Console.WriteLine("=== 測試任務8：實作模式切換的UI狀態控制 ===");
            Console.WriteLine();

            try
            {
                // 測試1：驗證新增模式的UI狀態
                await TestCreateModeUIState();

                // 測試2：驗證編輯模式的UI狀態
                await TestEditModeUIState();

                // 測試3：驗證標題文字動態顯示
                await TestDynamicTitleDisplay();

                // 測試4：驗證視覺指示器
                await TestVisualIndicators();

                // 測試5：驗證基本資訊欄位的唯讀狀態
                await TestReadOnlyFieldsInEditMode();

                Console.WriteLine("✅ 所有測試通過！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 測試失敗: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 測試新增模式的UI狀態
        /// </summary>
        private static async Task TestCreateModeUIState()
        {
            Console.WriteLine("測試1：驗證新增模式的UI狀態");

            // 創建新增模式的IndexEditorInfo
            var createInfo = new IndexEditorInfo
            {
                IsEditMode = false,
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = null
            };

            // 模擬ViewModel的創建模式狀態
            var isCreateMode = !createInfo.IsEditMode;
            var isEditMode = createInfo.IsEditMode;

            // 驗證模式狀態
            if (!isCreateMode)
                throw new Exception("新增模式狀態驗證失敗：IsCreateMode應為true");

            if (isEditMode)
                throw new Exception("新增模式狀態驗證失敗：IsEditMode應為false");

            // 驗證UI控制項應該可編輯
            var shouldBeEditable = !isEditMode;
            if (!shouldBeEditable)
                throw new Exception("新增模式下控制項應該可編輯");

            Console.WriteLine("  ✅ 新增模式UI狀態正確");
            Console.WriteLine($"     - IsCreateMode: {isCreateMode}");
            Console.WriteLine($"     - IsEditMode: {isEditMode}");
            Console.WriteLine($"     - 控制項可編輯: {shouldBeEditable}");
            Console.WriteLine();
        }

        /// <summary>
        /// 測試編輯模式的UI狀態
        /// </summary>
        private static async Task TestEditModeUIState()
        {
            Console.WriteLine("測試2：驗證編輯模式的UI狀態");

            // 創建編輯模式的IndexEditorInfo
            var editInfo = new IndexEditorInfo
            {
                IsEditMode = true,
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX"
            };

            // 模擬ViewModel的編輯模式狀態
            var isCreateMode = !editInfo.IsEditMode;
            var isEditMode = editInfo.IsEditMode;

            // 驗證模式狀態
            if (isCreateMode)
                throw new Exception("編輯模式狀態驗證失敗：IsCreateMode應為false");

            if (!isEditMode)
                throw new Exception("編輯模式狀態驗證失敗：IsEditMode應為true");

            // 驗證基本資訊欄位應該唯讀
            var basicInfoShouldBeReadOnly = isEditMode;
            if (!basicInfoShouldBeReadOnly)
                throw new Exception("編輯模式下基本資訊欄位應該唯讀");

            Console.WriteLine("  ✅ 編輯模式UI狀態正確");
            Console.WriteLine($"     - IsCreateMode: {isCreateMode}");
            Console.WriteLine($"     - IsEditMode: {isEditMode}");
            Console.WriteLine($"     - 基本資訊欄位唯讀: {basicInfoShouldBeReadOnly}");
            Console.WriteLine();
        }

        /// <summary>
        /// 測試標題文字動態顯示
        /// </summary>
        private static async Task TestDynamicTitleDisplay()
        {
            Console.WriteLine("測試3：驗證標題文字動態顯示");

            // 測試新增模式標題
            var createModeTitle = GetTitleForMode(false);
            if (createModeTitle != "新增索引")
                throw new Exception($"新增模式標題錯誤：期望'新增索引'，實際'{createModeTitle}'");

            // 測試編輯模式標題
            var editModeTitle = GetTitleForMode(true);
            if (editModeTitle != "編輯索引")
                throw new Exception($"編輯模式標題錯誤：期望'編輯索引'，實際'{editModeTitle}'");

            Console.WriteLine("  ✅ 標題文字動態顯示正確");
            Console.WriteLine($"     - 新增模式標題: {createModeTitle}");
            Console.WriteLine($"     - 編輯模式標題: {editModeTitle}");
            Console.WriteLine();
        }

        /// <summary>
        /// 測試視覺指示器
        /// </summary>
        private static async Task TestVisualIndicators()
        {
            Console.WriteLine("測試4：驗證視覺指示器");

            // 測試新增模式指示器
            var createModeIndicator = GetModeIndicatorText(false);
            var createModeColor = GetModeIndicatorColor(false);

            if (createModeIndicator != "新增模式")
                throw new Exception($"新增模式指示器文字錯誤：期望'新增模式'，實際'{createModeIndicator}'");

            if (createModeColor != "Green")
                throw new Exception($"新增模式指示器顏色錯誤：期望'Green'，實際'{createModeColor}'");

            // 測試編輯模式指示器
            var editModeIndicator = GetModeIndicatorText(true);
            var editModeColor = GetModeIndicatorColor(true);

            if (editModeIndicator != "編輯模式")
                throw new Exception($"編輯模式指示器文字錯誤：期望'編輯模式'，實際'{editModeIndicator}'");

            if (editModeColor != "Blue")
                throw new Exception($"編輯模式指示器顏色錯誤：期望'Blue'，實際'{editModeColor}'");

            Console.WriteLine("  ✅ 視覺指示器正確");
            Console.WriteLine($"     - 新增模式: {createModeIndicator} ({createModeColor})");
            Console.WriteLine($"     - 編輯模式: {editModeIndicator} ({editModeColor})");
            Console.WriteLine();
        }

        /// <summary>
        /// 測試基本資訊欄位的唯讀狀態
        /// </summary>
        private static async Task TestReadOnlyFieldsInEditMode()
        {
            Console.WriteLine("測試5：驗證基本資訊欄位的唯讀狀態");

            // 測試新增模式下欄位可編輯
            var createModeFieldsEditable = GetFieldEditableState(false);
            if (!createModeFieldsEditable.SchemaEditable || !createModeFieldsEditable.TableEditable || !createModeFieldsEditable.IndexNameEditable)
                throw new Exception("新增模式下基本資訊欄位應該可編輯");

            // 測試編輯模式下欄位唯讀
            var editModeFieldsEditable = GetFieldEditableState(true);
            if (editModeFieldsEditable.SchemaEditable || editModeFieldsEditable.TableEditable || editModeFieldsEditable.IndexNameEditable)
                throw new Exception("編輯模式下基本資訊欄位應該唯讀");

            Console.WriteLine("  ✅ 基本資訊欄位唯讀狀態正確");
            Console.WriteLine($"     - 新增模式 - Schema可編輯: {createModeFieldsEditable.SchemaEditable}");
            Console.WriteLine($"     - 新增模式 - Table可編輯: {createModeFieldsEditable.TableEditable}");
            Console.WriteLine($"     - 新增模式 - IndexName可編輯: {createModeFieldsEditable.IndexNameEditable}");
            Console.WriteLine($"     - 編輯模式 - Schema可編輯: {editModeFieldsEditable.SchemaEditable}");
            Console.WriteLine($"     - 編輯模式 - Table可編輯: {editModeFieldsEditable.TableEditable}");
            Console.WriteLine($"     - 編輯模式 - IndexName可編輯: {editModeFieldsEditable.IndexNameEditable}");
            Console.WriteLine();
        }

        /// <summary>
        /// 根據模式獲取標題文字
        /// </summary>
        private static string GetTitleForMode(bool isEditMode)
        {
            return isEditMode ? "編輯索引" : "新增索引";
        }

        /// <summary>
        /// 根據模式獲取指示器文字
        /// </summary>
        private static string GetModeIndicatorText(bool isEditMode)
        {
            return isEditMode ? "編輯模式" : "新增模式";
        }

        /// <summary>
        /// 根據模式獲取指示器顏色
        /// </summary>
        private static string GetModeIndicatorColor(bool isEditMode)
        {
            return isEditMode ? "Blue" : "Green";
        }

        /// <summary>
        /// 根據模式獲取欄位可編輯狀態
        /// </summary>
        private static (bool SchemaEditable, bool TableEditable, bool IndexNameEditable) GetFieldEditableState(bool isEditMode)
        {
            // 在編輯模式下，基本資訊欄位應該唯讀
            var editable = !isEditMode;
            return (editable, editable, editable);
        }
    }
}