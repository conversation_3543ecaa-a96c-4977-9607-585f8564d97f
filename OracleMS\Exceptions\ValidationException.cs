using System;

namespace OracleMS.Exceptions
{
    /// <summary>
    /// 驗證例外類別
    /// </summary>
    public class ValidationException : Exception
    {
        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="message">錯誤訊息</param>
        public ValidationException(string message) : base(message)
        {
        }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="message">錯誤訊息</param>
        /// <param name="innerException">內部例外</param>
        public ValidationException(string message, Exception innerException) : base(message, innerException)
        {
        }
    }
}