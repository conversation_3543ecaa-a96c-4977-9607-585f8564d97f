using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.DependencyInjection;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using System;
using System.ComponentModel;
using System.Data;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;

namespace OracleMS.Views;

public partial class ConnectionEditDialog : Window, INotifyPropertyChanged
{
    public ConnectionInfo ConnectionInfo { get; private set; }
    public IDbConnection? DatabaseConnection { get; private set; }
    
    private bool _isTestSuccessful;
    public bool IsTestSuccessful 
    { 
        get => _isTestSuccessful;
        private set
        {
            _isTestSuccessful = value;
            OnPropertyChanged(nameof(IsTestSuccessful));
            OnPropertyChanged(nameof(IsTestFailed));
        }
    }
    
    public bool IsTestFailed => !IsTestSuccessful && !string.IsNullOrEmpty(TestResult);
    
    private bool _isTesting;
    public bool IsTesting
    {
        get => _isTesting;
        private set
        {
            _isTesting = value;
            OnPropertyChanged(nameof(IsTesting));
        }
    }
    
    private string _testResult = string.Empty;
    public string TestResult
    {
        get => _testResult;
        private set
        {
            _testResult = value;
            OnPropertyChanged(nameof(TestResult));
            OnPropertyChanged(nameof(IsTestFailed));
        }
    }
    
    private readonly ConnectionManagerViewModel _viewModel;

    public event PropertyChangedEventHandler? PropertyChanged;

    public ConnectionEditDialog(ConnectionInfo connectionInfo, ConnectionManagerViewModel viewModel, bool autoExecute = false)
    {
        InitializeComponent();
        
        ConnectionInfo = connectionInfo ?? new ConnectionInfo();
        _viewModel = viewModel;
        
        DataContext = ConnectionInfo;
        
        // Set password if available
        if (!string.IsNullOrEmpty(ConnectionInfo.Password))
        {
            PasswordBox.Password = ConnectionInfo.Password;
            if (autoExecute)
            {
                // 在視窗載入完成後自動執行連線
                Loaded += async (s, e) => 
                {
                    // 稍微延遲以確保 UI 完全載入
                    await Task.Delay(20);
                    OnOK(OnOk_button, new RoutedEventArgs());
                };
            }
        }

        
        // Focus on connection name
        Loaded += (s, e) => 
        {
            TextBox? nameTextBox;
            if (string.IsNullOrEmpty(ConnectionInfo.Server))
            {
                nameTextBox = FindName("ServerBox") as TextBox;
                nameTextBox?.Focus();
            }
            else if (string.IsNullOrEmpty(ConnectionInfo.ServiceName))
            {
                nameTextBox = FindName("ServiceNameBox") as TextBox;
                nameTextBox?.Focus();
            }
            else if (string.IsNullOrEmpty(ConnectionInfo.Username))
            {
                nameTextBox = FindName("UsernameBox") as TextBox;
                nameTextBox?.Focus();
            }
            else if (string.IsNullOrEmpty(ConnectionInfo.Password))
            {
                var PasswordTextBox = FindName("PasswordBox") as PasswordBox;
                PasswordTextBox?.Focus();
            }
        };
    }

    private void OnPasswordChanged(object sender, RoutedEventArgs e)
    {
        if (sender is PasswordBox passwordBox)
        {
            ConnectionInfo.Password = passwordBox.Password;
        }
    }

    private async void OnTestConnection(object sender, RoutedEventArgs e)
    {
        try
        {
            IsTesting = true;
            TestResult = "測試連線中...";
            
            // Update the editing connection in the view model
            _viewModel.EditingConnection = ConnectionInfo;
            
            // Execute test command
            if (_viewModel.TestConnectionCommand.CanExecute(null))
            {
                await ((AsyncRelayCommand)_viewModel.TestConnectionCommand).ExecuteAsync(null);
            }
            
            // Update test result display
            TestResult = _viewModel.TestResult;
            IsTestSuccessful = _viewModel.TestResult.Contains("成功");
        }
        catch (Exception ex)
        {
            TestResult = $"測試連線時發生錯誤: {ex.Message}";
            IsTestSuccessful = false;
        }
        finally
        {
            IsTesting = false;
        }
    }

    private async void OnOK(object sender, RoutedEventArgs e)
    {
        // Validate connection info
        var (isValid, errorMessage) = ConnectionInfo.Validate();
        if (!isValid)
        {
            MessageBox.Show(errorMessage, "驗證錯誤",
                          MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            // 更新 ViewModel 中的連線資訊
            _viewModel.EditingConnection = ConnectionInfo;

            // 透過 ConnectionService 創建連線
            var connectionService = App.ServiceProvider?.GetService<IConnectionService>();
            if (connectionService == null)
            {
                MessageBox.Show("無法取得連線服務", "系統錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 創建實際的連線提供者
            var connectionProvider = App.ServiceProvider?.GetService<IConnectionProvider>();
            if (connectionProvider == null)
            {
                MessageBox.Show("無法取得連線提供者", "系統錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 使用連線進度對話框顯示連線過程
            var serverInfo = $"{ConnectionInfo.Server}:{ConnectionInfo.Port}/{ConnectionInfo.ServiceName}";
            
            DatabaseConnection = await ConnectionProgressDialog.ShowConnectionDialog(
                this,
                ConnectionInfo.Id,
                async (cancellationToken) =>
                {
                    // 創建連線
                    var connection = await connectionProvider.CreateConnectionAsync(ConnectionInfo);
                    
                    // 測試連線是否正常
                    if (connection.State != ConnectionState.Open)
                    {
                        connection.Open();
                    }
                    
                    // 檢查取消令牌
                    cancellationToken.ThrowIfCancellationRequested();
                    
                    return connection;
                },
                serverInfo,
                ConnectionInfo.ConnectionTimeout
            );

            // 確認連線成功後才設定 DialogResult
            if (DatabaseConnection != null && DatabaseConnection.State == ConnectionState.Open)
            {
                // 儲存連線資訊到設定檔
                try
                {
                    // 儲存連線資訊，ConnectionService.SaveConnectionAsync 會檢查是否已存在
                    // 如果已存在，會更新而不是重複儲存
                    await connectionService.SaveConnectionAsync(ConnectionInfo);
                }
                catch (Exception saveEx)
                {
                    // 儲存失敗不影響連線使用，只顯示警告
                    MessageBox.Show($"連線成功，但儲存連線資訊失敗: {saveEx.Message}", "警告",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
                
                DialogResult = true;
                Close();
            }
            else
            {
                // 連線被取消或失敗，清理資源但不設定 DialogResult
                // 這樣對話框會保持開啟，讓使用者可以重新嘗試
                DatabaseConnection?.Dispose();
                DatabaseConnection = null;
                // DialogResult 保持 null，對話框不會關閉
            }
        }
        catch (OperationCanceledException)
        {
            // 連線被取消，清理資源但不設定 DialogResult
            DatabaseConnection?.Dispose();
            DatabaseConnection = null;
            // DialogResult 保持 null，對話框不會關閉
        }
        catch (Exception ex)
        {
            // 其他錯誤已經由進度對話框處理，這裡只需要清理資源
            DatabaseConnection?.Dispose();
            DatabaseConnection = null;
            // DialogResult 保持 null，對話框不會關閉
            MessageBox.Show("連線建立失敗", "連線錯誤",
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
    private async void OnOK0(object sender, RoutedEventArgs e)
    {
        // Validate connection info
        var (isValid, errorMessage) = ConnectionInfo.Validate();
        if (!isValid)
        {
            MessageBox.Show(errorMessage, "驗證錯誤",
                          MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            // 更新 ViewModel 中的連線資訊
            _viewModel.EditingConnection = ConnectionInfo;

            // 透過 ConnectionService 創建連線
            var connectionService = App.ServiceProvider?.GetService<IConnectionService>();
            if (connectionService == null)
            {
                MessageBox.Show("無法取得連線服務", "系統錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 先測試連線
            //if (_viewModel.TestConnectionCommand.CanExecute(null))
            //{
            //    await ((AsyncRelayCommand)_viewModel.TestConnectionCommand).ExecuteAsync(null);

            //    if (!_viewModel.TestResult.Contains("成功"))
            //    {
            //        MessageBox.Show($"連線測試失敗: {_viewModel.TestResult}", "連線錯誤",
            //                      MessageBoxButton.OK, MessageBoxImage.Error);
            //        return;
            //    }
            //}

            // 創建實際的連線
            var connectionProvider = App.ServiceProvider?.GetService<IConnectionProvider>();
            if (connectionProvider == null)
            {
                MessageBox.Show("無法取得連線提供者", "系統錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            DatabaseConnection = await connectionProvider.CreateConnectionAsync(ConnectionInfo);

            // 測試連線是否正常
            if (DatabaseConnection.State != ConnectionState.Open)
            {
                DatabaseConnection.Open();
            }

            // 確認連線成功後才設定 DialogResult
            if (DatabaseConnection != null && DatabaseConnection.State == ConnectionState.Open)
            {
                // 儲存連線資訊到設定檔
                try
                {
                    // 儲存連線資訊，ConnectionService.SaveConnectionAsync 會檢查是否已存在
                    // 如果已存在，會更新而不是重複儲存
                    await connectionService.SaveConnectionAsync(ConnectionInfo);
                }
                catch (Exception saveEx)
                {
                    // 儲存失敗不影響連線使用，只顯示警告
                    MessageBox.Show($"連線成功，但儲存連線資訊失敗: {saveEx.Message}", "警告",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }

                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("連線建立失敗", "連線錯誤",
                              MessageBoxButton.OK, MessageBoxImage.Error);
                DatabaseConnection?.Dispose();
                DatabaseConnection = null;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"連線失敗: {ex.Message}", "連線錯誤",
                          MessageBoxButton.OK, MessageBoxImage.Error);
            DatabaseConnection?.Dispose();
            DatabaseConnection = null;
        }
    }


    private void OnCancel(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
    
    private void OnPropertyChanged(string propertyName)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}