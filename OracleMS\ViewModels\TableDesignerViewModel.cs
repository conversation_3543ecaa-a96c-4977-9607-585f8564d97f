using System.Collections.ObjectModel;
using System.Data;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels;

public class TableDesignerViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    private IDbConnection? _connection;
    private string _tableName = string.Empty;

    private TableSchema? _tableSchema;
    public TableSchema? TableSchema
    {
        get => _tableSchema;
        set => SetProperty(ref _tableSchema, value);
    }

    public ObservableCollection<ColumnInfo> Columns { get; } = new();
    public ObservableCollection<IndexInfo> Indexes { get; } = new();
    public ObservableCollection<ConstraintInfo> Constraints { get; } = new();
    public ObservableCollection<TriggerInfo> Triggers { get; } = new();

    private ColumnInfo? _selectedColumn;
    public ColumnInfo? SelectedColumn
    {
        get => _selectedColumn;
        set => SetProperty(ref _selectedColumn, value);
    }

    private IndexInfo? _selectedIndex;
    public IndexInfo? SelectedIndex
    {
        get => _selectedIndex;
        set => SetProperty(ref _selectedIndex, value);
    }

    private ConstraintInfo? _selectedConstraint;
    public ConstraintInfo? SelectedConstraint
    {
        get => _selectedConstraint;
        set => SetProperty(ref _selectedConstraint, value);
    }

    private TriggerInfo? _selectedTrigger;
    public TriggerInfo? SelectedTrigger
    {
        get => _selectedTrigger;
        set => SetProperty(ref _selectedTrigger, value);
    }

    private int _selectedTabIndex;
    public int SelectedTabIndex
    {
        get => _selectedTabIndex;
        set => SetProperty(ref _selectedTabIndex, value);
    }

    private bool _isLoading;
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    private string _statusMessage = string.Empty;
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    private string _tableComment = string.Empty;
    public string TableComment
    {
        get => _tableComment;
        set => SetProperty(ref _tableComment, value);
    }

    private string _tablespace = string.Empty;
    public string Tablespace
    {
        get => _tablespace;
        set => SetProperty(ref _tablespace, value);
    }

    private long _tableSize;
    public long TableSize
    {
        get => _tableSize;
        set => SetProperty(ref _tableSize, value);
    }

    private long _rowCount;
    public long RowCount
    {
        get => _rowCount;
        set => SetProperty(ref _rowCount, value);
    }

    public string TableSizeFormatted => FormatBytes(TableSize);

    public ICommand RefreshCommand { get; }
    public ICommand ViewColumnDetailsCommand { get; }
    public ICommand ViewIndexDetailsCommand { get; }
    public ICommand ViewConstraintDetailsCommand { get; }
    public ICommand ViewTriggerDetailsCommand { get; }
    public ICommand GenerateCreateScriptCommand { get; }
    public ICommand CopyColumnNameCommand { get; }
    public ICommand CopyIndexNameCommand { get; }

    public event EventHandler<GenerateScriptEventArgs>? GenerateScriptRequested;

    public TableDesignerViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;

        RefreshCommand = new AsyncRelayCommand(OnRefreshAsync);
        ViewColumnDetailsCommand = new RelayCommand<ColumnInfo>(OnViewColumnDetails);
        ViewIndexDetailsCommand = new RelayCommand<IndexInfo>(OnViewIndexDetails);
        ViewConstraintDetailsCommand = new RelayCommand<ConstraintInfo>(OnViewConstraintDetails);
        ViewTriggerDetailsCommand = new RelayCommand<TriggerInfo>(OnViewTriggerDetails);
        GenerateCreateScriptCommand = new AsyncRelayCommand(OnGenerateCreateScriptAsync);
        CopyColumnNameCommand = new RelayCommand<ColumnInfo>(OnCopyColumnName);
        CopyIndexNameCommand = new RelayCommand<IndexInfo>(OnCopyIndexName);
    }

    public void SetConnection(IDbConnection connection)
    {
        _connection = connection;
    }

    public async Task LoadTableSchema(string tableName)
    {
        _tableName = tableName;
        await LoadSchemaAsync();
    }

    public async Task LoadTableSchemaAsync(IDbConnection connection, string tableName)
    {
        _connection = connection;
        _tableName = tableName;

        await LoadSchemaAsync();
    }

    private async Task LoadSchemaAsync()
    {
        if (_connection == null || string.IsNullOrEmpty(_tableName)) return;

        IsLoading = true;
        StatusMessage = "載入資料表結構中...";

        try
        {
            TableSchema = await _databaseService.GetTableSchemaAsync(_connection, _tableName);
            
            if (TableSchema != null)
            {
                // Load columns
                Columns.Clear();
                foreach (var column in TableSchema.Columns)
                {
                    Columns.Add(column);
                }

                // Load indexes
                Indexes.Clear();
                foreach (var index in TableSchema.Indexes)
                {
                    Indexes.Add(index);
                }

                // Load constraints
                Constraints.Clear();
                foreach (var constraint in TableSchema.Constraints)
                {
                    Constraints.Add(constraint);
                }

                // Load triggers
                Triggers.Clear();
                foreach (var trigger in TableSchema.Triggers)
                {
                    Triggers.Add(trigger);
                }

                // Load additional table information
                await LoadTableInfoAsync();
            }

            StatusMessage = $"已載入資料表結構: {_tableName}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"載入資料表結構失敗: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task LoadTableInfoAsync()
    {
        if (_connection == null) return;

        try
        {
            // Get table comment
            var commentSql = $@"
                SELECT comments 
                FROM user_tab_comments 
                WHERE table_name = '{_tableName.ToUpper()}'";
            
            var commentResult = await _databaseService.ExecuteQueryAsync(_connection, commentSql);
            if (commentResult.Rows.Count > 0)
            {
                TableComment = commentResult.Rows[0]["comments"]?.ToString() ?? string.Empty;
            }

            // Get tablespace
            var tablespaceSql = $@"
                SELECT tablespace_name 
                FROM user_tables 
                WHERE table_name = '{_tableName.ToUpper()}'";
            
            var tablespaceResult = await _databaseService.ExecuteQueryAsync(_connection, tablespaceSql);
            if (tablespaceResult.Rows.Count > 0)
            {
                Tablespace = tablespaceResult.Rows[0]["tablespace_name"]?.ToString() ?? string.Empty;
            }

            // Get row count
            var countSql = $"SELECT COUNT(*) as row_count FROM {_tableName}";
            var countResult = await _databaseService.ExecuteQueryAsync(_connection, countSql);
            if (countResult.Rows.Count > 0)
            {
                RowCount = Convert.ToInt64(countResult.Rows[0]["row_count"]);
            }

            // Get table size (approximate)
            var sizeSql = $@"
                SELECT 
                    SUM(bytes) as table_size
                FROM user_segments 
                WHERE segment_name = '{_tableName.ToUpper()}'
                AND segment_type = 'TABLE'";
            
            var sizeResult = await _databaseService.ExecuteQueryAsync(_connection, sizeSql);
            if (sizeResult.Rows.Count > 0 && sizeResult.Rows[0]["table_size"] != DBNull.Value)
            {
                TableSize = Convert.ToInt64(sizeResult.Rows[0]["table_size"]);
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"載入資料表資訊時發生錯誤: {ex.Message}";
        }
    }

    private async Task OnRefreshAsync()
    {
        await LoadSchemaAsync();
    }

    private void OnViewColumnDetails(ColumnInfo? column)
    {
        if (column == null) return;

        SelectedColumn = column;
        StatusMessage = $"檢視欄位詳細資訊: {column.ColumnName}";
    }

    private void OnViewIndexDetails(IndexInfo? index)
    {
        if (index == null) return;

        SelectedIndex = index;
        StatusMessage = $"檢視索引詳細資訊: {index.IndexName}";
    }

    private void OnViewConstraintDetails(ConstraintInfo? constraint)
    {
        if (constraint == null) return;

        SelectedConstraint = constraint;
        StatusMessage = $"檢視約束詳細資訊: {constraint.ConstraintName}";
    }

    private void OnViewTriggerDetails(TriggerInfo? trigger)
    {
        if (trigger == null) return;

        SelectedTrigger = trigger;
        StatusMessage = $"檢視觸發器詳細資訊: {trigger.TriggerName}";
    }

    private async Task OnGenerateCreateScriptAsync()
    {
        if (_connection == null || string.IsNullOrEmpty(_tableName)) return;

        try
        {
            var script = await GenerateTableCreateScriptAsync();
            GenerateScriptRequested?.Invoke(this, new GenerateScriptEventArgs(script, $"CREATE TABLE {_tableName}"));
            StatusMessage = $"已產生 CREATE TABLE 腳本: {_tableName}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"產生 CREATE TABLE 腳本失敗: {ex.Message}";
        }
    }

    private async Task<string> GenerateTableCreateScriptAsync()
    {
        if (TableSchema == null) return string.Empty;

        var script = new System.Text.StringBuilder();
        script.AppendLine($"CREATE TABLE {_tableName} (");

        // Add columns
        var columnDefinitions = new List<string>();
        foreach (var column in TableSchema.Columns)
        {
            var definition = $"    {column.ColumnName} {column.DataType}";
            
            if (column.MaxLength.HasValue && column.MaxLength > 0)
            {
                definition += $"({column.MaxLength})";
            }
            else if (column.Precision.HasValue && column.Scale.HasValue)
            {
                definition += $"({column.Precision},{column.Scale})";
            }

            if (!column.IsNullable)
            {
                definition += " NOT NULL";
            }

            if (!string.IsNullOrEmpty(column.DefaultValue))
            {
                definition += $" DEFAULT {column.DefaultValue}";
            }

            columnDefinitions.Add(definition);
        }

        script.AppendLine(string.Join(",\n", columnDefinitions));

        // Add primary key constraint
        var primaryKeyColumns = TableSchema.Columns.Where(c => c.IsPrimaryKey).ToList();
        if (primaryKeyColumns.Any())
        {
            var pkColumns = string.Join(", ", primaryKeyColumns.Select(c => c.ColumnName));
            script.AppendLine($",    CONSTRAINT PK_{_tableName} PRIMARY KEY ({pkColumns})");
        }

        script.AppendLine(");");

        // Add comments
        if (!string.IsNullOrEmpty(TableComment))
        {
            script.AppendLine();
            script.AppendLine($"COMMENT ON TABLE {_tableName} IS '{TableComment}';");
        }

        foreach (var column in TableSchema.Columns.Where(c => !string.IsNullOrEmpty(c.Comment)))
        {
            script.AppendLine($"COMMENT ON COLUMN {_tableName}.{column.ColumnName} IS '{column.Comment}';");
        }

        // Add indexes
        foreach (var index in TableSchema.Indexes.Where(i => !i.IsPrimaryKey))
        {
            script.AppendLine();
            var uniqueKeyword = index.IsUnique ? "UNIQUE " : "";
            var indexColumns = string.Join(", ", index.Columns);
            script.AppendLine($"CREATE {uniqueKeyword}INDEX {index.IndexName} ON {_tableName} ({indexColumns});");
        }

        return script.ToString();
    }

    private void OnCopyColumnName(ColumnInfo? column)
    {
        if (column == null) return;

        try
        {
            System.Windows.Clipboard.SetText(column.ColumnName);
            StatusMessage = $"已複製欄位名稱: {column.ColumnName}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"複製失敗: {ex.Message}";
        }
    }

    private void OnCopyIndexName(IndexInfo? index)
    {
        if (index == null) return;

        try
        {
            System.Windows.Clipboard.SetText(index.IndexName);
            StatusMessage = $"已複製索引名稱: {index.IndexName}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"複製失敗: {ex.Message}";
        }
    }

    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";

        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;

        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }

        return $"{size:0.##} {sizes[order]}";
    }

    public string GetColumnDataTypeDisplay(ColumnInfo column)
    {
        var display = column.DataType;
        
        if (column.MaxLength.HasValue && column.MaxLength > 0)
        {
            display += $"({column.MaxLength})";
        }
        else if (column.Precision.HasValue && column.Scale.HasValue)
        {
            display += $"({column.Precision},{column.Scale})";
        }

        return display;
    }

    public string GetColumnConstraints(ColumnInfo column)
    {
        var constraints = new List<string>();
        
        if (column.IsPrimaryKey)
            constraints.Add("PK");
        
        if (!column.IsNullable)
            constraints.Add("NOT NULL");
        
        if (!string.IsNullOrEmpty(column.DefaultValue))
            constraints.Add("DEFAULT");

        return string.Join(", ", constraints);
    }
}