using System;
using System.Collections.Generic;
using System.Windows.Controls;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using OracleMS.Views;
using OracleMS.Models;

namespace OracleMS.Tests
{
    /// <summary>
    /// 測試 Tab Index 記錄功能的實現
    /// </summary>
    [TestClass]
    public class TestTabIndexRecordingImplementation
    {
        /// <summary>
        /// 測試 DbSession 中的 _tabOpenerIndex 字典是否正確初始化
        /// </summary>
        [TestMethod]
        public void TestTabOpenerIndexDictionaryInitialization()
        {
            // 這個測試驗證 DbSession 類別中是否正確添加了 _tabOpenerIndex 字典
            // 由於這是私有字段，我們通過編譯成功來驗證實現
            
            // 檢查 DbSession 類別是否存在
            var dbSessionType = typeof(DbSession);
            Assert.IsNotNull(dbSessionType, "DbSession 類別應該存在");
            
            // 檢查是否有 ReturnToOpenerTabPublic 公開方法
            var publicMethod = dbSessionType.GetMethod("ReturnToOpenerTabPublic");
            Assert.IsNotNull(publicMethod, "ReturnToOpenerTabPublic 方法應該存在");
            
            // 檢查方法參數
            var parameters = publicMethod.GetParameters();
            Assert.AreEqual(1, parameters.Length, "ReturnToOpenerTabPublic 方法應該有一個參數");
            Assert.AreEqual(typeof(TabItem), parameters[0].ParameterType, "參數應該是 TabItem 類型");
        }

        /// <summary>
        /// 測試 IndexEditorView 中的 FindParentDbSession 方法是否存在
        /// </summary>
        [TestMethod]
        public void TestIndexEditorViewFindParentDbSessionMethod()
        {
            // 檢查 IndexEditorView 類別是否存在
            var indexEditorViewType = typeof(IndexEditorView);
            Assert.IsNotNull(indexEditorViewType, "IndexEditorView 類別應該存在");
            
            // 檢查是否有 FindParentDbSession 私有方法
            var privateMethod = indexEditorViewType.GetMethod("FindParentDbSession", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            Assert.IsNotNull(privateMethod, "FindParentDbSession 方法應該存在");
        }

        /// <summary>
        /// 測試功能實現的完整性
        /// </summary>
        [TestMethod]
        public void TestTabIndexRecordingFeatureCompleteness()
        {
            // 驗證所有必要的組件都已實現
            
            // 1. DbSession 應該有記錄 tab opener 的能力
            var dbSessionType = typeof(DbSession);
            var createObjectEditorTabMethod = dbSessionType.GetMethod("CreateObjectEditorTab");
            var createIndexEditorTabMethod = dbSessionType.GetMethod("CreateIndexEditorTab");
            var returnToOpenerTabPublicMethod = dbSessionType.GetMethod("ReturnToOpenerTabPublic");
            
            Assert.IsNotNull(createObjectEditorTabMethod, "CreateObjectEditorTab 方法應該存在");
            Assert.IsNotNull(createIndexEditorTabMethod, "CreateIndexEditorTab 方法應該存在");
            Assert.IsNotNull(returnToOpenerTabPublicMethod, "ReturnToOpenerTabPublic 方法應該存在");
            
            // 2. IndexEditorView 應該有尋找父級 DbSession 的能力
            var indexEditorViewType = typeof(IndexEditorView);
            var findParentDbSessionMethod = indexEditorViewType.GetMethod("FindParentDbSession", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            
            Assert.IsNotNull(findParentDbSessionMethod, "FindParentDbSession 方法應該存在");
            
            // 3. 驗證方法簽名正確
            Assert.AreEqual(typeof(TabItem), createObjectEditorTabMethod.ReturnType, 
                "CreateObjectEditorTab 應該返回 TabItem");
            Assert.AreEqual(typeof(TabItem), createIndexEditorTabMethod.ReturnType, 
                "CreateIndexEditorTab 應該返回 TabItem");
            Assert.AreEqual(typeof(void), returnToOpenerTabPublicMethod.ReturnType, 
                "ReturnToOpenerTabPublic 應該返回 void");
        }

        /// <summary>
        /// 測試支援的編輯器類型
        /// </summary>
        [TestMethod]
        public void TestSupportedEditorTypes()
        {
            // 驗證 DatabaseObjectType 枚舉包含所有支援的類型
            var supportedTypes = new[]
            {
                DatabaseObjectType.Index,
                DatabaseObjectType.Table,
                DatabaseObjectType.Function,
                DatabaseObjectType.Procedure,
                DatabaseObjectType.Package,
                DatabaseObjectType.View
            };

            foreach (var type in supportedTypes)
            {
                Assert.IsTrue(Enum.IsDefined(typeof(DatabaseObjectType), type), 
                    $"DatabaseObjectType 應該包含 {type}");
            }
        }

        /// <summary>
        /// 驗證實現文檔是否存在
        /// </summary>
        [TestMethod]
        public void TestImplementationDocumentationExists()
        {
            // 檢查實現說明文檔是否存在
            var documentPath = "TabIndex_Recording_Implementation.md";
            var fileExists = System.IO.File.Exists(documentPath);
            
            Assert.IsTrue(fileExists, "實現說明文檔應該存在");
            
            if (fileExists)
            {
                var content = System.IO.File.ReadAllText(documentPath);
                Assert.IsTrue(content.Contains("Tab Index 記錄功能實現說明"), 
                    "文檔應該包含功能說明");
                Assert.IsTrue(content.Contains("_tabOpenerIndex"), 
                    "文檔應該說明 _tabOpenerIndex 的使用");
                Assert.IsTrue(content.Contains("ReturnToOpenerTab"), 
                    "文檔應該說明 ReturnToOpenerTab 方法");
            }
        }
    }
}