using System;
using System.Windows;

namespace OracleMS.Interfaces
{
    /// <summary>
    /// Interface for logging binding errors in the application
    /// </summary>
    public interface IBindingErrorLogger
    {
        /// <summary>
        /// Log a binding error
        /// </summary>
        /// <param name="source">The source object where the binding error occurred</param>
        /// <param name="errorMessage">The error message</param>
        void LogBindingError(object source, string errorMessage);

        /// <summary>
        /// Log a binding error with exception details
        /// </summary>
        /// <param name="source">The source object where the binding error occurred</param>
        /// <param name="errorMessage">The error message</param>
        /// <param name="exception">The exception that occurred</param>
        void LogBindingError(object source, string errorMessage, Exception exception);

        /// <summary>
        /// Log a binding success
        /// </summary>
        /// <param name="source">The source object where the binding succeeded</param>
        /// <param name="message">The success message</param>
        void LogBindingSuccess(object source, string message);
    }
}