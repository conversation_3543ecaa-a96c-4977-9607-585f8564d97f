using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels;

public class DataGridViewModel : ViewModelBase, ISaveable, IDisposable
{
    private readonly IDatabaseService _databaseService;
    private IDbConnection? _connection;
    private string _tableName = string.Empty;
    private object[]? _copiedRowData;

    private DataTable? _dataTable;
    public DataTable? DataTable
    {
        get => _dataTable;
        set => SetProperty(ref _dataTable, value);
    }

    private DataView? _dataView;
    public DataView? DataView
    {
        get => _dataView;
        set => SetProperty(ref _dataView, value);
    }

    private DataRowView? _selectedRow;
    public DataRowView? SelectedRow
    {
        get => _selectedRow;
        set => SetProperty(ref _selectedRow, value);
    }

    private string _filterText = string.Empty;
    public string FilterText
    {
        get => _filterText;
        set
        {
            SetProperty(ref _filterText, value);
            ApplyFilter();
        }
    }

    private string _sortColumn = string.Empty;
    public string SortColumn
    {
        get => _sortColumn;
        set => SetProperty(ref _sortColumn, value);
    }

    private ListSortDirection _sortDirection = ListSortDirection.Ascending;
    public ListSortDirection SortDirection
    {
        get => _sortDirection;
        set => SetProperty(ref _sortDirection, value);
    }

    private int _currentPage = 1;
    public int CurrentPage
    {
        get => _currentPage;
        set
        {
            SetProperty(ref _currentPage, value);
            OnPropertyChanged(nameof(PageInfo));
        }
    }

    private int _pageSize = 100;
    public int PageSize
    {
        get => _pageSize;
        set
        {
            SetProperty(ref _pageSize, value);
            OnPropertyChanged(nameof(TotalPages));
            OnPropertyChanged(nameof(PageInfo));
        }
    }

    private int _totalRows;
    public int TotalRows
    {
        get => _totalRows;
        set
        {
            SetProperty(ref _totalRows, value);
            OnPropertyChanged(nameof(TotalPages));
            OnPropertyChanged(nameof(PageInfo));
        }
    }

    public int TotalPages => (int)Math.Ceiling((double)TotalRows / PageSize);
    public string PageInfo => $"第 {CurrentPage} 頁，共 {TotalPages} 頁 (總計 {TotalRows} 筆記錄)";

    private bool _isLoading;
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    private bool _hasUnsavedChanges;
    public bool HasUnsavedChanges
    {
        get => _hasUnsavedChanges;
        set => SetProperty(ref _hasUnsavedChanges, value);
    }

    private bool _autoSaveEnabled = true;
    public bool AutoSaveEnabled
    {
        get => _autoSaveEnabled;
        set => SetProperty(ref _autoSaveEnabled, value);
    }

    private Timer? _autoSaveTimer;

    private string _statusMessage = string.Empty;
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    private bool _isReadOnly;
    public bool IsReadOnly
    {
        get => _isReadOnly;
        set => SetProperty(ref _isReadOnly, value);
    }

    public ObservableCollection<string> AvailablePageSizes { get; } = new() { "50", "100", "200", "500", "1000" };

    public ICommand RefreshCommand { get; }
    public ICommand SaveChangesCommand { get; }
    public ICommand CancelChangesCommand { get; }
    public ICommand AddRowCommand { get; }
    public ICommand DeleteRowCommand { get; }
    public ICommand FirstPageCommand { get; }
    public ICommand PreviousPageCommand { get; }
    public ICommand NextPageCommand { get; }
    public ICommand LastPageCommand { get; }
    public ICommand SortCommand { get; }
    public ICommand ExportCommand { get; }
    public ICommand ImportCommand { get; }
    public ICommand CopyRowCommand { get; }
    public ICommand PasteRowCommand { get; }

    public DataGridViewModel(IDatabaseService databaseService)
    {
        _databaseService = databaseService;

        RefreshCommand = new AsyncRelayCommand(OnRefreshAsync);
        SaveChangesCommand = new AsyncRelayCommand(OnSaveChangesAsync, () => HasUnsavedChanges && !IsReadOnly);
        CancelChangesCommand = new RelayCommand(OnCancelChanges, () => HasUnsavedChanges);
        AddRowCommand = new RelayCommand(OnAddRow, () => !IsReadOnly);
        DeleteRowCommand = new RelayCommand(OnDeleteRow, () => SelectedRow != null && !IsReadOnly);
        FirstPageCommand = new AsyncRelayCommand(OnFirstPageAsync, () => CurrentPage > 1);
        PreviousPageCommand = new AsyncRelayCommand(OnPreviousPageAsync, () => CurrentPage > 1);
        NextPageCommand = new AsyncRelayCommand(OnNextPageAsync, () => CurrentPage < TotalPages);
        LastPageCommand = new AsyncRelayCommand(OnLastPageAsync, () => CurrentPage < TotalPages);
        SortCommand = new RelayCommand<string>(OnSort);
        ExportCommand = new AsyncRelayCommand(OnExportAsync);
        ImportCommand = new AsyncRelayCommand(OnImportAsync, () => !IsReadOnly);
        CopyRowCommand = new RelayCommand(OnCopyRow, () => SelectedRow != null);
        PasteRowCommand = new RelayCommand(OnPasteRow, () => !IsReadOnly && _copiedRowData != null);
    }

    public void SetConnection(IDbConnection connection)
    {
        _connection = connection;
    }

    public async Task LoadTableData(string tableName, bool isReadOnly = false)
    {
        _tableName = tableName;
        IsReadOnly = isReadOnly;
        CurrentPage = 1;

        await LoadDataAsync();
    }

    public async Task LoadTableDataAsync(IDbConnection connection, string tableName, bool isReadOnly = false)
    {
        _connection = connection;
        _tableName = tableName;
        IsReadOnly = isReadOnly;
        CurrentPage = 1;

        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        if (_connection == null || string.IsNullOrEmpty(_tableName)) return;

        IsLoading = true;
        StatusMessage = "載入資料中...";

        try
        {
            // Get total row count
            var countSql = $"SELECT COUNT(*) FROM {_tableName}";
            var countResult = await _databaseService.ExecuteQueryAsync(_connection, countSql);
            TotalRows = Convert.ToInt32(countResult.Rows[0][0]);

            // Load current page data
            var offset = (CurrentPage - 1) * PageSize;
            var sql = $@"
                SELECT * FROM (
                    SELECT t.*, ROW_NUMBER() OVER (ORDER BY ROWID) as rn 
                    FROM {_tableName} t
                ) WHERE rn > {offset} AND rn <= {offset + PageSize}";

            DataTable = await _databaseService.ExecuteQueryAsync(_connection, sql);
            
            // Remove the row number column
            if (DataTable.Columns.Contains("rn"))
            {
                DataTable.Columns.Remove("rn");
            }

            DataView = new DataView(DataTable);
            DataView.ListChanged += OnDataViewListChanged;

            StatusMessage = $"已載入 {DataTable.Rows.Count} 筆記錄";
            HasUnsavedChanges = false;
        }
        catch (Exception ex)
        {
            StatusMessage = $"載入資料失敗: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void OnDataViewListChanged(object? sender, ListChangedEventArgs e)
    {
        if (e.ListChangedType == ListChangedType.ItemChanged ||
            e.ListChangedType == ListChangedType.ItemAdded ||
            e.ListChangedType == ListChangedType.ItemDeleted)
        {
            HasUnsavedChanges = true;
            
            // 啟動自動儲存計時器
            if (AutoSaveEnabled && !IsReadOnly)
            {
                StartAutoSaveTimer();
            }
            
            // 即時驗證變更的資料
            if (e.ListChangedType == ListChangedType.ItemChanged && e.NewIndex >= 0)
            {
                ValidateRowInRealTime(e.NewIndex);
            }
        }
    }

    private void StartAutoSaveTimer()
    {
        // 停止現有的計時器
        _autoSaveTimer?.Dispose();
        
        // 啟動新的計時器，5秒後自動儲存
        _autoSaveTimer = new Timer(async _ => await AutoSaveAsync(), null, TimeSpan.FromSeconds(5), Timeout.InfiniteTimeSpan);
    }

    private async Task AutoSaveAsync()
    {
        try
        {
            if (HasUnsavedChanges && !IsReadOnly && _connection != null)
            {
                await OnSaveChangesAsync();
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"自動儲存失敗: {ex.Message}";
        }
        finally
        {
            _autoSaveTimer?.Dispose();
            _autoSaveTimer = null;
        }
    }

    private void ValidateRowInRealTime(int rowIndex)
    {
        try
        {
            if (DataView == null || rowIndex >= DataView.Count) return;

            var rowView = DataView[rowIndex];
            var row = rowView.Row;

            // 執行即時驗證
            ValidateRowData(row);
            
            // 如果驗證通過，清除錯誤狀態
            StatusMessage = "資料驗證通過";
        }
        catch (ArgumentException ex)
        {
            // 顯示驗證錯誤，但不阻止編輯
            StatusMessage = $"驗證警告: {ex.Message}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"驗證錯誤: {ex.Message}";
        }
    }

    private async Task OnRefreshAsync()
    {
        if (HasUnsavedChanges)
        {
            // In a real implementation, ask user if they want to lose changes
            StatusMessage = "有未儲存的變更，請先儲存或取消變更";
            return;
        }

        await LoadDataAsync();
    }

    private async Task OnSaveChangesAsync()
    {
        if (_connection == null || DataTable == null || !HasUnsavedChanges) return;

        IsLoading = true;
        StatusMessage = "儲存變更中...";

        try
        {
            var changedRows = DataTable.GetChanges();
            if (changedRows == null)
            {
                StatusMessage = "沒有變更需要儲存";
                return;
            }

            var savedCount = 0;
            var errorCount = 0;
            var errors = new List<string>();

            // 使用交易進行批次更新
            using var transaction = _connection.BeginTransaction();
            
            try
            {
                foreach (DataRow row in changedRows.Rows)
                {
                    try
                    {
                        switch (row.RowState)
                        {
                            case DataRowState.Added:
                                await InsertRowWithValidationAsync(row);
                                savedCount++;
                                break;
                            case DataRowState.Modified:
                                await UpdateRowWithValidationAsync(row);
                                savedCount++;
                                break;
                            case DataRowState.Deleted:
                                await DeleteRowWithValidationAsync(row);
                                savedCount++;
                                break;
                        }
                    }
                    catch (Exception rowEx)
                    {
                        errorCount++;
                        errors.Add($"資料列 {GetRowIdentifier(row)}: {rowEx.Message}");
                        
                        // 回復此資料列的變更
                        if (row.RowState != DataRowState.Deleted)
                        {
                            row.RejectChanges();
                        }
                    }
                }

                if (errorCount == 0)
                {
                    transaction.Commit();
                    DataTable.AcceptChanges();
                    HasUnsavedChanges = false;
                    StatusMessage = $"已成功儲存 {savedCount} 筆變更";
                }
                else
                {
                    transaction.Rollback();
                    var errorMessage = $"儲存失敗: {errorCount} 筆資料有錯誤\n" + string.Join("\n", errors.Take(5));
                    if (errors.Count > 5)
                    {
                        errorMessage += $"\n... 還有 {errors.Count - 5} 個錯誤";
                    }
                    StatusMessage = errorMessage;
                    
                    // 顯示詳細錯誤訊息
                    ShowValidationErrors(errors);
                }
            }
            catch (Exception transactionEx)
            {
                transaction.Rollback();
                StatusMessage = $"交易失敗: {transactionEx.Message}";
                
                // 回復所有變更
                DataTable.RejectChanges();
                HasUnsavedChanges = false;
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"儲存失敗: {ex.Message}";
            
            // 回復所有變更
            DataTable?.RejectChanges();
            HasUnsavedChanges = false;
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task InsertRowAsync(DataRow row)
    {
        if (_connection == null) return;

        var columns = string.Join(", ", DataTable!.Columns.Cast<DataColumn>().Select(c => c.ColumnName));
        var values = string.Join(", ", row.ItemArray.Select(v => $"'{v}'"));
        var sql = $"INSERT INTO {_tableName} ({columns}) VALUES ({values})";

        await _databaseService.ExecuteNonQueryAsync(_connection, sql);
    }

    private async Task UpdateRowAsync(DataRow row)
    {
        if (_connection == null) return;

        var setParts = new List<string>();
        var whereParts = new List<string>();

        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var currentValue = row[i];
            var originalValue = row[i, DataRowVersion.Original];

            setParts.Add($"{column.ColumnName} = '{currentValue}'");
            whereParts.Add($"{column.ColumnName} = '{originalValue}'");
        }

        var sql = $"UPDATE {_tableName} SET {string.Join(", ", setParts)} WHERE {string.Join(" AND ", whereParts)}";
        await _databaseService.ExecuteNonQueryAsync(_connection, sql);
    }

    private async Task DeleteRowAsync(DataRow row)
    {
        if (_connection == null) return;

        var whereParts = new List<string>();
        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var value = row[i, DataRowVersion.Original];
            whereParts.Add($"{column.ColumnName} = '{value}'");
        }

        var sql = $"DELETE FROM {_tableName} WHERE {string.Join(" AND ", whereParts)}";
        await _databaseService.ExecuteNonQueryAsync(_connection, sql);
    }

    private async Task InsertRowWithValidationAsync(DataRow row)
    {
        if (_connection == null) return;

        // 驗證資料
        ValidateRowData(row);

        // 建構 INSERT SQL
        var columns = new List<string>();
        var parameters = new List<string>();

        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var value = row[i];

            columns.Add(column.ColumnName);
            parameters.Add(FormatSqlValue(value, column.DataType));
        }

        var sql = $"INSERT INTO {_tableName} ({string.Join(", ", columns)}) VALUES ({string.Join(", ", parameters)})";
        
        try
        {
            await _databaseService.ExecuteNonQueryAsync(_connection, sql);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"新增資料失敗: {GetFriendlyErrorMessage(ex)}", ex);
        }
    }

    private async Task UpdateRowWithValidationAsync(DataRow row)
    {
        if (_connection == null) return;

        // 驗證資料
        ValidateRowData(row);

        // 檢查並發衝突
        await CheckConcurrencyConflictAsync(row);

        // 建構 UPDATE SQL
        var setParts = new List<string>();
        var whereParts = new List<string>();

        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var currentValue = row[i];
            var originalValue = row[i, DataRowVersion.Original];

            setParts.Add($"{column.ColumnName} = {FormatSqlValue(currentValue, column.DataType)}");
            whereParts.Add($"{column.ColumnName} = {FormatSqlValue(originalValue, column.DataType)}");
        }

        var sql = $"UPDATE {_tableName} SET {string.Join(", ", setParts)} WHERE {string.Join(" AND ", whereParts)}";
        
        try
        {
            var rowsAffected = await _databaseService.ExecuteNonQueryAsync(_connection, sql);
            if (rowsAffected == 0)
            {
                throw new InvalidOperationException("資料已被其他使用者修改或刪除，請重新整理後再試");
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"更新資料失敗: {GetFriendlyErrorMessage(ex)}", ex);
        }
    }

    private async Task DeleteRowWithValidationAsync(DataRow row)
    {
        if (_connection == null) return;

        // 檢查並發衝突
        await CheckConcurrencyConflictAsync(row);

        // 建構 DELETE SQL
        var whereParts = new List<string>();
        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var value = row[i, DataRowVersion.Original];
            whereParts.Add($"{column.ColumnName} = {FormatSqlValue(value, column.DataType)}");
        }

        var sql = $"DELETE FROM {_tableName} WHERE {string.Join(" AND ", whereParts)}";
        
        try
        {
            var rowsAffected = await _databaseService.ExecuteNonQueryAsync(_connection, sql);
            if (rowsAffected == 0)
            {
                throw new InvalidOperationException("資料已被其他使用者修改或刪除，請重新整理後再試");
            }
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"刪除資料失敗: {GetFriendlyErrorMessage(ex)}", ex);
        }
    }

    private void ValidateRowData(DataRow row)
    {
        var errors = new List<string>();

        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var value = row[i];

            // 檢查必填欄位
            if (!column.AllowDBNull && (value == null || value == DBNull.Value || string.IsNullOrWhiteSpace(value.ToString())))
            {
                errors.Add($"欄位 '{column.ColumnName}' 不能為空");
            }

            // 檢查資料長度
            if (value != null && value != DBNull.Value && column.DataType == typeof(string))
            {
                var stringValue = value.ToString();
                if (column.MaxLength > 0 && stringValue!.Length > column.MaxLength)
                {
                    errors.Add($"欄位 '{column.ColumnName}' 長度不能超過 {column.MaxLength} 個字元");
                }
            }

            // 檢查數值範圍
            if (value != null && value != DBNull.Value)
            {
                if (column.DataType == typeof(int) || column.DataType == typeof(long))
                {
                    if (!long.TryParse(value.ToString(), out _))
                    {
                        errors.Add($"欄位 '{column.ColumnName}' 必須是有效的整數");
                    }
                }
                else if (column.DataType == typeof(decimal) || column.DataType == typeof(double) || column.DataType == typeof(float))
                {
                    if (!decimal.TryParse(value.ToString(), out _))
                    {
                        errors.Add($"欄位 '{column.ColumnName}' 必須是有效的數值");
                    }
                }
                else if (column.DataType == typeof(DateTime))
                {
                    if (!DateTime.TryParse(value.ToString(), out _))
                    {
                        errors.Add($"欄位 '{column.ColumnName}' 必須是有效的日期時間");
                    }
                }
            }
        }

        if (errors.Any())
        {
            throw new ArgumentException($"資料驗證失敗:\n{string.Join("\n", errors)}");
        }
    }

    private async Task CheckConcurrencyConflictAsync(DataRow row)
    {
        if (_connection == null) return;

        // 建構查詢 SQL 來檢查資料是否已被修改
        var whereParts = new List<string>();
        for (int i = 0; i < DataTable!.Columns.Count; i++)
        {
            var column = DataTable.Columns[i];
            var originalValue = row[i, DataRowVersion.Original];
            whereParts.Add($"{column.ColumnName} = {FormatSqlValue(originalValue, column.DataType)}");
        }

        var checkSql = $"SELECT COUNT(*) FROM {_tableName} WHERE {string.Join(" AND ", whereParts)}";
        
        try
        {
            var result = await _databaseService.ExecuteQueryAsync(_connection, checkSql);
            var count = Convert.ToInt32(result.Rows[0][0]);
            
            if (count == 0)
            {
                throw new InvalidOperationException("資料已被其他使用者修改或刪除");
            }
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            throw new InvalidOperationException($"檢查資料衝突時發生錯誤: {ex.Message}", ex);
        }
    }

    private string FormatSqlValue(object? value, Type dataType)
    {
        if (value == null || value == DBNull.Value)
        {
            return "NULL";
        }

        if (dataType == typeof(string))
        {
            return $"'{value.ToString()?.Replace("'", "''")}'";
        }
        else if (dataType == typeof(DateTime))
        {
            var dateValue = Convert.ToDateTime(value);
            return $"TO_DATE('{dateValue:yyyy-MM-dd HH:mm:ss}', 'YYYY-MM-DD HH24:MI:SS')";
        }
        else if (dataType == typeof(bool))
        {
            return Convert.ToBoolean(value) ? "1" : "0";
        }
        else
        {
            return value.ToString() ?? "NULL";
        }
    }

    private string GetRowIdentifier(DataRow row)
    {
        try
        {
            // 嘗試找到主鍵欄位
            var primaryKeys = DataTable?.PrimaryKey;
            if (primaryKeys != null && primaryKeys.Length > 0)
            {
                var keyValues = primaryKeys.Select(col => row[col.ColumnName]?.ToString() ?? "NULL");
                return string.Join(", ", keyValues);
            }

            // 如果沒有主鍵，使用第一個欄位
            if (DataTable != null && DataTable.Columns.Count > 0)
            {
                return row[0]?.ToString() ?? "NULL";
            }

            return "未知";
        }
        catch
        {
            return "未知";
        }
    }

    private string GetFriendlyErrorMessage(Exception ex)
    {
        var message = ex.Message;
        
        // Oracle 特定錯誤處理
        if (message.Contains("ORA-00001"))
        {
            return "違反唯一約束條件，資料重複";
        }
        else if (message.Contains("ORA-01400"))
        {
            return "必填欄位不能為空";
        }
        else if (message.Contains("ORA-01722"))
        {
            return "數值格式錯誤";
        }
        else if (message.Contains("ORA-02291"))
        {
            return "違反外鍵約束條件";
        }
        else if (message.Contains("ORA-12899"))
        {
            return "資料長度超過欄位限制";
        }

        return message;
    }

    private void ShowValidationErrors(List<string> errors)
    {
        // 在實際應用中，這裡會顯示一個錯誤對話框
        // 目前只記錄到狀態訊息中
        var errorMessage = "資料驗證錯誤:\n" + string.Join("\n", errors.Take(10));
        if (errors.Count > 10)
        {
            errorMessage += $"\n... 還有 {errors.Count - 10} 個錯誤";
        }
        
        StatusMessage = errorMessage;
    }

    private void OnCancelChanges()
    {
        if (DataTable == null) return;

        DataTable.RejectChanges();
        HasUnsavedChanges = false;
        StatusMessage = "已取消所有變更";
    }

    private void OnAddRow()
    {
        if (DataTable == null || IsReadOnly) return;

        var newRow = DataTable.NewRow();
        DataTable.Rows.Add(newRow);
        StatusMessage = "已新增空白資料列";
    }

    private void OnDeleteRow()
    {
        if (SelectedRow == null || IsReadOnly) return;

        SelectedRow.Delete();
        StatusMessage = "已標記資料列為刪除";
    }

    private async Task OnFirstPageAsync()
    {
        CurrentPage = 1;
        await LoadDataAsync();
    }

    private async Task OnPreviousPageAsync()
    {
        if (CurrentPage > 1)
        {
            CurrentPage--;
            await LoadDataAsync();
        }
    }

    private async Task OnNextPageAsync()
    {
        if (CurrentPage < TotalPages)
        {
            CurrentPage++;
            await LoadDataAsync();
        }
    }

    private async Task OnLastPageAsync()
    {
        CurrentPage = TotalPages;
        await LoadDataAsync();
    }

    private void OnSort(string? columnName)
    {
        if (string.IsNullOrEmpty(columnName) || DataView == null) return;

        if (SortColumn == columnName)
        {
            SortDirection = SortDirection == ListSortDirection.Ascending 
                ? ListSortDirection.Descending 
                : ListSortDirection.Ascending;
        }
        else
        {
            SortColumn = columnName;
            SortDirection = ListSortDirection.Ascending;
        }

        var direction = SortDirection == ListSortDirection.Ascending ? "ASC" : "DESC";
        DataView.Sort = $"{columnName} {direction}";
        StatusMessage = $"已按 {columnName} {(SortDirection == ListSortDirection.Ascending ? "升序" : "降序")} 排序";
    }

    private void ApplyFilter()
    {
        if (DataView == null) return;

        if (string.IsNullOrWhiteSpace(FilterText))
        {
            DataView.RowFilter = string.Empty;
            StatusMessage = "已清除篩選";
        }
        else
        {
            // Simple filter - in a real implementation, this would be more sophisticated
            var filterParts = new List<string>();
            foreach (DataColumn column in DataTable!.Columns)
            {
                if (column.DataType == typeof(string))
                {
                    filterParts.Add($"{column.ColumnName} LIKE '%{FilterText}%'");
                }
            }

            if (filterParts.Any())
            {
                DataView.RowFilter = string.Join(" OR ", filterParts);
                StatusMessage = $"已套用篩選，顯示 {DataView.Count} 筆記錄";
            }
        }
    }

    private async Task OnExportAsync()
    {
        if (DataTable == null) return;

        try
        {
            var exportPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                $"{_tableName}_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

            await ExportToCsvAsync(DataTable, exportPath);
            StatusMessage = $"資料已匯出至: {exportPath}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"匯出失敗: {ex.Message}";
        }
    }

    private async Task OnImportAsync()
    {
        // In a real implementation, this would show an OpenFileDialog and import data
        StatusMessage = "匯入功能需要在 UI 層實作";
        await Task.CompletedTask;
    }

    private async Task ExportToCsvAsync(DataTable dataTable, string filePath)
    {
        using var writer = new StreamWriter(filePath);
        
        // Write headers
        var headers = dataTable.Columns.Cast<DataColumn>().Select(column => column.ColumnName);
        await writer.WriteLineAsync(string.Join(",", headers));

        // Write data
        foreach (DataRow row in dataTable.Rows)
        {
            var values = row.ItemArray.Select(field => $"\"{field}\"");
            await writer.WriteLineAsync(string.Join(",", values));
        }
    }

    private void OnCopyRow()
    {
        if (SelectedRow == null) return;

        _copiedRowData = SelectedRow.Row.ItemArray.Clone() as object[];
        StatusMessage = "已複製資料列";
    }

    private void OnPasteRow()
    {
        if (_copiedRowData == null || DataTable == null || IsReadOnly) return;

        try
        {
            var newRow = DataTable.NewRow();
            newRow.ItemArray = _copiedRowData;
            DataTable.Rows.Add(newRow);
            StatusMessage = "已貼上資料列";
        }
        catch (Exception ex)
        {
            StatusMessage = $"貼上失敗: {ex.Message}";
        }
    }

    public void Save()
    {
        _ = OnSaveChangesAsync();
    }

    public void Dispose()
    {
        _autoSaveTimer?.Dispose();
        _autoSaveTimer = null;
        
        if (DataView != null)
        {
            DataView.ListChanged -= OnDataViewListChanged;
        }
    }
}