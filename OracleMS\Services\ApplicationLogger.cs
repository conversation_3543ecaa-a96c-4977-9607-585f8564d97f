using System;
using System.Diagnostics;
using System.IO;
using System.Text;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using Serilog;
using Serilog.Context;
using ILogger = Microsoft.Extensions.Logging.ILogger;

namespace OracleMS.Services
{
    /// <summary>
    /// Implementation of IApplicationLogger that provides enhanced logging capabilities
    /// </summary>
    public class ApplicationLogger : IApplicationLogger
    {
        private readonly ILogger _logger;
        private readonly string _applicationVersion;

        public ApplicationLogger(ILogger<ApplicationLogger> logger)
        {
            _logger = logger;
            _applicationVersion = GetApplicationVersion();
        }

        /// <summary>
        /// Log a debug message
        /// </summary>
        public void LogDebug(object source, string message)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            {
                _logger.LogDebug(message);
            }
        }

        /// <summary>
        /// Log an informational message
        /// </summary>
        public void LogInformation(object source, string message)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            {
                _logger.LogInformation(message);
            }
        }

        /// <summary>
        /// Log a warning message
        /// </summary>
        public void LogWarning(object source, string message)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            {
                _logger.LogWarning(message);
            }
        }

        /// <summary>
        /// Log an error message
        /// </summary>
        public void LogError(object source, string message)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            {
                _logger.LogError(message);
            }
        }

        /// <summary>
        /// Log an error message with exception details
        /// </summary>
        public void LogError(object source, string message, Exception exception)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            using (LogContext.PushProperty("ExceptionType", exception.GetType().Name))
            {
                _logger.LogError(exception, message);
            }
        }

        /// <summary>
        /// Log a critical error message
        /// </summary>
        public void LogCritical(object source, string message)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            {
                _logger.LogCritical(message);
            }
        }

        /// <summary>
        /// Log a critical error message with exception details
        /// </summary>
        public void LogCritical(object source, string message, Exception exception)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            using (LogContext.PushProperty("ExceptionType", exception.GetType().Name))
            {
                _logger.LogCritical(exception, message);
            }
        }

        /// <summary>
        /// Log a user action
        /// </summary>
        public void LogUserAction(object source, string action, string details = null)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            using (LogContext.PushProperty("ActionType", "UserAction"))
            using (LogContext.PushProperty("Action", action))
            {
                _logger.LogInformation(details ?? action);
            }
        }

        /// <summary>
        /// Log a database operation
        /// </summary>
        public void LogDatabaseOperation(object source, string operation, string details = null, long? duration = null)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            using (LogContext.PushProperty("ActionType", "DatabaseOperation"))
            using (LogContext.PushProperty("Operation", operation))
            {
                if (duration.HasValue)
                {
                    using (LogContext.PushProperty("DurationMs", duration.Value))
                    {
                        _logger.LogInformation(details ?? operation);
                    }
                }
                else
                {
                    _logger.LogInformation(details ?? operation);
                }
            }
        }

        /// <summary>
        /// Log a performance metric
        /// </summary>
        public void LogPerformance(object source, string operation, long durationMs, string details = null)
        {
            using (LogContext.PushProperty("SourceType", source?.GetType().Name ?? "Unknown"))
            using (LogContext.PushProperty("AppVersion", _applicationVersion))
            using (LogContext.PushProperty("ActionType", "Performance"))
            using (LogContext.PushProperty("Operation", operation))
            using (LogContext.PushProperty("DurationMs", durationMs))
            {
                _logger.LogInformation(details ?? $"Performance: {operation} took {durationMs}ms");
            }
        }

        /// <summary>
        /// Get the current application version
        /// </summary>
        private string GetApplicationVersion()
        {
            try
            {
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }
    }
}