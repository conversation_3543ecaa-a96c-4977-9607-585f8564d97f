using System;
using System.Data;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 序列編輯器 ViewModel
    /// </summary>
    public class SequenceEditorViewModel : BaseObjectEditorViewModel
    {
        private SequenceDefinition _originalSequenceDefinition;
        private SequenceDefinition _sequenceDefinition;
        private string _ddlPreview = string.Empty;

        /// <summary>
        /// 序列定義
        /// </summary>
        public SequenceDefinition SequenceDefinition
        {
            get => _sequenceDefinition;
            private set => SetProperty(ref _sequenceDefinition, value);
        }

        /// <summary>
        /// DDL 預覽
        /// </summary>
        public string DdlPreview
        {
            get => _ddlPreview;
            private set => SetProperty(ref _ddlPreview, value);
        }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="sequenceName">序列名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public SequenceEditorViewModel(
            string sequenceName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(sequenceName, DatabaseObjectType.Sequence, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            _sequenceDefinition = new SequenceDefinition { Name = sequenceName };
            _originalSequenceDefinition = new SequenceDefinition { Name = sequenceName };

            // 註冊屬性變更事件
            PropertyChanged += (s, e) =>
            {
                if (!_isInitializing && e.PropertyName == nameof(SequenceDefinition))
                {
                    UpdateDdlPreview();
                }
            };

            // 註冊序列定義屬性變更事件
            _sequenceDefinition.PropertyChanged += (s, e) =>
            {
                if (!_isInitializing)
                {
                    HasUnsavedChanges = true;
                    UpdateDdlPreview();
                }
            };
        }

        /// <summary>
        /// 載入序列定義
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            _isInitializing = true;
            try
            {
                // 載入序列定義
                var definition = await _objectEditorService.GetSequenceDefinitionAsync(connection, ObjectName);
                SequenceDefinition = definition;
                _originalSequenceDefinition = CloneSequenceDefinition(definition);

                // 更新 DDL 預覽
                UpdateDdlPreview();
            }
            finally
            {
                _isInitializing = false;
            }
        }

        /// <summary>
        /// 儲存序列定義
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 儲存序列定義
            await _objectEditorService.SaveSequenceDefinitionAsync(connection, SequenceDefinition);

            // 更新原始序列定義
            _originalSequenceDefinition = CloneSequenceDefinition(SequenceDefinition);
        }

        /// <summary>
        /// 產生 DDL 腳本
        /// </summary>
        /// <returns>DDL 腳本</returns>
        protected override string GenerateScript()
        {
            return GenerateAlterSequenceScript();
        }

        /// <summary>
        /// 驗證序列定義
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            return SequenceDefinition.Validate();
        }

        /// <summary>
        /// 更新 DDL 預覽
        /// </summary>
        private void UpdateDdlPreview()
        {
            DdlPreview = GenerateAlterSequenceScript();
        }

        /// <summary>
        /// 產生 ALTER SEQUENCE 腳本
        /// </summary>
        /// <returns>ALTER SEQUENCE 腳本</returns>
        private string GenerateAlterSequenceScript()
        {
            if (SequenceDefinition == null)
                return string.Empty;

            var script = $"ALTER SEQUENCE {SequenceDefinition.Owner}.{SequenceDefinition.Name}\n";

            // 增量
            script += $"  INCREMENT BY {SequenceDefinition.IncrementBy}\n";

            // 最小值
            script += $"  MINVALUE {SequenceDefinition.MinValue}\n";

            // 最大值
            if (SequenceDefinition.MaxValue == long.MaxValue)
                script += "  NOMAXVALUE\n";
            else
                script += $"  MAXVALUE {SequenceDefinition.MaxValue}\n";

            // 起始值
            script += $"  START WITH {SequenceDefinition.LastNumber}\n";

            // 快取
            if (SequenceDefinition.CacheSize <= 0)
                script += "  NOCACHE\n";
            else
                script += $"  CACHE {SequenceDefinition.CacheSize}\n";

            // 循環
            script += SequenceDefinition.IsCycling ? "  CYCLE\n" : "  NOCYCLE\n";

            // 排序
            script += SequenceDefinition.IsOrdered ? "  ORDER\n" : "  NOORDER\n";

            return script;
        }

        /// <summary>
        /// 複製序列定義
        /// </summary>
        /// <param name="source">來源序列定義</param>
        /// <returns>複製的序列定義</returns>
        private SequenceDefinition CloneSequenceDefinition(SequenceDefinition source)
        {
            return new SequenceDefinition
            {
                Name = source.Name,
                Owner = source.Owner,
                MinValue = source.MinValue,
                MaxValue = source.MaxValue,
                IncrementBy = source.IncrementBy,
                CacheSize = source.CacheSize,
                IsCycling = source.IsCycling,
                IsOrdered = source.IsOrdered,
                LastNumber = source.LastNumber
            };
        }
    }
}