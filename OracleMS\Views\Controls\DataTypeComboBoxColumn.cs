using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using OracleMS.Converters;

namespace OracleMS.Views.Controls
{
    /// <summary>
    /// 自定義的資料類型下拉選單欄位，解決 DataGridComboBoxColumn 綁定問題
    /// </summary>
    public class DataTypeComboBoxColumn : DataGridComboBoxColumn
    {
        public DataTypeComboBoxColumn()
        {
            // 直接綁定到 ViewModel 的 DataTypes 屬性
            var binding = new Binding("DataContext.DataTypes")
            {
                RelativeSource = new RelativeSource(RelativeSourceMode.FindAncestor, typeof(UserControl), 1)
            };
            
            this.ItemsSource = DefaultDataTypes.DefaultOracleDataTypes;
            
            // 嘗試綁定到 ViewModel 的 DataTypes 屬性
            BindingOperations.SetBinding(this, DataGridComboBoxColumn.ItemsSourceProperty, binding);
        }
    }
    
    /// <summary>
    /// 預設的 Oracle 資料類型清單
    /// </summary>
    public static class DefaultDataTypes
    {
        public static readonly string[] DefaultOracleDataTypes = new string[]
        {
            "VARCHAR2", "NVARCHAR2", "CHAR", "NCHAR", "NUMBER", "DATE", "TIMESTAMP", 
            "TIMESTAMP WITH TIME ZONE", "TIMESTAMP WITH LOCAL TIME ZONE", "INTERVAL YEAR TO MONTH", 
            "INTERVAL DAY TO SECOND", "BINARY_FLOAT", "BINARY_DOUBLE", "FLOAT", "LONG", "RAW", 
            "LONG RAW", "BLOB", "CLOB", "NCLOB", "BFILE", "ROWID", "UROWID", "XMLType"
        };
    }
}