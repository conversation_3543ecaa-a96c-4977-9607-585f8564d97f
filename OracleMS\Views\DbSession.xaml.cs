﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OracleMS.Factories;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using OracleMS.Views;

namespace OracleMS.Views
{
    /// <summary>
    /// DbSession.xaml 的互動邏輯
    /// </summary>
    public partial class DbSession : UserControl
    {
        private int _queryCounter = 0;
        private IDbConnection? _currentConnection;
        private ConnectionInfo? _currentConnectionInfo;

        // 共用的服務（每個編輯器共用同一個服務實例）
        private Interfaces.IDatabaseService? _sharedDatabaseService;
        private Interfaces.IScriptGeneratorService? _sharedScriptGeneratorService;
        private Interfaces.IObjectEditorService? _sharedObjectEditorService;

        public DbSession()
        {
            InitializeComponent();

            // 訂閱 Unloaded 事件
            this.Unloaded += DbSession_Unloaded;

            // 確保共用的 DatabaseService 已初始化
            EnsureSharedDatabaseServiceInitialized();

            // 初始化 ObjectExplorerView 的 ViewModel
            InitializeObjectExplorer();

            // 不在建構函式中創建 QueryEditor，等待連線設定後再創建
        }

        // 公開存取內部控制項的屬性
        public ObjectExplorerView ObjectExplorerView => objectExplorerView;
        public TabControl GetMainTabControl() => MainTabControl;
        //public QueryEditorView QueryEditorView => queryEditorView;



        // 新增查詢標籤頁的事件處理器
        private void AddQueryTab_Click(object sender, RoutedEventArgs e)
        {
            CreateNewQueryTab();
        }

        // 創建新查詢標籤頁的共用方法
        public TabItem CreateNewQueryTab()
        {
            _queryCounter++;

            // 創建新的 TabItem
            var newTabItem = new TabItem();

            // 先創建 QueryEditorView
            var queryEditor = new QueryEditorView();

            // 設置 Header 包含標題和關閉按鈕
            var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };

            var titleText = new TextBlock
            {
                Text = $"查詢 {_queryCounter}",
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var closeButton = new Button
            {
                Content = "×",
                Width = 16,
                Height = 16,
                FontSize = 10,
                FontWeight = FontWeights.Bold,
                Background = System.Windows.Media.Brushes.Transparent,
                BorderBrush = Brushes.Transparent,
                BorderThickness = new Thickness(1),
                ToolTip = "關閉標籤頁",
                Margin = new Thickness(4, 0, 0, 0)
            };

            // 關閉按鈕的點擊事件
            closeButton.Click += (s, args) =>
            {
                // 檢查是否可以關閉標籤頁
                if (CanCloseTab(queryEditor))
                {
                    // 取消訂閱事件以避免記憶體洩漏
                    if (queryEditor.DataContext is ViewModels.QueryEditorViewModel viewModel)
                    {
                        viewModel.NewQueryTabRequested -= OnNewQueryTabRequested;
                        // 釋放 ViewModel 資源
                        viewModel.Dispose();
                    }

                    // 移除 TabItem
                    MainTabControl.Items.Remove(newTabItem);

                    // 檢查關閉後是否需要新增 TabItem
                    CheckAndCreateTabIfNeeded();
                }
            };

            headerPanel.Children.Add(titleText);
            headerPanel.Children.Add(closeButton);
            newTabItem.Header = headerPanel;

            // 確保共用的 DatabaseService 已初始化
            EnsureSharedDatabaseServiceInitialized();

            // 使用共用的 DatabaseService
            if (_sharedDatabaseService != null)
            {
                // 為每個 QueryEditor 創建獨立的 ViewModel，但使用共用的 DatabaseService
                // 傳遞一個函數來獲取當前連線
                var viewModel = new ViewModels.QueryEditorViewModel(_sharedDatabaseService, () => _currentConnection);
                queryEditor.DataContext = viewModel;

                // 訂閱新增查詢標籤頁的事件
                viewModel.NewQueryTabRequested += OnNewQueryTabRequested;
            }

            newTabItem.Content = queryEditor;

            // 添加到 TabControl 並選中新標籤頁
            MainTabControl.Items.Add(newTabItem);
            MainTabControl.SelectedItem = newTabItem;

            return newTabItem;
        }

        // 初始化 ObjectExplorer
        private void InitializeObjectExplorer()
        {
            if (App.ServiceProvider != null)
            {
                // 確保所有共用服務已初始化
                EnsureSharedDatabaseServiceInitialized();
                EnsureSharedScriptGeneratorServiceInitialized();
                EnsureSharedObjectEditorServiceInitialized();

                if (_sharedDatabaseService != null && _sharedScriptGeneratorService != null)
                {
                    var objectExplorerViewModel = new ViewModels.ObjectExplorerViewModel(_sharedDatabaseService, _sharedScriptGeneratorService, _sharedObjectEditorService);
                    objectExplorerView.DataContext = objectExplorerViewModel;
                    
                    // 訂閱物件編輯器開啟事件
                    objectExplorerViewModel.OpenTableDataRequested += OnOpenTableDataRequested;
                    objectExplorerViewModel.OpenTableDesignRequested += OnOpenTableDesignRequested;
                    objectExplorerViewModel.GenerateScriptRequested += OnGenerateScriptRequested;
                    objectExplorerViewModel.OpenViewEditorRequested += OnOpenViewEditorRequested;
                    objectExplorerViewModel.OpenProcedureEditorRequested += OnOpenProcedureEditorRequested;
                    objectExplorerViewModel.OpenFunctionEditorRequested += OnOpenFunctionEditorRequested;
                    objectExplorerViewModel.OpenPackageEditorRequested += OnOpenPackageEditorRequested;
                    objectExplorerViewModel.OpenSequenceEditorRequested += OnOpenSequenceEditorRequested;
                    objectExplorerViewModel.OpenTriggerEditorRequested += OnOpenTriggerEditorRequested;
                    objectExplorerViewModel.OpenIndexEditorRequested += OnOpenIndexEditorRequested;
                }
            }
        }
        
        // 處理開啟資料表資料的事件
        private void OnOpenTableDataRequested(object? sender, ViewModels.OpenTableDataEventArgs e)
        {
            // 這裡將在實作資料表資料編輯器後更新
            MessageBox.Show($"開啟資料表資料: {e.TableName}");
        }
        
        // 處理開啟資料表設計的事件
        private void OnOpenTableDesignRequested(object? sender, ViewModels.OpenTableDesignEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Table, e.TableName);
        }
        
        // 處理產生腳本的事件
        private void OnGenerateScriptRequested(object? sender, ViewModels.GenerateScriptEventArgs e)
        {
            // 創建新的查詢標籤頁並填入腳本
            var tabItem = CreateNewQueryTab();
            if (tabItem?.Content is QueryEditorView queryEditor && 
                queryEditor.DataContext is ViewModels.QueryEditorViewModel viewModel)
            {
                viewModel.SqlText = e.Script;
            }
        }

        // 確保共用的 DatabaseService 已初始化
        private void EnsureSharedDatabaseServiceInitialized()
        {
            if (_sharedDatabaseService == null && App.ServiceProvider != null)
            {
                // 創建新的 DatabaseService 實例（每個 DbSession 有自己的）
                var databaseRepository = App.ServiceProvider.GetService<Interfaces.IDatabaseRepository>();
                var logger = App.ServiceProvider.GetService<Microsoft.Extensions.Logging.ILogger<Services.DatabaseService>>();

                if (databaseRepository != null && logger != null)
                {
                    _sharedDatabaseService = new Services.DatabaseService(databaseRepository, logger);

                    // 調試資訊：記錄 DatabaseService 實例
                    System.Diagnostics.Debug.WriteLine($"DbSession - Created shared DatabaseService with HashCode: {_sharedDatabaseService.GetHashCode()}");
                }
                else
                {
                    throw new InvalidOperationException("Required services not registered");
                }
            }
            else if (_sharedDatabaseService != null)
            {
                // 調試資訊：使用現有的 DatabaseService
                System.Diagnostics.Debug.WriteLine($"DbSession - Using existing shared DatabaseService with HashCode: {_sharedDatabaseService.GetHashCode()}");
            }
        }
        
        // 確保共用的 ScriptGeneratorService 已初始化
        private void EnsureSharedScriptGeneratorServiceInitialized()
        {
            if (_sharedScriptGeneratorService == null && App.ServiceProvider != null)
            {
                _sharedScriptGeneratorService = App.ServiceProvider.GetService<Interfaces.IScriptGeneratorService>();
                
                if (_sharedScriptGeneratorService == null)
                {
                    throw new InvalidOperationException("IScriptGeneratorService not registered");
                }
                
                System.Diagnostics.Debug.WriteLine($"DbSession - Using ScriptGeneratorService with HashCode: {_sharedScriptGeneratorService.GetHashCode()}");
            }
        }
        
        // 確保共用的 ObjectEditorService 已初始化
        private void EnsureSharedObjectEditorServiceInitialized()
        {
            if (_sharedObjectEditorService == null && App.ServiceProvider != null)
            {
                // 從 DI 容器獲取 ObjectEditorService
                _sharedObjectEditorService = App.ServiceProvider.GetService<Interfaces.IObjectEditorService>();

                if (_sharedObjectEditorService == null)
                {
                    throw new InvalidOperationException("IObjectEditorService not registered");
                }
                
                // 調試資訊：記錄 ObjectEditorService 實例
                System.Diagnostics.Debug.WriteLine($"DbSession - Using ObjectEditorService with HashCode: {_sharedObjectEditorService.GetHashCode()}");
            }
            else if (_sharedObjectEditorService != null)
            {
                // 調試資訊：使用現有的 ObjectEditorService
                System.Diagnostics.Debug.WriteLine($"DbSession - Using existing shared ObjectEditorService with HashCode: {_sharedObjectEditorService.GetHashCode()}");
            }
        }

        // 處理新增查詢標籤頁的事件
        private void OnNewQueryTabRequested(object? sender, EventArgs e)
        {
            // 新增一個新的查詢標籤頁
            CreateNewQueryTab();
        }

        // 檢查是否可以關閉標籤頁
        private bool CanCloseTab(QueryEditorView queryEditor)
        {
            // 計算目前有多少個 QueryEditor TabItem
            int queryTabCount = CountQueryEditorTabs();

            if (queryEditor.DataContext is ViewModels.QueryEditorViewModel viewModel)
            {
                // 1. 如果有未儲存的變更，則先詢問是否關閉
                if (viewModel.HasUnsavedChanges)
                {
                    var result = MessageBox.Show(
                        $"查詢 '{viewModel.FileName}' 有未儲存的變更。\n\n是否要關閉而不儲存？",
                        "確認關閉",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question,
                        MessageBoxResult.No);

                    if (result != MessageBoxResult.Yes)
                    {
                        return false; // 用戶選擇不關閉
                    }
                    // 用戶確認關閉，繼續執行下面的邏輯
                }

                //2.如果沒有未儲存的變更，檢查 TabItem 數量
                if (!viewModel.HasUnsavedChanges && queryTabCount <= 1)
                {
                    // 只有1個或更少，不關閉也不須做任何動作
                    return false;
                }
            }

            // 3. 超過1個則可以關閉，但需要檢查關閉後的狀態
            return true;
        }

        // 計算目前有多少個 QueryEditor TabItem
        private int CountQueryEditorTabs()
        {
            int count = 0;
            foreach (TabItem tabItem in MainTabControl.Items)
            {
                if (tabItem.Content is QueryEditorView)
                {
                    count++;
                }
            }
            return count;
        }

        // 檢查關閉後是否需要新增 TabItem
        private void CheckAndCreateTabIfNeeded()
        {
            // 如果關閉後沒有任何 QueryEditor TabItem，則新增一個
            if (CountQueryEditorTabs() == 0)
            {
                CreateNewQueryTab();
            }
        }

        // 設定資料庫連線
        public void SetDatabaseConnection(IDbConnection connection, ConnectionInfo connectionInfo)
        {
            _currentConnection = connection;
            _currentConnectionInfo = connectionInfo;

            // 確保在 UI 執行緒中執行 UI 相關操作
            Dispatcher.Invoke(() =>
            {
                // 如果還沒有任何 QueryEditor，創建初始的 QueryEditor
                if (CountQueryEditorTabs() == 0)
                {
                    CreateNewQueryTab();
                }
                // 注意：不需要設定連線給個別的 ViewModel，因為它們都使用共用的 DatabaseService
                // 而 DatabaseService 的方法會接收連線作為參數

                // 將連線設定給 ObjectExplorerView
                if (objectExplorerView?.DataContext is ViewModels.ObjectExplorerViewModel objectExplorerViewModel)
                {
                    objectExplorerViewModel.SetConnection(connection);
                }
            });
        }

        // 取得目前的資料庫連線
        public IDbConnection? GetCurrentConnection()
        {
            return _currentConnection;
        }

        // 取得目前的連線資訊
        public ConnectionInfo? GetCurrentConnectionInfo()
        {
            return _currentConnectionInfo;
        }



        // 清理資源
        private void CleanupResources()
        {
            // 清理所有 QueryEditor 的 ViewModel
            foreach (TabItem tabItem in MainTabControl.Items)
            {
                if (tabItem.Content is QueryEditorView queryEditor &&
                    queryEditor.DataContext is ViewModels.QueryEditorViewModel viewModel)
                {
                    // 取消訂閱事件
                    viewModel.NewQueryTabRequested -= OnNewQueryTabRequested;
                    // 釋放 ViewModel 資源
                    viewModel.Dispose();
                }
                else if (tabItem.Content is UserControl objectEditor &&
                         objectEditor.DataContext is IDisposable disposableViewModel)
                {
                    // 取消訂閱 TableEditorViewModel 事件
                    if (objectEditor.DataContext is TableEditorViewModel tableEditorViewModel)
                    {
                        tableEditorViewModel.OpenIndexEditorRequested -= OnOpenIndexEditorRequested;
                    }

                    // 釋放物件編輯器 ViewModel 資源
                    disposableViewModel.Dispose();
                }
            }

            // 清理 DatabaseService（每個 DbSession 有自己的實例）
            if (_sharedDatabaseService is IDisposable disposableService)
            {
                disposableService.Dispose();
            }
            _sharedDatabaseService = null;
            
            // 清理 ObjectEditorService（每個 DbSession 有自己的實例）
            if (_sharedObjectEditorService is IDisposable disposableObjectEditorService)
            {
                disposableObjectEditorService.Dispose();
            }
            _sharedObjectEditorService = null;
        }

        // 當 UserControl 被卸載時清理資源
        private void DbSession_Unloaded(object sender, RoutedEventArgs e)
        {
            CleanupResources();
        }

        /// <summary>
        /// 創建物件編輯器標籤頁
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <returns>創建的 TabItem</returns>
        public TabItem? CreateObjectEditorTab(DatabaseObjectType objectType, string objectName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(objectName))
                    return null;

            // 確保所有共用服務已初始化
            EnsureSharedDatabaseServiceInitialized();
            EnsureSharedScriptGeneratorServiceInitialized();
            EnsureSharedObjectEditorServiceInitialized();

            if (_sharedDatabaseService == null || _sharedScriptGeneratorService == null || _sharedObjectEditorService == null)
                return null;

            // 檢查是否已經開啟了相同的物件編輯器
            foreach (TabItem tabItem in MainTabControl.Items)
            {
                if (tabItem.Tag is Tuple<DatabaseObjectType, string> tag &&
                    tag.Item1 == objectType && tag.Item2.Equals(objectName, StringComparison.OrdinalIgnoreCase))
                {
                    // 已經開啟了相同的物件編輯器，選中它
                    MainTabControl.SelectedItem = tabItem;
                    return tabItem;
                }
            }

            // 創建新的 TabItem
            var newTabItem = new TabItem();

            // 獲取 logger
            var logger = App.ServiceProvider?.GetService<ILogger<DbSession>>();
            if (logger == null)
                throw new InvalidOperationException("Logger service not available");

            // 使用工廠創建物件編輯器
            var objectEditor = ObjectEditorFactory.CreateEditor(
                objectType,
                objectName,
                _sharedDatabaseService,
                _sharedScriptGeneratorService,
                _sharedObjectEditorService,
                () => _currentConnection,
                logger);

            // 設置 TabItem 的 Tag 以便識別
            newTabItem.Tag = new Tuple<DatabaseObjectType, string>(objectType, objectName);

            // 設置 Header 包含標題和關閉按鈕
            var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };

            var titleText = new TextBlock
            {
                Text = ObjectEditorFactory.GetEditorTitle(objectType, objectName),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(0, 0, 8, 0)
            };

            var closeButton = new Button
            {
                Content = "×",
                Width = 16,
                Height = 16,
                FontSize = 10,
                FontWeight = FontWeights.Bold,
                Background = System.Windows.Media.Brushes.Transparent,
                BorderBrush = Brushes.Transparent,
                BorderThickness = new Thickness(1),
                ToolTip = "關閉標籤頁",
                Margin = new Thickness(4, 0, 0, 0)
            };

            // 關閉按鈕的點擊事件
            closeButton.Click += (s, args) =>
            {
                try
                {
                    // 檢查是否可以關閉標籤頁
                    if (CanCloseObjectEditorTab(objectEditor))
                    {
                        // 取消訂閱 TableEditorViewModel 事件
                        if (objectEditor.DataContext is TableEditorViewModel tableEditorViewModel)
                        {
                            tableEditorViewModel.OpenIndexEditorRequested -= OnOpenIndexEditorRequested;
                        }

                        // 釋放資源
                        if (objectEditor.DataContext is IDisposable disposable)
                        {
                            try
                            {
                                disposable.Dispose();
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"釋放物件編輯器資源時發生錯誤: {ex.Message}");
                            }
                        }

                        // 移除 TabItem
                        try
                        {
                            MainTabControl.Items.Remove(newTabItem);
                            MainTabControl.SelectedIndex = 0; // 選中第一個標籤頁
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"移除標籤頁時發生錯誤: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"關閉標籤頁時發生錯誤: {ex.Message}");
                    // 即使發生錯誤，也嘗試移除標籤頁
                    try
                    {
                        MainTabControl.Items.Remove(newTabItem);
                    }
                    catch (Exception removeEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"強制移除標籤頁時發生錯誤: {removeEx.Message}");
                    }
                }
            };

            headerPanel.Children.Add(titleText);
            headerPanel.Children.Add(closeButton);
            newTabItem.Header = headerPanel;
            newTabItem.Content = objectEditor;

            // 添加到 TabControl 並選中新標籤頁
            MainTabControl.Items.Add(newTabItem);
            MainTabControl.SelectedItem = newTabItem;

            // 訂閱 TableEditorViewModel 的索引編輯器開啟事件
            if (objectEditor.DataContext is TableEditorViewModel tableEditorViewModel)
            {
                tableEditorViewModel.OpenIndexEditorRequested += OnOpenIndexEditorRequested;
            }

            // 初始化物件編輯器 - 在 UI 執行緒上執行
            if (objectEditor.DataContext is BaseObjectEditorViewModel viewModel)
            {
                _ = Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        await viewModel.InitializeAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"初始化物件編輯器失敗: {ex.Message}\n\n詳細錯誤:\n{ex}", "錯誤",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                });
            }

                return newTabItem;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"開啟物件編輯器失敗: {ex.Message}\n\n詳細錯誤:\n{ex}", "錯誤",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }

        /// <summary>
        /// 檢查是否可以關閉物件編輯器標籤頁
        /// </summary>
        /// <param name="objectEditor">物件編輯器</param>
        /// <returns>是否可以關閉</returns>
        private bool CanCloseObjectEditorTab(UserControl objectEditor)
        {
            try
            {
                // 檢查是否有未儲存的變更
                if (objectEditor.DataContext is ISaveable saveable && saveable.HasUnsavedChanges)
                {
                    MessageBoxResult result;

                    // 確保在 UI 執行緒上顯示對話框
                    if (Application.Current.Dispatcher.CheckAccess())
                    {
                        result = MessageBox.Show(
                            "物件編輯器有未儲存的變更。\n\n是否要關閉而不儲存？",
                            "確認關閉",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question,
                            MessageBoxResult.No);
                    }
                    else
                    {
                        result = (MessageBoxResult)Application.Current.Dispatcher.Invoke(() =>
                        {
                            return MessageBox.Show(
                                "物件編輯器有未儲存的變更。\n\n是否要關閉而不儲存？",
                                "確認關閉",
                                MessageBoxButton.YesNo,
                                MessageBoxImage.Question,
                                MessageBoxResult.No);
                        });
                    }

                    if (result != MessageBoxResult.Yes)
                    {
                        return false; // 用戶選擇不關閉
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"檢查是否可以關閉標籤頁時發生錯誤: {ex.Message}");
                // 發生錯誤時，為了安全起見，允許關閉
                return true;
            }
        }
   // 處理開啟檢視表編輯器的事件
        private void OnOpenViewEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.View, e.ObjectName);
        }
        
        // 處理開啟預存程序編輯器的事件
        private void OnOpenProcedureEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Procedure, e.ObjectName);
        }
        
        // 處理開啟函數編輯器的事件
        private void OnOpenFunctionEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Function, e.ObjectName);
        }
        
        // 處理開啟套件編輯器的事件
        private void OnOpenPackageEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Package, e.ObjectName);
        }
        
        // 處理開啟序列編輯器的事件
        private void OnOpenSequenceEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Sequence, e.ObjectName);
        }
        
        // 處理開啟觸發器編輯器的事件
        private void OnOpenTriggerEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Trigger, e.ObjectName);
        }
        
        // 處理開啟索引編輯器的事件
        private void OnOpenIndexEditorRequested(object? sender, ViewModels.OpenObjectEditorEventArgs e)
        {
            CreateObjectEditorTab(DatabaseObjectType.Index, e.ObjectName);
        }

        // 處理從 TableEditorView 開啟索引編輯器的事件
        private void OnOpenIndexEditorRequested(object? sender, ViewModels.OpenIndexEditorEventArgs e)
        {
            CreateIndexEditorTab(e.IndexInfo);
        }

        /// <summary>
        /// 創建索引編輯器標籤頁
        /// </summary>
        /// <param name="indexInfo">索引編輯器參數</param>
        /// <returns>創建的 TabItem</returns>
        public TabItem? CreateIndexEditorTab(IndexEditorInfo indexInfo)
        {
            try
            {
                if (indexInfo == null)
                    return null;

                // 確保所有共用服務已初始化
                EnsureSharedDatabaseServiceInitialized();
                EnsureSharedScriptGeneratorServiceInitialized();
                EnsureSharedObjectEditorServiceInitialized();

                if (_sharedDatabaseService == null || _sharedScriptGeneratorService == null || _sharedObjectEditorService == null)
                    return null;

                // 檢查是否已經開啟了相同的索引編輯器
                string tabKey = indexInfo.IsEditMode 
                    ? $"EditIndex_{indexInfo.Schema}_{indexInfo.TableName}_{indexInfo.IndexName}"
                    : $"CreateIndex_{indexInfo.Schema}_{indexInfo.TableName}";

                foreach (TabItem tabItem in MainTabControl.Items)
                {
                    if (tabItem.Tag is string tag && tag == tabKey)
                    {
                        // 已經開啟了相同的索引編輯器，選中它
                        MainTabControl.SelectedItem = tabItem;
                        return tabItem;
                    }
                }

                // 創建新的 TabItem
                var newTabItem = new TabItem();

                // 獲取 logger
                var logger = App.ServiceProvider?.GetService<ILogger<DbSession>>();
                if (logger == null)
                    throw new InvalidOperationException("Logger service not available");

                // 創建 IndexEditorView
                var indexEditorView = new IndexEditorView();
                var indexEditorViewModel = new IndexEditorViewModel(
                    indexInfo,
                    _sharedDatabaseService,
                    _sharedScriptGeneratorService,
                    _sharedObjectEditorService,
                    () => _currentConnection,
                    logger);

                indexEditorView.DataContext = indexEditorViewModel;

                // 設置 TabItem 的 Tag 以便識別
                newTabItem.Tag = tabKey;

                // 設置 Header 包含標題和關閉按鈕
                var headerPanel = new StackPanel { Orientation = Orientation.Horizontal };

                string title = indexInfo.IsEditMode 
                    ? $"編輯索引 - {indexInfo.IndexName}"
                    : $"新增索引 - {indexInfo.TableName}";

                var titleText = new TextBlock
                {
                    Text = title,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(0, 0, 8, 0)
                };

                var closeButton = new Button
                {
                    Content = "×",
                    Width = 16,
                    Height = 16,
                    FontSize = 10,
                    FontWeight = FontWeights.Bold,
                    Background = System.Windows.Media.Brushes.Transparent,
                    BorderBrush = Brushes.Transparent,
                    BorderThickness = new Thickness(1),
                    ToolTip = "關閉標籤頁",
                    Margin = new Thickness(4, 0, 0, 0)
                };

                // 關閉按鈕的點擊事件
                closeButton.Click += (s, args) =>
                {
                    try
                    {
                        // 檢查是否可以關閉標籤頁
                        if (CanCloseObjectEditorTab(indexEditorView))
                        {
                            // 釋放資源
                            if (indexEditorView.DataContext is IDisposable disposable)
                            {
                                try
                                {
                                    disposable.Dispose();
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"釋放索引編輯器資源時發生錯誤: {ex.Message}");
                                }
                            }

                            // 移除 TabItem
                            try
                            {
                                MainTabControl.Items.Remove(newTabItem);
                                if (MainTabControl.Items.Count > 0)
                                    MainTabControl.SelectedIndex = 0; // 選中第一個標籤頁
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"移除標籤頁時發生錯誤: {ex.Message}");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"關閉標籤頁時發生錯誤: {ex.Message}");
                        // 即使發生錯誤，也嘗試移除標籤頁
                        try
                        {
                            MainTabControl.Items.Remove(newTabItem);
                        }
                        catch (Exception removeEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"強制移除標籤頁時發生錯誤: {removeEx.Message}");
                        }
                    }
                };

                headerPanel.Children.Add(titleText);
                headerPanel.Children.Add(closeButton);
                newTabItem.Header = headerPanel;
                newTabItem.Content = indexEditorView;

                // 添加到 TabControl 並選中新標籤頁
                MainTabControl.Items.Add(newTabItem);
                MainTabControl.SelectedItem = newTabItem;

                // 初始化索引編輯器 - 在 UI 執行緒上執行
                _ = Dispatcher.InvokeAsync(async () =>
                {
                    try
                    {
                        await indexEditorViewModel.InitializeAsync();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"初始化索引編輯器失敗: {ex.Message}\n\n詳細錯誤:\n{ex}", "錯誤",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                });

                return newTabItem;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"開啟索引編輯器失敗: {ex.Message}\n\n詳細錯誤:\n{ex}", "錯誤",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }
    }
}