<UserControl x:Class="OracleMS.Views.ViewEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             xmlns:dgx="urn:tom-englert.de/DataGridExtensions"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:ViewEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MinWidth" Value="75"/>
            <Setter Property="Height" Value="28"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/> <!-- Content Area -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Command="{Binding SaveCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="儲存檢視表變更 (Ctrl+S)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" Margin="0,0,4,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding RefreshCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重新載入檢視表定義">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" Margin="0,0,4,0"/>
                        <TextBlock Text="重新載入"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding ExecuteTestCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="執行測試查詢">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="▶" Margin="0,0,4,0" FontWeight="Bold" Foreground="Green"/>
                        <TextBlock Text="執行測試"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding ValidateSqlCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="驗證 SQL 語法">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✓" Margin="0,0,4,0" FontWeight="Bold" Foreground="Blue"/>
                        <TextBlock Text="驗證語法"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding GenerateScriptCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="產生 DDL 腳本">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" Margin="0,0,4,0"/>
                        <TextBlock Text="產生腳本"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <!-- Progress indicator -->
            <ProgressBar Width="100" Height="16" 
                         IsIndeterminate="True"
                         Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                         Margin="10,0"/>
        </ToolBar>

        <!-- Content Area -->
        <TabControl Grid.Row="1">
            <!-- SQL Definition Tab -->
            <TabItem Header="SQL 定義">
                <Grid>
                    <Border BorderBrush="Gray" BorderThickness="1">
                        <avalonedit:TextEditor x:Name="SqlDefinitionEditor"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               ShowLineNumbers="True"
                                               WordWrap="False"
                                               HorizontalScrollBarVisibility="Auto"
                                               VerticalScrollBarVisibility="Auto"
                                               Background="White"
                                               Foreground="Black">
                            <avalonedit:TextEditor.Options>
                                <avalonedit:TextEditorOptions ShowSpaces="False"
                                                              ShowTabs="False"
                                                              ShowEndOfLine="False"
                                                              ShowBoxForControlCharacters="False"
                                                              ConvertTabsToSpaces="True"
                                                              IndentationSize="4"/>
                            </avalonedit:TextEditor.Options>
                        </avalonedit:TextEditor>
                    </Border>
                </Grid>
            </TabItem>

            <!-- Test Results Tab -->
            <TabItem Header="測試結果">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Results toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <TextBlock Text="{Binding RowCount, StringFormat='共 {0} 筆資料'}" 
                                   Style="{StaticResource StatusTextStyle}"
                                   Visibility="{Binding HasTestResults, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        <Separator/>
                        <Button Command="{Binding ExportResultsCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="匯出結果集">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📊" Margin="0,0,4,0"/>
                                    <TextBlock Text="匯出結果"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Results content -->
                    <Grid Grid.Row="1">
                        <!-- Success result -->
                        <DataGrid x:Name="ResultsDataGrid"
                                  ItemsSource="{Binding TestResults.DefaultView, IsAsync=True}"
                                  AutoGenerateColumns="True"
                                  IsReadOnly="True"
                                  GridLinesVisibility="All"
                                  HeadersVisibility="All"
                                  CanUserReorderColumns="True"
                                  CanUserResizeColumns="True"
                                  CanUserSortColumns="True"
                                  AlternatingRowBackground="LightGray"
                                  Visibility="{Binding HasTestResults, Converter={StaticResource BooleanToVisibilityConverter}}"
                                  EnableRowVirtualization="True"
                                  EnableColumnVirtualization="True"
                                  VirtualizingPanel.VirtualizationMode="Recycling"
                                  VirtualizingPanel.IsVirtualizing="True"
                                  VirtualizingPanel.ScrollUnit="Pixel"
                                  VirtualizingPanel.CacheLengthUnit="Item"
                                  VirtualizingPanel.CacheLength="20,20"
                                  ScrollViewer.CanContentScroll="True"
                                  ScrollViewer.IsDeferredScrollingEnabled="False"
                                  dgx:DataGridFilter.IsAutoFilterEnabled="True"
                                  dgx:Tools.ApplyInitialSorting="True">
                            <DataGrid.ContextMenu>
                                <ContextMenu>
                                    <MenuItem Header="複製" Click="CopyCell_Click"/>
                                    <MenuItem Header="複製行" Click="CopyRow_Click"/>
                                    <MenuItem Header="複製所有" Click="CopyAll_Click"/>
                                    <Separator/>
                                    <MenuItem Header="匯出至 CSV"
                                              Command="{Binding ExportResultsCommand}"/>
                                </ContextMenu>
                            </DataGrid.ContextMenu>
                        </DataGrid>

                        <!-- No results message -->
                        <TextBlock Text="尚未執行測試查詢或查詢未返回結果"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="Gray"
                                   FontSize="14"
                                   Visibility="{Binding HasTestResults, Converter={x:Static local:Converters.InverseBooleanToVisibilityConverter}}"/>

                        <!-- Error result -->
                        <TextBox Text="{Binding ErrorMessage, Mode=OneWay}"
                                 IsReadOnly="True"
                                 Background="LightPink"
                                 Foreground="DarkRed"
                                 FontFamily="Consolas"
                                 TextWrapping="Wrap"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Auto"
                                 Visibility="{Binding HasError, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- Columns Tab -->
            <TabItem Header="欄位資訊">
                <Grid>
                    <!-- Columns DataGrid -->
                    <DataGrid x:Name="ColumnsDataGrid"
                              ItemsSource="{Binding ColumnInfo}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="欄位名稱" 
                                                Binding="{Binding Name}" 
                                                Width="150"/>
                            <DataGridTextColumn Header="資料類型" 
                                                Binding="{Binding DataType}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="長度" 
                                                Binding="{Binding Length}" 
                                                Width="60"/>
                            <DataGridTextColumn Header="精度" 
                                                Binding="{Binding Precision}" 
                                                Width="60"/>
                            <DataGridTextColumn Header="小數位數" 
                                                Binding="{Binding Scale}" 
                                                Width="80"/>
                            <DataGridCheckBoxColumn Header="可為空值" 
                                                    Binding="{Binding IsNullable}" 
                                                    Width="80"/>
                            <DataGridTextColumn Header="備註" 
                                                Binding="{Binding Comments}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Dependencies Tab -->
            <TabItem Header="相依性">
                <Grid>
                    <!-- Dependencies DataGrid -->
                    <DataGrid x:Name="DependenciesDataGrid"
                              ItemsSource="{Binding Dependencies}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="物件名稱" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            <DataGridTextColumn Header="物件類型" 
                                                Binding="{Binding Type}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="擁有者" 
                                                Binding="{Binding Owner}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="相依類型" 
                                                Binding="{Binding DependencyType}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="描述" 
                                                Binding="{Binding Description}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding LastExecutionTime, StringFormat='執行時間: {0:F2} 秒'}" 
                           Style="{StaticResource StatusTextStyle}"
                           Visibility="{Binding LastExecutionTime, Converter={x:Static local:Converters.TimeSpanToVisibilityConverter}}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Text" Value="載入中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding HasUnsavedChanges}" Value="True">
                                    <Setter Property="Text" Value="已修改"/>
                                    <Setter Property="Foreground" Value="Green"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>