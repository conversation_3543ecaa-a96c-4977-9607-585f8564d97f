# TableEditor 關閉當機問題修復

## 問題描述

在 TableEditorView 中修改欄位資料後，按關閉 tableEditor，在對話框回答「不儲存」就關閉時，程式立即當機，對滑鼠鍵盤無任何反應。

## 問題分析

經過程式碼分析，發現以下幾個可能導致當機的問題：

### 1. 非同步操作的不當處理
在 `TableEditorViewModel.OnTableDefinitionChanged()` 方法中：
```csharp
private void OnTableDefinitionChanged()
{
    if (_isInitializing)
        return;

    HasUnsavedChanges = true;
    UpdateDdlPreviewAsync().ConfigureAwait(false); // 問題：在同步方法中呼叫非同步方法
}
```

### 2. Dispose 方法中的同步等待
在 `BaseObjectEditorViewModel.Dispose()` 方法中：
```csharp
if (HasUnsavedChanges)
{
    SaveEditorStateAsync().ConfigureAwait(false).GetAwaiter().GetResult(); // 問題：可能導致死鎖
}
```

### 3. 事件訂閱未正確取消
TableEditorViewModel 訂閱了多個事件，但在 Dispose 時沒有正確取消訂閱，可能導致記憶體洩漏和意外的事件觸發。

### 4. UI 執行緒操作問題
在非 UI 執行緒上顯示 MessageBox 可能導致當機。

## 修復方案

### 1. 修復非同步操作處理
修改 `TableEditorViewModel.OnTableDefinitionChanged()` 方法：
```csharp
private void OnTableDefinitionChanged()
{
    if (_isInitializing || _disposed)
        return;

    HasUnsavedChanges = true;
    
    // 使用 Task.Run 避免在 UI 執行緒上執行非同步操作
    Task.Run(async () =>
    {
        try
        {
            await UpdateDdlPreviewAsync();
        }
        catch (Exception ex)
        {
            // 記錄錯誤但不拋出異常，避免程式當機
            System.Diagnostics.Debug.WriteLine($"更新 DDL 預覽時發生錯誤: {ex.Message}");
        }
    });
}
```

### 2. 修復 Dispose 方法
重新設計 `BaseObjectEditorViewModel.Dispose()` 方法：
- 先設定 `_disposed` 標記，避免其他操作繼續執行
- 使用 `Task.Run` 避免同步等待非同步操作
- 添加異常處理，確保即使發生錯誤也能正確釋放資源

### 3. 添加事件取消訂閱
在 `TableEditorViewModel.OnDisposing()` 方法中：
```csharp
protected override void OnDisposing()
{
    try
    {
        // 取消訂閱事件以避免記憶體洩漏和潛在的當機
        if (TableDefinition != null)
        {
            TableDefinition.PropertyChanged -= (s, e) => OnTableDefinitionChanged();
            TableDefinition.Columns.CollectionChanged -= (s, e) => OnTableDefinitionChanged();
            // ... 其他事件取消訂閱
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"清理 TableEditorViewModel 資源時發生錯誤: {ex.Message}");
    }
}
```

### 4. 修復 UI 執行緒問題
修改 `DbSession.CanCloseObjectEditorTab()` 方法：
```csharp
private bool CanCloseObjectEditorTab(UserControl objectEditor)
{
    try
    {
        if (objectEditor.DataContext is ISaveable saveable && saveable.HasUnsavedChanges)
        {
            MessageBoxResult result;
            
            // 確保在 UI 執行緒上顯示對話框
            if (Application.Current.Dispatcher.CheckAccess())
            {
                result = MessageBox.Show(...);
            }
            else
            {
                result = (MessageBoxResult)Application.Current.Dispatcher.Invoke(() =>
                {
                    return MessageBox.Show(...);
                });
            }
            
            if (result != MessageBoxResult.Yes)
            {
                return false;
            }
        }
        return true;
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"檢查是否可以關閉標籤頁時發生錯誤: {ex.Message}");
        return true; // 發生錯誤時，為了安全起見，允許關閉
    }
}
```

## 測試驗證

創建了 `TableEditorCloseTest.cs` 測試類別，包含以下測試案例：
1. `TableEditor_CloseWithUnsavedChanges_ShouldNotCrash` - 測試有未儲存變更時關閉不會當機
2. `TableEditor_MultipleDisposeCall_ShouldNotCrash` - 測試多次呼叫 Dispose 不會當機
3. `TableEditor_DisposeWhileUpdatingDdl_ShouldNotCrash` - 測試在 DDL 更新過程中關閉不會當機

## 預期效果

修復後，TableEditor 在以下情況下應該不會當機：
1. 修改欄位資料後選擇「不儲存」關閉
2. 在 DDL 預覽更新過程中快速關閉
3. 多次嘗試關閉編輯器
4. 在非 UI 執行緒上觸發關閉操作

## 注意事項

1. 所有異常都被捕獲並記錄到 Debug 輸出，避免未處理的異常導致程式當機
2. 使用 `Task.Run` 來處理非同步操作，避免 UI 執行緒阻塞
3. 正確的事件取消訂閱避免記憶體洩漏
4. 確保所有 UI 操作都在 UI 執行緒上執行
