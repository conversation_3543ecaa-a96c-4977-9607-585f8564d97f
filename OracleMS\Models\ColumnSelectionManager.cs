using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace OracleMS.Models
{
    /// <summary>
    /// 欄位選擇管理器，管理可用欄位和已選欄位的ObservableCollection
    /// </summary>
    public class ColumnSelectionManager
    {
        /// <summary>
        /// 可用欄位集合
        /// </summary>
        public ObservableCollection<string> AvailableColumns { get; }

        /// <summary>
        /// 已選欄位集合
        /// </summary>
        public ObservableCollection<string> SelectedColumns { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        public ColumnSelectionManager()
        {
            AvailableColumns = new ObservableCollection<string>();
            SelectedColumns = new ObservableCollection<string>();
        }

        /// <summary>
        /// 將欄位從可用欄位移動到已選欄位
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        public void MoveToSelected(string columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            if (AvailableColumns.Contains(columnName))
            {
                AvailableColumns.Remove(columnName);
                SelectedColumns.Add(columnName);
            }
        }

        /// <summary>
        /// 將欄位從已選欄位移回可用欄位
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        public void MoveToAvailable(string columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            if (SelectedColumns.Contains(columnName))
            {
                SelectedColumns.Remove(columnName);
                AvailableColumns.Add(columnName);
            }
        }

        /// <summary>
        /// 在已選欄位中將指定欄位上移
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        public void MoveUp(string columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            var index = SelectedColumns.IndexOf(columnName);
            if (index > 0)
            {
                SelectedColumns.Move(index, index - 1);
            }
        }

        /// <summary>
        /// 在已選欄位中將指定欄位下移
        /// </summary>
        /// <param name="columnName">欄位名稱</param>
        public void MoveDown(string columnName)
        {
            if (string.IsNullOrWhiteSpace(columnName))
                return;

            var index = SelectedColumns.IndexOf(columnName);
            if (index >= 0 && index < SelectedColumns.Count - 1)
            {
                SelectedColumns.Move(index, index + 1);
            }
        }

        /// <summary>
        /// 載入指定資料表的欄位到可用欄位集合
        /// </summary>
        /// <param name="schema">Schema名稱</param>
        /// <param name="tableName">資料表名稱</param>
        public void LoadTableColumns(string schema, string tableName)
        {
            // 清空現有的欄位集合
            AvailableColumns.Clear();
            SelectedColumns.Clear();

            // 注意：這個方法目前只是一個骨架實作
            // 實際的資料庫查詢邏輯需要在後續的任務中整合
            // 這裡暫時不實作資料庫查詢，因為這需要依賴於資料庫服務
            // 在實際使用時，這個方法會透過IDatabaseRepository來查詢資料表的欄位資訊
        }

        /// <summary>
        /// 從索引欄位定義集合填充已選欄位
        /// </summary>
        /// <param name="columns">索引欄位定義集合</param>
        public void PopulateSelectedColumns(IEnumerable<IndexColumnDefinition> columns)
        {
            try
            {
                Console.WriteLine("[ColumnSelectionManager] Starting column population process");

                if (columns == null)
                {
                    Console.WriteLine("[ColumnSelectionManager] Warning: Columns collection is null, skipping population");
                    return;
                }

                var columnList = columns.ToList();
                Console.WriteLine($"[ColumnSelectionManager] Processing {columnList.Count} columns for population");

                if (columnList.Count == 0)
                {
                    Console.WriteLine("[ColumnSelectionManager] Info: No columns to populate, clearing selected columns");
                    SelectedColumns.Clear();
                    return;
                }

                // Clear existing selections
                Console.WriteLine("[ColumnSelectionManager] Clearing existing selected columns");
                SelectedColumns.Clear();

                // Sort columns by Position property before adding
                var sortedColumns = columnList.OrderBy(c => c.Position).ToList();
                Console.WriteLine($"[ColumnSelectionManager] Sorted {sortedColumns.Count} columns by Position property");

                // Add columns to SelectedColumns in the correct order
                foreach (var column in sortedColumns)
                {
                    if (!string.IsNullOrWhiteSpace(column.ColumnName))
                    {
                        SelectedColumns.Add(column.ColumnName);
                        Console.WriteLine($"[ColumnSelectionManager] Added column '{column.ColumnName}' at position {column.Position} to SelectedColumns");

                        // Remove from AvailableColumns to prevent duplication
                        if (AvailableColumns.Contains(column.ColumnName))
                        {
                            AvailableColumns.Remove(column.ColumnName);
                            Console.WriteLine($"[ColumnSelectionManager] Removed column '{column.ColumnName}' from AvailableColumns to prevent duplication");
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[ColumnSelectionManager] Warning: Skipping column with empty name at position {column.Position}");
                    }
                }

                Console.WriteLine($"[ColumnSelectionManager] Column population completed successfully. Total selected columns: {SelectedColumns.Count}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ColumnSelectionManager] Error during column population: {ex.Message}");
                Console.WriteLine($"[ColumnSelectionManager] Stack trace: {ex.StackTrace}");
                throw;
            }
        }
    }
}