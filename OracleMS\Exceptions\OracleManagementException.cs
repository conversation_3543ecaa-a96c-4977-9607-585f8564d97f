namespace OracleMS.Exceptions;

/// <summary>
/// Oracle Management Studio 應用程式的基礎例外類別
/// </summary>
public class OracleManagementException : Exception
{
    public string ErrorCode { get; }
    public string Context { get; }

    public OracleManagementException(string message) : base(message)
    {
        ErrorCode = "OMS_GENERAL";
        Context = string.Empty;
    }

    public OracleManagementException(string message, Exception innerException) : base(message, innerException)
    {
        ErrorCode = "OMS_GENERAL";
        Context = string.Empty;
    }

    public OracleManagementException(string errorCode, string message, string context) : base(message)
    {
        ErrorCode = errorCode;
        Context = context;
    }

    public OracleManagementException(string errorCode, string message, string context, Exception innerException) 
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Context = context;
    }
}

/// <summary>
/// 資料庫連線相關例外
/// </summary>
public class DatabaseConnectionException : OracleManagementException
{
    public string ConnectionName { get; }

    public DatabaseConnectionException(string connectionName, string message) 
        : base("DB_CONNECTION", message, $"Connection: {connectionName}")
    {
        ConnectionName = connectionName;
    }

    public DatabaseConnectionException(string connectionName, string message, Exception innerException) 
        : base("DB_CONNECTION", message, $"Connection: {connectionName}", innerException)
    {
        ConnectionName = connectionName;
    }
}

/// <summary>
/// SQL 執行相關例外
/// </summary>
public class SqlExecutionException : OracleManagementException
{
    public string SqlStatement { get; }
    public int LineNumber { get; }

    public SqlExecutionException(string sqlStatement, string message) 
        : base("SQL_EXECUTION", message, $"SQL: {sqlStatement}")
    {
        SqlStatement = sqlStatement;
        LineNumber = 0;
    }

    public SqlExecutionException(string sqlStatement, string message, Exception innerException) 
        : base("SQL_EXECUTION", message, $"SQL: {sqlStatement}", innerException)
    {
        SqlStatement = sqlStatement;
        LineNumber = 0;
    }

    public SqlExecutionException(string sqlStatement, int lineNumber, string message) 
        : base("SQL_EXECUTION", message, $"SQL: {sqlStatement}, Line: {lineNumber}")
    {
        SqlStatement = sqlStatement;
        LineNumber = lineNumber;
    }
}

/// <summary>
/// 資料驗證相關例外
/// </summary>
public class DataValidationException : OracleManagementException
{
    public string FieldName { get; }
    public object? InvalidValue { get; }

    public DataValidationException(string fieldName, object? invalidValue, string message) 
        : base("DATA_VALIDATION", message, $"Field: {fieldName}")
    {
        FieldName = fieldName;
        InvalidValue = invalidValue;
    }
}

/// <summary>
/// 設定檔相關例外
/// </summary>
public class ConfigurationException : OracleManagementException
{
    public string ConfigurationKey { get; }

    public ConfigurationException(string configurationKey, string message) 
        : base("CONFIGURATION", message, $"Key: {configurationKey}")
    {
        ConfigurationKey = configurationKey;
    }

    public ConfigurationException(string configurationKey, string message, Exception innerException) 
        : base("CONFIGURATION", message, $"Key: {configurationKey}", innerException)
    {
        ConfigurationKey = configurationKey;
    }
}