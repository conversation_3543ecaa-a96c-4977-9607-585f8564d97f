# Oracle Management Studio

A WPF-based desktop application for managing Oracle databases, similar to SQL Server Management Studio.

## Project Structure

```
OracleMS/
├── Interfaces/          # Service interfaces
│   ├── IConnectionService.cs
│   ├── IDatabaseService.cs
│   ├── IScriptGeneratorService.cs
│   ├── IObjectEditorService.cs
│   └── IConfigurationService.cs
├── Models/              # Data models
│   ├── ConnectionInfo.cs
│   ├── DatabaseObject.cs
│   ├── QueryResult.cs
│   ├── ObjectDefinitions.cs
│   └── TableSchema.cs
├── Services/            # Business logic services
│   ├── ServiceCollectionExtensions.cs
│   ├── ObjectEditorService.cs
│   ├── TransactionManager.cs
│   └── GlobalExceptionHandler.cs
├── ViewModels/          # MVVM ViewModels
│   ├── ViewModelBase.cs
│   ├── MainWindowViewModel.cs
│   ├── BaseObjectEditorViewModel.cs
│   ├── TableEditorViewModel.cs
│   ├── ViewEditorViewModel.cs
│   ├── ProcedureEditorViewModel.cs
│   ├── FunctionEditorViewModel.cs
│   ├── PackageEditorViewModel.cs
│   ├── SequenceEditorViewModel.cs
│   ├── TriggerEditorViewModel.cs
│   └── IndexEditorViewModel.cs
├── Views/               # WPF Views
│   ├── MainWindow.xaml
│   ├── DbSession.xaml
│   ├── ObjectExplorerView.xaml
│   ├── QueryEditorView.xaml
│   ├── TableEditorView.xaml
│   ├── ViewEditorView.xaml
│   ├── ProcedureEditorView.xaml
│   ├── FunctionEditorView.xaml
│   ├── PackageEditorView.xaml
│   ├── SequenceEditorView.xaml
│   ├── TriggerEditorView.xaml
│   └── IndexEditorView.xaml
├── Factories/          # Factory classes
│   └── ObjectEditorFactory.cs
├── Exceptions/         # Exception handling
│   └── ObjectEditorErrorHandler.cs
├── Repositories/        # Data access layer
└── App.xaml.cs         # Application startup with DI container
```

## Technology Stack

- **Framework**: .NET 8 WPF
- **MVVM**: CommunityToolkit.Mvvm
- **Database**: Oracle.ManagedDataAccess.Core
- **DI Container**: Microsoft.Extensions.DependencyInjection
- **Logging**: Serilog
- **Configuration**: Microsoft.Extensions.Configuration

## Getting Started

1. Ensure you have .NET 8 SDK installed
2. Build the project: `dotnet build`
3. Run the application: `dotnet run`

## Architecture

The application follows the MVVM pattern with dependency injection:
- **Views**: WPF user interface components
- **ViewModels**: Presentation logic and data binding
- **Services**: Business logic and data operations
- **Models**: Data transfer objects and entities
- **Repositories**: Data access abstraction layer
- **Factories**: Object creation and initialization
- **Exceptions**: Error handling and reporting

## Features

### Query Editor
- SQL query execution with syntax highlighting
- Query results in tabular format
- Transaction management
- Query history

### Object Explorer
- Browse database objects by type
- Search functionality
- Context menu actions for database objects

### Database Object Editors
- Table Editor: Visual table structure editing
- View Editor: SQL definition editing with testing
- Procedure Editor: PL/SQL editing with compilation
- Function Editor: PL/SQL editing with testing
- Package Editor: Separate spec and body editing
- Sequence Editor: Visual sequence properties editing
- Trigger Editor: Event configuration and PL/SQL editing
- Index Editor: Index properties viewing and rebuilding

## Documentation

- [User Guide](DATABASE_OBJECT_EDITORS_GUIDE.md): Comprehensive guide for using the database object editors
- [API Documentation](API_DOCUMENTATION.md): Technical documentation for developers