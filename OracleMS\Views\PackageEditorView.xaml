<UserControl x:Class="OracleMS.Views.PackageEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:avalonEdit="http://icsharpcode.net/sharpdevelop/avalonedit"
             xmlns:converters="clr-namespace:OracleMS.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    
    <UserControl.Resources>
        <converters:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
    </UserControl.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 工具列 -->
        <ToolBar Grid.Row="0">
            <Button Command="{Binding SaveCommand}" ToolTip="儲存套件 (Ctrl+S)">
                <StackPanel Orientation="Horizontal">
                    <Image Source="/OracleMS;component/Resources/Images/save.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="儲存"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Command="{Binding CompileSpecCommand}" ToolTip="編譯套件規格">
                <StackPanel Orientation="Horizontal">
                    <Image Source="/OracleMS;component/Resources/Images/compile.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="編譯規格"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding CompileBodyCommand}" ToolTip="編譯套件主體">
                <StackPanel Orientation="Horizontal">
                    <Image Source="/OracleMS;component/Resources/Images/compile.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="編譯主體"/>
                </StackPanel>
            </Button>
            <Button Command="{Binding CompileAllCommand}" ToolTip="編譯套件規格和主體">
                <StackPanel Orientation="Horizontal">
                    <Image Source="/OracleMS;component/Resources/Images/compile_all.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="編譯全部"/>
                </StackPanel>
            </Button>
            <Separator/>
            <Button Command="{Binding RefreshCommand}" ToolTip="重新載入">
                <StackPanel Orientation="Horizontal">
                    <Image Source="/OracleMS;component/Resources/Images/refresh.png" Width="16" Height="16" Margin="0,0,5,0"/>
                    <TextBlock Text="重新載入"/>
                </StackPanel>
            </Button>
        </ToolBar>
        
        <!-- 主要內容區域 -->
        <TabControl Grid.Row="1" SelectedIndex="{Binding SelectedTabIndex}">
            <!-- 套件規格分頁 -->
            <TabItem Header="套件規格 (Specification)">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 規格編輯器 -->
                    <avalonEdit:TextEditor
                        Grid.Row="0"
                        Name="SpecificationEditor"
                        FontFamily="Consolas"
                        FontSize="12"
                        SyntaxHighlighting="SQL"
                        ShowLineNumbers="True"
                        WordWrap="False"
                        IsReadOnly="{Binding IsLoading}">
                    </avalonEdit:TextEditor>
                    
                    <!-- 規格狀態資訊 -->
                    <Border Grid.Row="1" Background="#FFEAEA" 
                            Visibility="{Binding HasSpecificationError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding SpecificationErrorMessage, Mode=OneWay}" Margin="5" TextWrapping="Wrap" Foreground="Red"/>
                    </Border>
                </Grid>
            </TabItem>
            
            <!-- 套件主體分頁 -->
            <TabItem Header="套件主體 (Body)">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 主體編輯器 -->
                    <avalonEdit:TextEditor
                        Grid.Row="0"
                        Name="BodyEditor"
                        FontFamily="Consolas"
                        FontSize="12"
                        SyntaxHighlighting="SQL"
                        ShowLineNumbers="True"
                        WordWrap="False"
                        IsReadOnly="{Binding IsLoading}">
                    </avalonEdit:TextEditor>
                    
                    <!-- 主體狀態資訊 -->
                    <Border Grid.Row="1" Background="#FFEAEA" 
                            Visibility="{Binding HasBodyError, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding BodyErrorMessage, Mode=OneWay}" Margin="5" TextWrapping="Wrap" Foreground="Red"/>
                    </Border>
                </Grid>
            </TabItem>
            
            <!-- 編譯結果分頁 -->
            <TabItem Header="編譯結果">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 編譯狀態摘要 -->
                    <Grid Grid.Row="0" Margin="5">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="規格狀態:" FontWeight="Bold" Margin="0,0,5,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding SpecificationStatus, Mode=OneWay}" Foreground="{Binding SpecificationStatusColor, Mode=OneWay}"/>
                        <TextBlock Grid.Row="0" Grid.Column="2" Text="最後編譯時間:" FontWeight="Bold" Margin="10,0,5,0"/>
                        <TextBlock Grid.Row="0" Grid.Column="3" Text="{Binding SpecificationLastCompiled, Mode=OneWay}"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="主體狀態:" FontWeight="Bold" Margin="0,5,5,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding BodyStatus, Mode=OneWay}" Foreground="{Binding BodyStatusColor, Mode=OneWay}"/>
                        <TextBlock Grid.Row="1" Grid.Column="2" Text="最後編譯時間:" FontWeight="Bold" Margin="10,5,5,0"/>
                        <TextBlock Grid.Row="1" Grid.Column="3" Text="{Binding BodyLastCompiled, Mode=OneWay}"/>
                    </Grid>
                    
                    <!-- 編譯結果詳細資訊 -->
                    <TextBox Grid.Row="1" Text="{Binding CompilationResults, Mode=OneWay}" IsReadOnly="True"
                             VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
                             FontFamily="Consolas" Margin="5"/>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- 進度指示器 -->
        <Grid Grid.Row="1" Background="#80000000" 
              Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="{Binding LoadingMessage}" Foreground="White" FontWeight="Bold" Margin="0,0,0,10"/>
                <ProgressBar IsIndeterminate="True" Width="200" Height="20"/>
            </StackPanel>
        </Grid>
        
        <!-- 分隔線 -->
        <Separator Grid.Row="2"/>
        
        <!-- 狀態列 -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <Run Text="規格狀態:"/>
                    <Run Text="{Binding SpecificationStatus, Mode=OneWay}" Foreground="{Binding SpecificationStatusColor, Mode=OneWay}"/>
                </TextBlock>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <Run Text="主體狀態:"/>
                    <Run Text="{Binding BodyStatus, Mode=OneWay}" Foreground="{Binding BodyStatusColor, Mode=OneWay}"/>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>