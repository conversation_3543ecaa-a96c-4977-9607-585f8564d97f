using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.Reflection;
using System;
using System.IO;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// IndexEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class IndexEditorView : UserControl
    {
        public IndexEditorView()
        {
            InitializeComponent();

            // 載入 SQL 語法高亮定義
            try
            {
                using (var stream = Assembly.GetExecutingAssembly().GetManifestResourceStream("OracleMS.Resources.SQL.xshd"))
                {
                    if (stream != null)
                    {
                        using (var reader = new XmlTextReader(stream))
                        {
                            DdlPreviewEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 如果無法載入語法高亮定義，則使用預設
                System.Diagnostics.Debug.WriteLine($"無法載入 SQL 語法高亮定義: {ex.Message}");
            }

            // 設定 DataContext 變更時的處理
            DataContextChanged += OnDataContextChanged;

            // 設定 ListBox 事件處理
            SetupListBoxEventHandlers();

            // 設定鍵盤快捷鍵
            SetupKeyboardShortcuts();

            // 設定載入和卸載事件
            Loaded += OnLoaded;
            Unloaded += OnUnloaded;
        }

        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 取消訂閱舊的 ViewModel
            if (e.OldValue is IndexEditorViewModel oldViewModel)
            {
                oldViewModel.PropertyChanged -= OnViewModelPropertyChanged;
            }

            // 訂閱新的 ViewModel
            if (e.NewValue is IndexEditorViewModel newViewModel)
            {
                newViewModel.PropertyChanged += OnViewModelPropertyChanged;

                // 初始設定 DDL 預覽文字
                if (DdlPreviewEditor != null)
                {
                    DdlPreviewEditor.Text = newViewModel.DdlPreview ?? string.Empty;
                }
            }
        }

        /// <summary>
        /// 設定 ListBox 事件處理
        /// </summary>
        private void SetupListBoxEventHandlers()
        {
            // 可用欄位 ListBox 選擇變更事件
            AvailableColumnsListBox.SelectionChanged += OnAvailableColumnsSelectionChanged;
            
            // 已選欄位 ListBox 選擇變更事件
            SelectedColumnsListBox.SelectionChanged += OnSelectedColumnsSelectionChanged;

            // 雙擊事件處理
            AvailableColumnsListBox.MouseDoubleClick += OnAvailableColumnsDoubleClick;
            SelectedColumnsListBox.MouseDoubleClick += OnSelectedColumnsDoubleClick;
        }

        /// <summary>
        /// 設定鍵盤快捷鍵
        /// </summary>
        private void SetupKeyboardShortcuts()
        {
            // 為整個 UserControl 設定鍵盤事件
            this.KeyDown += OnKeyDown;
            
            // 確保 UserControl 可以接收鍵盤焦點
            this.Focusable = true;
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(IndexEditorViewModel.DdlPreview))
            {
                if (sender is IndexEditorViewModel viewModel && DdlPreviewEditor != null)
                {
                    DdlPreviewEditor.Text = viewModel.DdlPreview ?? string.Empty;
                }
            }
        }

        /// <summary>
        /// 可用欄位 ListBox 選擇變更事件處理
        /// </summary>
        private void OnAvailableColumnsSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 當選擇變更時，可以在這裡執行額外的邏輯
            // 例如：更新按鈕狀態、顯示欄位詳細資訊等
            if (DataContext is IndexEditorViewModel viewModel)
            {
                // 可以在這裡添加選擇變更時的邏輯
                // 例如：預覽選定欄位的詳細資訊
            }
        }

        /// <summary>
        /// 已選欄位 ListBox 選擇變更事件處理
        /// </summary>
        private void OnSelectedColumnsSelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 當選擇變更時，可以在這裡執行額外的邏輯
            // 例如：更新按鈕狀態、顯示欄位順序等
            if (DataContext is IndexEditorViewModel viewModel)
            {
                // 可以在這裡添加選擇變更時的邏輯
                // 例如：更新 DDL 預覽
            }
        }

        /// <summary>
        /// 可用欄位 ListBox 雙擊事件處理
        /// </summary>
        private void OnAvailableColumnsDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is IndexEditorViewModel viewModel && 
                AvailableColumnsListBox.SelectedItem is string selectedColumn)
            {
                // 雙擊時自動將欄位加入到索引欄位
                if (viewModel.AddColumnCommand.CanExecute(selectedColumn))
                {
                    viewModel.AddColumnCommand.Execute(selectedColumn);
                }
            }
        }

        /// <summary>
        /// 已選欄位 ListBox 雙擊事件處理
        /// </summary>
        private void OnSelectedColumnsDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (DataContext is IndexEditorViewModel viewModel && 
                SelectedColumnsListBox.SelectedItem is string selectedColumn)
            {
                // 雙擊時自動將欄位從索引欄位移除
                if (viewModel.RemoveColumnCommand.CanExecute(selectedColumn))
                {
                    viewModel.RemoveColumnCommand.Execute(selectedColumn);
                }
            }
        }

        /// <summary>
        /// 鍵盤快捷鍵事件處理
        /// </summary>
        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            if (DataContext is not IndexEditorViewModel viewModel)
                return;

            // 處理各種鍵盤快捷鍵
            switch (e.Key)
            {
                case Key.Enter:
                    HandleEnterKey(viewModel, e);
                    break;

                case Key.Delete:
                    HandleDeleteKey(viewModel, e);
                    break;

                case Key.Right:
                    if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                    {
                        HandleMoveToSelected(viewModel, e);
                    }
                    break;

                case Key.Left:
                    if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                    {
                        HandleMoveToAvailable(viewModel, e);
                    }
                    break;

                case Key.Up:
                    if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                    {
                        HandleMoveUp(viewModel, e);
                    }
                    break;

                case Key.Down:
                    if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                    {
                        HandleMoveDown(viewModel, e);
                    }
                    break;

                case Key.S:
                    if (e.KeyboardDevice.Modifiers == ModifierKeys.Control)
                    {
                        HandleSave(viewModel, e);
                    }
                    break;

                case Key.Escape:
                    HandleEscape(e);
                    break;
            }
        }

        /// <summary>
        /// 處理 Enter 鍵
        /// </summary>
        private void HandleEnterKey(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            // 如果焦點在可用欄位 ListBox 上，將選定欄位加入索引
            if (AvailableColumnsListBox.IsKeyboardFocusWithin && 
                AvailableColumnsListBox.SelectedItem is string availableColumn)
            {
                if (viewModel.AddColumnCommand.CanExecute(availableColumn))
                {
                    viewModel.AddColumnCommand.Execute(availableColumn);
                    e.Handled = true;
                }
            }
            // 如果焦點在已選欄位 ListBox 上，移除選定欄位
            else if (SelectedColumnsListBox.IsKeyboardFocusWithin && 
                     SelectedColumnsListBox.SelectedItem is string selectedColumn)
            {
                if (viewModel.RemoveColumnCommand.CanExecute(selectedColumn))
                {
                    viewModel.RemoveColumnCommand.Execute(selectedColumn);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 處理 Delete 鍵
        /// </summary>
        private void HandleDeleteKey(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            // Delete 鍵只在已選欄位 ListBox 上有效，用於移除欄位
            if (SelectedColumnsListBox.IsKeyboardFocusWithin && 
                SelectedColumnsListBox.SelectedItem is string selectedColumn)
            {
                if (viewModel.RemoveColumnCommand.CanExecute(selectedColumn))
                {
                    viewModel.RemoveColumnCommand.Execute(selectedColumn);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 處理 Ctrl+Right 鍵（移動到已選欄位）
        /// </summary>
        private void HandleMoveToSelected(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            if (AvailableColumnsListBox.SelectedItem is string selectedColumn)
            {
                if (viewModel.AddColumnCommand.CanExecute(selectedColumn))
                {
                    viewModel.AddColumnCommand.Execute(selectedColumn);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 處理 Ctrl+Left 鍵（移動到可用欄位）
        /// </summary>
        private void HandleMoveToAvailable(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            if (SelectedColumnsListBox.SelectedItem is string selectedColumn)
            {
                if (viewModel.RemoveColumnCommand.CanExecute(selectedColumn))
                {
                    viewModel.RemoveColumnCommand.Execute(selectedColumn);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 處理 Ctrl+Up 鍵（上移欄位）
        /// </summary>
        private void HandleMoveUp(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            if (SelectedColumnsListBox.SelectedItem is string selectedColumn)
            {
                if (viewModel.MoveColumnUpCommand.CanExecute(selectedColumn))
                {
                    viewModel.MoveColumnUpCommand.Execute(selectedColumn);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 處理 Ctrl+Down 鍵（下移欄位）
        /// </summary>
        private void HandleMoveDown(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            if (SelectedColumnsListBox.SelectedItem is string selectedColumn)
            {
                if (viewModel.MoveColumnDownCommand.CanExecute(selectedColumn))
                {
                    viewModel.MoveColumnDownCommand.Execute(selectedColumn);
                    e.Handled = true;
                }
            }
        }

        /// <summary>
        /// 處理 Ctrl+S 鍵（儲存）
        /// </summary>
        private void HandleSave(IndexEditorViewModel viewModel, KeyEventArgs e)
        {
            if (viewModel.IsCreateMode && viewModel.CreateIndexCommand.CanExecute(null))
            {
                viewModel.CreateIndexCommand.Execute(null);
                e.Handled = true;
            }
            else if (viewModel.IsEditMode && viewModel.UpdateIndexCommand.CanExecute(null))
            {
                viewModel.UpdateIndexCommand.Execute(null);
                e.Handled = true;
            }
        }

        /// <summary>
        /// 處理 Escape 鍵（取消）
        /// </summary>
        private void HandleEscape(KeyEventArgs e)
        {
            CancelButton_Click(this, new RoutedEventArgs());
            e.Handled = true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 關閉當前的 TabItem
            if (Parent is TabItem tabItem && tabItem.Parent is TabControl tabControl)
            {
                // 尋找包含此 TabControl 的 DbSession
                var dbSession = FindParentDbSession(tabControl);
                if (dbSession != null)
                {
                    // 使用 DbSession 的 ReturnToOpenerTab 方法
                    tabControl.Items.Remove(tabItem);
                    dbSession.ReturnToOpenerTabPublic(tabItem);
                }
                else
                {
                    // 如果找不到 DbSession，使用原來的邏輯
                    tabControl.Items.Remove(tabItem);
                }
            }
        }

        /// <summary>
        /// 尋找父級的 DbSession
        /// </summary>
        private DbSession? FindParentDbSession(DependencyObject child)
        {
            var parent = VisualTreeHelper.GetParent(child);
            while (parent != null)
            {
                if (parent is DbSession dbSession)
                    return dbSession;
                parent = VisualTreeHelper.GetParent(parent);
            }
            return null;
        }

        /// <summary>
        /// 設定焦點到適當的控制項
        /// </summary>
        public void SetInitialFocus()
        {
            if (DataContext is IndexEditorViewModel viewModel)
            {
                if (viewModel.IsCreateMode)
                {
                    // 創建模式時，焦點設定到 Schema ComboBox
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        var schemaComboBox = FindName("SchemaComboBox") as ComboBox;
                        schemaComboBox?.Focus();
                    }));
                }
                else
                {
                    // 編輯模式時，焦點設定到可用欄位 ListBox
                    Dispatcher.BeginInvoke(new Action(() =>
                    {
                        AvailableColumnsListBox.Focus();
                    }));
                }
            }
        }

        /// <summary>
        /// 清理資源
        /// </summary>
        private void OnUnloaded(object sender, RoutedEventArgs e)
        {
            // 取消事件訂閱
            if (DataContext is IndexEditorViewModel viewModel)
            {
                viewModel.PropertyChanged -= OnViewModelPropertyChanged;
            }

            // 清理 ListBox 事件
            AvailableColumnsListBox.SelectionChanged -= OnAvailableColumnsSelectionChanged;
            SelectedColumnsListBox.SelectionChanged -= OnSelectedColumnsSelectionChanged;
            AvailableColumnsListBox.MouseDoubleClick -= OnAvailableColumnsDoubleClick;
            SelectedColumnsListBox.MouseDoubleClick -= OnSelectedColumnsDoubleClick;

            // 清理鍵盤事件
            this.KeyDown -= OnKeyDown;
        }

        /// <summary>
        /// 當控制項載入完成時設定初始焦點
        /// </summary>
        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            SetInitialFocus();
        }
    }
}