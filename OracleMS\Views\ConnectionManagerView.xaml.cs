using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using OracleMS.Models;
using OracleMS.ViewModels;

namespace OracleMS.Views;

public partial class ConnectionManagerView : UserControl
{
    public ConnectionManagerView()
    {
        InitializeComponent();
    }

    private void OnPasswordChanged(object sender, RoutedEventArgs e)
    {
        if (sender is PasswordBox passwordBox && DataContext is ConnectionManagerViewModel viewModel)
        {
            viewModel.EditingConnection.Password = passwordBox.Password;
        }
    }

    private void OnConnectionDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (sender is ListBoxItem item && item.DataContext is ConnectionInfo connection)
        {
            if (DataContext is ConnectionManagerViewModel viewModel)
            {
                viewModel.SelectedConnection = connection;
                if (viewModel.ConnectCommand.CanExecute(null))
                {
                    viewModel.ConnectCommand.Execute(null);
                }
            }
        }
    }
}