﻿using System;
using System.Linq;
using OracleMS.Models;

namespace Task12Test
{
    /// <summary>
    /// Task 12 實作驗證程式
    /// 測試新增的驗證邏輯和錯誤處理功能
    /// </summary>
    public class Program
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== Task 12: 新增驗證邏輯和錯誤處理 測試 ===");
            Console.WriteLine();

            // 測試 1: 基本驗證功能
            TestBasicValidation();
            
            // 測試 2: 增強的錯誤分類
            TestEnhancedErrorCategorization();
            
            // 測試 3: 驗證警告功能
            TestValidationWarnings();
            
            // 測試 4: 不同驗證模式
            TestValidationModes();
            
            // 測試 5: 本地化訊息支援
            TestLocalizedMessages();
            
            // 測試 6: ValidationResult 增強功能
            TestValidationResultEnhancements();

            Console.WriteLine();
            Console.WriteLine("=== 所有測試完成 ===");
            Console.WriteLine("按任意鍵結束...");
            Console.ReadKey();
        }

        private static void TestBasicValidation()
        {
            Console.WriteLine("1. 測試基本驗證功能");
            Console.WriteLine("-------------------");

            var indexDefinition = new IndexDefinition
            {
                Name = "",
                Owner = "",
                TableName = "",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>()
            };

            var result = indexDefinition.Validate();
            
            Console.WriteLine($"驗證結果: {(result.IsValid ? "有效" : "無效")}");
            Console.WriteLine($"錯誤數量: {result.ErrorCount}");
            Console.WriteLine($"警告數量: {result.WarningCount}");
            
            if (!result.IsValid)
            {
                Console.WriteLine("錯誤詳情:");
                foreach (var error in result.DetailedErrors)
                {
                    Console.WriteLine($"  - {error.Message} (類型: {error.ErrorType}, 欄位: {error.FieldName})");
                }
            }
            
            Console.WriteLine();
        }

        private static void TestEnhancedErrorCategorization()
        {
            Console.WriteLine("2. 測試增強的錯誤分類");
            Console.WriteLine("-------------------");

            var indexDefinition = new IndexDefinition
            {
                Name = "123INVALID", // 格式錯誤
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COL1", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "COL1", Position = 2 } // 重複錯誤
                }
            };

            var result = indexDefinition.Validate();
            
            Console.WriteLine($"驗證結果: {(result.IsValid ? "有效" : "無效")}");
            Console.WriteLine("錯誤分類統計:");
            
            var errorsByType = result.DetailedErrors.GroupBy(e => e.ErrorType);
            foreach (var group in errorsByType)
            {
                Console.WriteLine($"  {group.Key}: {group.Count()} 個錯誤");
                foreach (var error in group)
                {
                    Console.WriteLine($"    - {error.Message}");
                }
            }
            
            Console.WriteLine();
        }

        private static void TestValidationWarnings()
        {
            Console.WriteLine("3. 測試驗證警告功能");
            Console.WriteLine("-------------------");

            var indexDefinition = new IndexDefinition
            {
                Name = "BADNAME", // 沒有 IDX 前綴，會產生警告
                Owner = "TEST_SCHEMA",
                TableName = "USERS",
                IsUnique = true,
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>()
            };

            // 新增多個欄位以觸發效能警告
            for (int i = 1; i <= 6; i++)
            {
                indexDefinition.Columns.Add(new IndexColumnDefinition 
                { 
                    ColumnName = $"COLUMN{i}", 
                    Position = i 
                });
            }

            var result = indexDefinition.Validate(ValidationMode.Create);
            
            Console.WriteLine($"驗證結果: {(result.IsValid ? "有效" : "無效")}");
            Console.WriteLine($"警告數量: {result.WarningCount}");
            
            if (result.HasWarnings)
            {
                Console.WriteLine("警告詳情:");
                foreach (var warning in result.Warnings)
                {
                    Console.WriteLine($"  - {warning.Message} (類型: {warning.WarningType})");
                }
            }
            
            Console.WriteLine();
        }

        private static void TestValidationModes()
        {
            Console.WriteLine("4. 測試不同驗證模式");
            Console.WriteLine("-------------------");

            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                AvailableColumns = new System.Collections.Generic.List<string> { "COL1", "COL2" },
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "INVALID_COL", Position = 1 }
                }
            };

            // 測試標準模式
            var standardResult = indexDefinition.Validate(ValidationMode.Standard);
            Console.WriteLine($"標準模式 - 錯誤: {standardResult.ErrorCount}, 警告: {standardResult.WarningCount}");

            // 測試創建模式
            var createResult = indexDefinition.Validate(ValidationMode.Create);
            Console.WriteLine($"創建模式 - 錯誤: {createResult.ErrorCount}, 警告: {createResult.WarningCount}");

            // 測試編輯模式
            var editResult = indexDefinition.Validate(ValidationMode.Edit);
            Console.WriteLine($"編輯模式 - 錯誤: {editResult.ErrorCount}, 警告: {editResult.WarningCount}");
            
            Console.WriteLine();
        }

        private static void TestLocalizedMessages()
        {
            Console.WriteLine("5. 測試本地化訊息支援");
            Console.WriteLine("-------------------");

            // 測試格式化訊息
            var formattedMessage = IndexValidationMessages.GetFormattedMessage(
                "DuplicateColumn", 
                "欄位 '{0}' 重複", 
                "TEST_COLUMN");
            
            Console.WriteLine($"格式化訊息: {formattedMessage}");
            
            // 測試各種訊息類型
            Console.WriteLine($"索引名稱必填: {IndexValidationMessages.IndexNameRequired}");
            Console.WriteLine($"資料庫連線失敗: {IndexValidationMessages.DatabaseConnectionFailed}");
            Console.WriteLine($"驗證進行中: {IndexValidationMessages.ValidationInProgress}");
            Console.WriteLine($"選擇欄位提示: {IndexValidationMessages.SelectColumns}");
            
            Console.WriteLine();
        }

        private static void TestValidationResultEnhancements()
        {
            Console.WriteLine("6. 測試 ValidationResult 增強功能");
            Console.WriteLine("-----------------------------------");

            var result = new ValidationResult();
            
            // 新增不同類型的錯誤和警告
            result.AddError("索引名稱不能為空", ValidationErrorType.Required, "Name");
            result.AddError("欄位格式錯誤", ValidationErrorType.Format, "ColumnName");
            result.AddWarning("建議使用更短的索引名稱", ValidationWarningType.BestPractice);
            result.AddWarning("可能影響查詢效能", ValidationWarningType.Performance);

            Console.WriteLine($"是否有效: {result.IsValid}");
            Console.WriteLine($"是否有警告: {result.HasWarnings}");
            Console.WriteLine($"錯誤數量: {result.ErrorCount}");
            Console.WriteLine($"警告數量: {result.WarningCount}");
            
            Console.WriteLine("\n錯誤摘要:");
            Console.WriteLine(result.GetErrorSummary());
            
            Console.WriteLine("\n詳細錯誤資訊:");
            foreach (var error in result.DetailedErrors)
            {
                Console.WriteLine($"  時間: {error.Timestamp:HH:mm:ss}");
                Console.WriteLine($"  訊息: {error.Message}");
                Console.WriteLine($"  類型: {error.ErrorType}");
                Console.WriteLine($"  欄位: {error.FieldName}");
                Console.WriteLine();
            }
            
            Console.WriteLine();
        }
    }
}
