using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 檢視表編輯器 ViewModel
    /// </summary>
    public class ViewEditorViewModel : BaseObjectEditorViewModel
    {
        private string _viewDefinition = string.Empty;
        private DataTable _testResults;
        private ObservableCollection<ColumnDefinition> _columnInfo = new();
        private ObservableCollection<DependencyInfo> _dependencies = new();
        private bool _hasTestResults;
        private int _rowCount;
        private TimeSpan _lastExecutionTime;

        /// <summary>
        /// 檢視表 SQL 定義
        /// </summary>
        public string ViewDefinition
        {
            get => _viewDefinition;
            set
            {
                if (SetProperty(ref _viewDefinition, value))
                {
                    if (!_isInitializing)
                    {
                        HasUnsavedChanges = true;
                    }
                }
            }
        }

        /// <summary>
        /// 測試結果資料表
        /// </summary>
        public DataTable TestResults
        {
            get => _testResults;
            private set => SetProperty(ref _testResults, value);
        }

        /// <summary>
        /// 欄位資訊集合
        /// </summary>
        public ObservableCollection<ColumnDefinition> ColumnInfo
        {
            get => _columnInfo;
            private set => SetProperty(ref _columnInfo, value);
        }

        /// <summary>
        /// 相依性資訊集合
        /// </summary>
        public ObservableCollection<DependencyInfo> Dependencies
        {
            get => _dependencies;
            private set => SetProperty(ref _dependencies, value);
        }

        /// <summary>
        /// 是否有測試結果
        /// </summary>
        public bool HasTestResults
        {
            get => _hasTestResults;
            private set => SetProperty(ref _hasTestResults, value);
        }

        /// <summary>
        /// 資料列數量
        /// </summary>
        public int RowCount
        {
            get => _rowCount;
            private set => SetProperty(ref _rowCount, value);
        }

        /// <summary>
        /// 最後執行時間
        /// </summary>
        public TimeSpan LastExecutionTime
        {
            get => _lastExecutionTime;
            private set => SetProperty(ref _lastExecutionTime, value);
        }

        /// <summary>
        /// 執行測試命令
        /// </summary>
        public ICommand ExecuteTestCommand { get; }

        /// <summary>
        /// 驗證 SQL 命令
        /// </summary>
        public ICommand ValidateSqlCommand { get; }

        /// <summary>
        /// 匯出結果命令
        /// </summary>
        public ICommand ExportResultsCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="viewName">檢視表名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public ViewEditorViewModel(
            string viewName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(viewName, DatabaseObjectType.View, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            // 初始化資料表
            _testResults = new DataTable();

            // 初始化命令
            ExecuteTestCommand = new AsyncRelayCommand(OnExecuteTestAsync, CanExecuteTest);
            ValidateSqlCommand = new AsyncRelayCommand(OnValidateSqlAsync, CanValidateSql);
            ExportResultsCommand = new AsyncRelayCommand(OnExportResultsAsync, CanExportResults);
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 載入檢視表定義
            ViewDefinition = await _objectEditorService.GetViewDefinitionAsync(connection, ObjectName);

            // 載入欄位資訊
            await LoadColumnInfoAsync(connection);

            // 載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            await _objectEditorService.SaveViewDefinitionAsync(connection, ObjectName, ViewDefinition);

            // 重新載入欄位資訊
            await LoadColumnInfoAsync(connection);

            // 重新載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected override string GenerateScript()
        {
            return ViewDefinition;
        }

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(ViewDefinition))
            {
                result.AddError("檢視表定義不能為空");
            }
            else if (!ViewDefinition.Trim().ToUpper().StartsWith("CREATE OR REPLACE VIEW") &&
                     !ViewDefinition.Trim().ToUpper().StartsWith("CREATE VIEW"))
            {
                result.AddError("檢視表定義必須以 CREATE [OR REPLACE] VIEW 開頭");
            }

            return result;
        }

        /// <summary>
        /// 載入欄位資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadColumnInfoAsync(IDbConnection connection)
        {
            try
            {
                // 在實際應用中，這裡會從資料庫中查詢檢視表的欄位資訊
                // 暫時使用簡單的實作
                var sql = $@"
                    SELECT 
                        COLUMN_NAME AS Name,
                        DATA_TYPE AS DataType,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        NULLABLE AS IsNullable,
                        COMMENTS AS Comments
                    FROM 
                        ALL_TAB_COLUMNS c
                    LEFT JOIN 
                        ALL_COL_COMMENTS cc ON c.OWNER = cc.OWNER AND c.TABLE_NAME = cc.TABLE_NAME AND c.COLUMN_NAME = cc.COLUMN_NAME
                    WHERE 
                        c.TABLE_NAME = '{ObjectName}'
                    ORDER BY 
                        c.COLUMN_ID";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                ColumnInfo.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        ColumnInfo.Add(new ColumnDefinition
                        {
                            Name = row["Name"].ToString(),
                            DataType = row["DataType"].ToString(),
                            Length = row["Length"] != DBNull.Value ? Convert.ToInt32(row["Length"]) : null,
                            Precision = row["Precision"] != DBNull.Value ? Convert.ToInt32(row["Precision"]) : null,
                            Scale = row["Scale"] != DBNull.Value ? Convert.ToInt32(row["Scale"]) : null,
                            IsNullable = row["IsNullable"].ToString() == "Y",
                            Comments = row["Comments"]?.ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入欄位資訊失敗：{ex}");
            }
        }

        /// <summary>
        /// 載入相依性資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadDependenciesAsync(IDbConnection connection)
        {
            try
            {
                // 在實際應用中，這裡會從資料庫中查詢檢視表的相依性資訊
                // 暫時使用簡單的實作
                var sql = $@"
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                        d.REFERENCED_TYPE AS Type,
                        d.REFERENCED_OWNER AS Owner,
                        'DEPENDS ON' AS DependencyType,
                        'This view depends on this object' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.NAME = '{ObjectName}'
                        AND d.TYPE = 'VIEW'
                    UNION ALL
                    SELECT 
                        d.NAME AS Name,
                        d.TYPE AS Type,
                        d.OWNER AS Owner,
                        'DEPENDENT' AS DependencyType,
                        'This object depends on this view' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.REFERENCED_NAME = '{ObjectName}'
                        AND d.REFERENCED_TYPE = 'VIEW'
                    ORDER BY 
                        DependencyType, Name";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                Dependencies.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        Dependencies.Add(new DependencyInfo
                        {
                            Name = row["Name"].ToString(),
                            Type = row["Type"].ToString(),
                            Owner = row["Owner"].ToString(),
                            DependencyType = row["DependencyType"].ToString(),
                            Description = row["Description"].ToString()
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入相依性資訊失敗：{ex}");
            }
        }

        /// <summary>
        /// 執行測試查詢
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnExecuteTestAsync()
        {
            if (string.IsNullOrWhiteSpace(ViewDefinition) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                StatusMessage = "正在執行測試查詢...";
                HasError = false;
                ErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 建立測試查詢
                var sql = $"SELECT * FROM {ObjectName}";

                // 執行查詢
                var stopwatch = Stopwatch.StartNew();
                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                stopwatch.Stop();

                if (result.IsSuccess && result.Data != null)
                {
                    TestResults = result.Data;
                    RowCount = result.Data.Rows.Count;
                    HasTestResults = true;
                    LastExecutionTime = stopwatch.Elapsed;
                    StatusMessage = $"測試查詢成功，返回 {RowCount} 筆資料，耗時 {LastExecutionTime.TotalSeconds:F2} 秒";
                }
                else
                {
                    HasError = true;
                    ErrorMessage = result.ErrorMessage;
                    StatusMessage = $"測試查詢失敗：{result.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"測試查詢失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以執行測試查詢
        /// </summary>
        /// <returns>是否可以執行</returns>
        private bool CanExecuteTest()
        {
            return !IsLoading && !IsSaving && _getConnection() != null;
        }

        /// <summary>
        /// 驗證 SQL 語法
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnValidateSqlAsync()
        {
            if (string.IsNullOrWhiteSpace(ViewDefinition) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                StatusMessage = "正在驗證 SQL 語法...";
                HasError = false;
                ErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 建立驗證查詢
                // 使用 EXPLAIN PLAN 來驗證 SQL 語法
                var viewSql = ViewDefinition.Trim();
                var selectPart = ExtractSelectPartFromView(viewSql);
                if (string.IsNullOrWhiteSpace(selectPart))
                {
                    throw new InvalidOperationException("無法從檢視表定義中提取 SELECT 語句");
                }

                var validateSql = $"EXPLAIN PLAN FOR {selectPart}";

                // 執行驗證
                var result = await _databaseService.ExecuteNonQueryAsync(connection, validateSql);

                StatusMessage = "SQL 語法驗證通過";
            }
            catch (Exception ex)
            {
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"SQL 語法驗證失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以驗證 SQL 語法
        /// </summary>
        /// <returns>是否可以驗證</returns>
        private bool CanValidateSql()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(ViewDefinition) && _getConnection() != null;
        }

        /// <summary>
        /// 匯出測試結果
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnExportResultsAsync()
        {
            if (!HasTestResults || TestResults == null)
                return;

            try
            {
                StatusMessage = "正在匯出測試結果...";

                // 在實際應用中，這裡會開啟一個儲存檔案對話框
                // 暫時使用簡單的實作
                var dialog = new Microsoft.Win32.SaveFileDialog
                {
                    FileName = $"{ObjectName}_Results",
                    DefaultExt = ".csv",
                    Filter = "CSV 檔案 (*.csv)|*.csv|所有檔案 (*.*)|*.*"
                };

                if (dialog.ShowDialog() == true)
                {
                    await ExportToCsvAsync(TestResults, dialog.FileName);
                    StatusMessage = $"測試結果已匯出至 {dialog.FileName}";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"匯出測試結果失敗：{ex.Message}";
            }
        }

        /// <summary>
        /// 是否可以匯出測試結果
        /// </summary>
        /// <returns>是否可以匯出</returns>
        private bool CanExportResults()
        {
            return HasTestResults && TestResults != null && TestResults.Rows.Count > 0;
        }

        /// <summary>
        /// 將資料表匯出為 CSV 檔案
        /// </summary>
        /// <param name="dataTable">資料表</param>
        /// <param name="filePath">檔案路徑</param>
        /// <returns>非同步工作</returns>
        private async Task ExportToCsvAsync(DataTable dataTable, string filePath)
        {
            using var writer = new System.IO.StreamWriter(filePath, false, System.Text.Encoding.UTF8);
            
            // 寫入標題
            var headers = new List<string>();
            foreach (DataColumn column in dataTable.Columns)
            {
                headers.Add(EscapeCsvField(column.ColumnName));
            }
            await writer.WriteLineAsync(string.Join(",", headers));

            // 寫入資料
            foreach (DataRow row in dataTable.Rows)
            {
                var values = new List<string>();
                foreach (var item in row.ItemArray)
                {
                    values.Add(EscapeCsvField(item?.ToString() ?? string.Empty));
                }
                await writer.WriteLineAsync(string.Join(",", values));
            }
        }

        /// <summary>
        /// 轉義 CSV 欄位
        /// </summary>
        /// <param name="field">欄位值</param>
        /// <returns>轉義後的欄位值</returns>
        private string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "\"\"";

            // 如果欄位包含逗號、引號或換行符，則需要用引號包圍並將引號轉義
            if (field.Contains("\"") || field.Contains(",") || field.Contains("\n") || field.Contains("\r"))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }

            return field;
        }

        /// <summary>
        /// 從檢視表定義中提取 SELECT 語句部分
        /// </summary>
        /// <param name="viewDefinition">檢視表定義</param>
        /// <returns>SELECT 語句</returns>
        private string ExtractSelectPartFromView(string viewDefinition)
        {
            if (string.IsNullOrWhiteSpace(viewDefinition))
                return string.Empty;

            var upperDefinition = viewDefinition.ToUpper();
            var asIndex = upperDefinition.IndexOf(" AS ");
            if (asIndex < 0)
                return string.Empty;

            return viewDefinition.Substring(asIndex + 4).Trim();
        }
    }


}