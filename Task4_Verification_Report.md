# Task 4 驗證報告：實作欄位選擇相關命令

## 任務概述
在IndexEditorViewModel中新增AddColumnCommand、RemoveColumnCommand、MoveColumnUpCommand、MoveColumnDownCommand四個命令，實作命令的執行邏輯，包含CanExecute條件檢查，整合ColumnSelectionManager來處理欄位移動操作，並創建單元測試驗證所有命令的執行邏輯。

## 實作內容

### 1. 新增的命令屬性
在IndexEditorViewModel中新增了以下四個命令屬性：

```csharp
/// <summary>
/// 新增欄位命令（將欄位從左側移到右側）
/// </summary>
public ICommand AddColumnCommand { get; }

/// <summary>
/// 移除欄位命令（將欄位從右側移回左側）
/// </summary>
public ICommand RemoveColumnCommand { get; }

/// <summary>
/// 欄位上移命令
/// </summary>
public ICommand MoveColumnUpCommand { get; }

/// <summary>
/// 欄位下移命令
/// </summary>
public ICommand MoveColumnDownCommand { get; }
```

### 2. 命令初始化
在建構函式中初始化所有命令：

```csharp
// 初始化欄位選擇命令
AddColumnCommand = new RelayCommand<string>(AddColumn, CanAddColumn);
RemoveColumnCommand = new RelayCommand<string>(RemoveColumn, CanRemoveColumn);
MoveColumnUpCommand = new RelayCommand<string>(MoveColumnUp, CanMoveColumnUp);
MoveColumnDownCommand = new RelayCommand<string>(MoveColumnDown, CanMoveColumnDown);
```

### 3. 命令執行方法實作

#### AddColumn 方法
- 將欄位從可用欄位移動到已選欄位
- 更新IndexDefinition的Columns
- 更新DDL預覽

#### RemoveColumn 方法
- 將欄位從已選欄位移回可用欄位
- 更新IndexDefinition的Columns
- 更新DDL預覽

#### MoveColumnUp 方法
- 在已選欄位中將指定欄位上移
- 更新IndexDefinition的Columns順序
- 更新DDL預覽

#### MoveColumnDown 方法
- 在已選欄位中將指定欄位下移
- 更新IndexDefinition的Columns順序
- 更新DDL預覽

### 4. CanExecute 條件檢查

#### CanAddColumn
- 檢查欄位名稱不為空
- 檢查欄位存在於可用欄位中
- 檢查不在操作進行中且不在載入中

#### CanRemoveColumn
- 檢查欄位名稱不為空
- 檢查欄位存在於已選欄位中
- 檢查不在操作進行中且不在載入中

#### CanMoveColumnUp
- 檢查欄位名稱不為空
- 檢查欄位不是第一個（index > 0）
- 檢查不在操作進行中且不在載入中

#### CanMoveColumnDown
- 檢查欄位名稱不為空
- 檢查欄位不是最後一個（index < count - 1）
- 檢查不在操作進行中且不在載入中

### 5. ColumnSelectionManager 整合
所有命令都透過ColumnSelectionManager來處理實際的欄位移動操作：
- `_columnSelectionManager.MoveToSelected(columnName)`
- `_columnSelectionManager.MoveToAvailable(columnName)`
- `_columnSelectionManager.MoveUp(columnName)`
- `_columnSelectionManager.MoveDown(columnName)`

### 6. IndexDefinition 更新
新增了`UpdateIndexDefinitionColumns()`方法，在每次欄位變更後自動更新IndexDefinition的Columns屬性，確保資料一致性。

## 測試驗證

### 單元測試
創建了完整的單元測試檔案 `IndexEditorViewModelCommandTests.cs`，包含以下測試案例：

1. **AddColumnCommand 測試**
   - 有效欄位新增測試
   - Null欄位處理測試
   - 空字串欄位處理測試

2. **RemoveColumnCommand 測試**
   - 有效欄位移除測試
   - Null欄位處理測試

3. **MoveColumnUpCommand 測試**
   - 有效欄位上移測試
   - 第一個欄位上移測試（應該不移動）

4. **MoveColumnDownCommand 測試**
   - 有效欄位下移測試
   - 最後一個欄位下移測試（應該不移動）

5. **CanExecute 條件測試**
   - 各種條件下的CanExecute測試
   - 邊界條件測試

6. **IndexDefinition 更新測試**
   - 驗證命令執行後IndexDefinition.Columns的正確更新

### 整合測試
創建了獨立的測試程式 `Task4Test`，驗證所有功能的整合運作：

```
=== Task 4: 實作欄位選擇相關命令 - 驗證測試 ===
✓ IndexEditorViewModel 初始化成功

--- 測試 AddColumnCommand ---
初始狀態 - 可用欄位: 3, 已選欄位: 0
新增 COLUMN1 後 - 可用欄位: 2, 已選欄位: 1
COLUMN1 在已選欄位中: True
COLUMN1 在可用欄位中: False
✓ AddColumnCommand 測試通過

--- 測試 RemoveColumnCommand ---
移除 COLUMN1 後 - 可用欄位: 3, 已選欄位: 0
COLUMN1 在已選欄位中: False
COLUMN1 在可用欄位中: True
✓ RemoveColumnCommand 測試通過

--- 測試 MoveColumnUpCommand 和 MoveColumnDownCommand ---
新增三個欄位後順序: COLUMN1, COLUMN2, COLUMN3
COLUMN2 上移後順序: COLUMN2, COLUMN1, COLUMN3
✓ MoveColumnUpCommand 測試通過
COLUMN2 下移後順序: COLUMN1, COLUMN2, COLUMN3
✓ MoveColumnDownCommand 測試通過

--- 測試 IndexDefinition 更新 ---
IndexDefinition.Columns 數量: 3
IndexDefinition.Columns 內容: COLUMN1, COLUMN2, COLUMN3
✓ IndexDefinition 更新測試通過

--- 測試 CanExecute 條件 ---
AddColumnCommand.CanExecute(null): False
AddColumnCommand.CanExecute("NON_EXISTENT"): False
RemoveColumnCommand.CanExecute("COLUMN1"): True
MoveColumnUpCommand.CanExecute("COLUMN1"): False
MoveColumnDownCommand.CanExecute("COLUMN3"): False
✓ CanExecute 條件測試通過

=== Task 4 實作驗證完成 - 所有測試通過 ===
```

## 符合需求驗證

### Requirements 2.2, 2.3, 2.4 驗證
- ✅ **2.2**: 用戶選擇左邊listbox中的欄位並點擊移動按鈕時，系統將該欄位移動到右邊的索引欄位listbox
- ✅ **2.3**: 用戶選擇右邊listbox中的欄位並點擊移動按鈕時，系統將該欄位移回左邊的可用欄位listbox  
- ✅ **2.4**: 用戶在右邊listbox中調整欄位順序時，系統更新索引欄位的排序

## 技術特點

1. **命令模式實作**: 使用RelayCommand<string>實作所有命令，支援參數傳遞和CanExecute檢查
2. **資料一致性**: 每次操作後自動更新IndexDefinition.Columns和DDL預覽
3. **錯誤處理**: 完整的參數驗證和邊界條件檢查
4. **可測試性**: 所有邏輯都可以透過單元測試驗證
5. **使用者體驗**: CanExecute條件確保按鈕狀態正確反映可執行性

## 結論

Task 4 已成功完成，所有要求的功能都已實作並通過測試：

1. ✅ 在IndexEditorViewModel中新增了四個欄位選擇相關命令
2. ✅ 實作了完整的命令執行邏輯，包含CanExecute條件檢查
3. ✅ 整合了ColumnSelectionManager來處理欄位移動操作
4. ✅ 創建了完整的單元測試驗證所有命令的執行邏輯
5. ✅ 所有測試都通過，功能運作正常

實作符合設計文件的要求，並且具有良好的可維護性和可測試性。