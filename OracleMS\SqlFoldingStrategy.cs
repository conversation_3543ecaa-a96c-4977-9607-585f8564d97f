
using ICSharpCode.AvalonEdit.Document;
using ICSharpCode.AvalonEdit.Folding;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Documents;
using System.Windows.Shapes;

namespace OracleMS
{
    /// <summary>
    /// SQL 摺疊區塊的包裝類別，封裝 AvalonEdit 的 NewFolding 物件
    /// 並提供額外的結束模式匹配功能
    /// </summary>
    public class SQLFolding
    {
        /// <summary>
        /// 建構函式：建立具有指定起始和結束位置的 SQL 摺疊區塊
        /// </summary>
        /// <param name="startOffset">摺疊區塊的起始字元位置</param>
        /// <param name="endOffset">摺疊區塊的結束字元位置</param>
        public SQLFolding(int startOffset, int endOffset)
        {
            //fold = new NewFolding();
            fold.StartOffset = startOffset;
            fold.EndOffset = endOffset;
        }

        /// <summary>
        /// 預設建構函式：建立空的 SQL 摺疊區塊
        /// </summary>
        //public SQLFolding()
        //{
        //    fold = new NewFolding();
        //}

        /// <summary>
        /// AvalonEdit 的摺疊物件實例
        /// </summary>
        public NewFolding fold { get; set; } = new();

        /// <summary>
        /// 用於匹配此摺疊區塊結束標記的正規表達式模式
        /// </summary>
        public string endPattern { get; set; } = string.Empty;
    }

    /// <summary>
    /// SQL 程式碼摺疊策略類別，用於在 AvalonEdit 編輯器中實現 SQL 語法的程式碼摺疊功能
    /// 支援 PROCEDURE、FUNCTION、BEGIN、IF、WHILE、LOOP 等 SQL 語法結構的摺疊
    /// </summary>
    public class SqlFoldingStrategy
    {
        /// <summary>
        /// 除錯日誌檔案的路徑
        /// 用於記錄摺疊區塊分析過程的詳細資訊
        /// </summary>
        private string log = @"f:\avanlon.log";

        /// <summary>
        /// 搜尋文本中下一個可摺疊區塊的起始位置
        /// 支援 PROCEDURE、FUNCTION、BEGIN、IF、WHILE、FOR、LOOP 等 SQL 語法結構
        /// </summary>
        /// <param name="text">要搜尋的完整文本</param>
        /// <param name="startOffset">開始搜尋的字元位置</param>
        /// <param name="endBlock">輸出參數：對應的結束區塊正規表達式模式</param>
        /// <returns>找到的第一個區塊起始匹配結果</returns>
        private Match searchBlockStart(string text, int startOffset, ref string endBlock)
        {
            int minMatchIndex = -1; // 記錄最早出現的區塊位置
            Match result = Match.Empty;

            // 1. 搜尋 PROCEDURE 或 FUNCTION 定義
            // 匹配模式：PROCEDURE/FUNCTION 名稱 ... IS/AS
            var regex = new Regex(@"\b(PROCEDURE|FUNCTION)[\s\r\n]+(\w+)[\s\S]*?\b(IS|AS)\b", RegexOptions.IgnoreCase);
            var match = regex.Match(text.Substring(startOffset));
            result = match;
            int matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                // 建立對應的結束模式：END; 或 END 程序名;
                endBlock = $@"\bEND(?:[\s\r\n]*;|[\s\r\n]+{match.Groups[2].Value}[\s\r\n]*;)";
            }

            // 2. 搜尋 BEGIN 區塊
            regex = new Regex(@"\b(BEGIN)\b+", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                result = match;
                // BEGIN 對應的結束模式：END;
                endBlock = @"\bEND[\s\r\n]*;";
            }

            // 3. 搜尋 IF...THEN 條件區塊
            regex = new Regex(@"\bIF\b(?!\s*;)[\s\S]*?\bTHEN\b", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                result = match;
                // IF 對應的結束模式：END IF;
                endBlock = @"\bEND\s+IF\b\s*;";
            }

            // 4. 搜尋 FOR/WHILE...LOOP 迴圈區塊
            regex = new Regex(@"\b(FOR|WHILE)\b[\s\S]*?\bLOOP\b", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            matchIndex = match.Success ? startOffset + match.Index : -1;
            if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
            {
                minMatchIndex = matchIndex;
                result = match;
                // FOR/WHILE LOOP 對應的結束模式：END LOOP;
                endBlock = @"\bEND\s+LOOP\b\s*;";
            }

            // 5. 搜尋單純的 LOOP 區塊（需要特殊處理避免與 WHILE LOOP 衝突）
            regex = new Regex(@"\b(LOOP)\b", RegexOptions.IgnoreCase);
            match = regex.Match(text.Substring(startOffset));
            if (match.Success)
            {
                // 檢查是否已經被包含在 END...LOOP 結構中
                regex = new Regex(@"\bEND\b[\s\S]*?\bLOOP\b", RegexOptions.IgnoreCase);
                var match2 = regex.Match(text.Substring(startOffset));
                if (match2.Success)
                {
                    // 如果 LOOP 不在 END...LOOP 範圍內，才視為獨立的 LOOP 區塊
                    if (!(match.Index > match2.Index && match.Index + match.Length <= match2.Index + match2.Length))
                    {
                        matchIndex = match.Success ? startOffset + match.Index : -1;
                        if ((matchIndex < minMatchIndex || minMatchIndex < 0) && matchIndex >= 0)
                        {
                            minMatchIndex = matchIndex;
                            result = match;
                            // LOOP 對應的結束模式：END LOOP;
                            endBlock = @"\bEND\s+LOOP\b\s*;";
                        }
                    }
                }
            }

            return result;
        }
        
        /// <summary>
        /// 遞迴建構摺疊區塊的核心方法
        /// 處理巢狀的 SQL 語法結構，建立對應的摺疊區塊
        /// </summary>
        /// <param name="document">文件物件</param>
        /// <param name="searchStart">開始搜尋的位置</param>
        /// <param name="folding">當前處理的摺疊區塊</param>
        /// <param name="foldings">所有摺疊區塊的集合</param>
        /// <param name="level">巢狀層級（用於除錯日誌的縮排）</param>
        /// <returns>處理完成後的文字位置</returns>
        private int constructFolding(TextDocument document, int searchStart, SQLFolding folding, List<SQLFolding> foldings, int level)
        {
            var text = document.Text;
            DocumentLine? line = document.GetLineByOffset(searchStart);
            string endBlock = string.Empty;
            string pattern = folding.endPattern;
            int startFrom = searchStart;
            int startP;
            while (startFrom < text.Length - 1)
            {
                var nextStart = searchBlockStart(text, startFrom, ref endBlock);
                writelog(document, startFrom, $" try searchBlockStart @ ", level);
                if (nextStart.Success)
                {
                    writelog(document, startFrom + nextStart.Index, $"  --- search OK ", level);
                }

                startP = nextStart.Success ? startFrom + nextStart.Index : -1;

                var regex = new Regex(folding.endPattern, RegexOptions.IgnoreCase); // 搜尋上層區塊的結束標記
                var matchEnd = regex.Match(text.Substring(startFrom));
                writelog(document, -1, $" 搜尋結束標記 @start:{startFrom} ~{folding.endPattern}~", level);

                int endP = matchEnd.Success ? startFrom + matchEnd.Index : -1;
                if (endP > 0)
                {
                    writelog(document, startFrom + matchEnd.Index, $"  --- end match OK ", level);
                    folding.fold.EndOffset = endP + matchEnd.Length;
                    writelog(document, folding, level);

                    if (endP < startP || startP < 0)
                    {
                        writelog(document, endP, $" 上層的結束標記先找到, return", level);
                        return folding.fold.EndOffset;
                    }
                }

                if (endP < 0 && startP < 0)
                {
                    folding.fold.EndOffset = text.Length;
                    writelog(document, -1, $" 既沒有找到結束標記也沒有找到起始區塊", level);
                    writelog(document, folding.fold.StartOffset, $" ==", level);
                    writelog(document, folding.fold.EndOffset, $" ====", level);
                    writelog(document, folding, level);
                    return folding.fold.EndOffset;
                }

                if (startP > 0)
                {
                    writelog(document, -1, $" 找到了新的起始區塊", level);

                    var newFolding = new SQLFolding(startP, startP)
                    {
                        endPattern = endBlock
                    };
                    newFolding.fold.Name = nextStart.Value; // 設定子區塊的名稱

                    foldings.Add(newFolding);
                    writelog(document, startP, $" $$$$建立新的子摺疊區塊 = ", level);

                    startFrom = constructFolding(document, startP + nextStart.Length, newFolding, foldings, level + 8);
                    writelog(document, startFrom, $" 結束constructFolding return {startFrom}", level);

                    if (startFrom >= folding.fold.EndOffset && folding.fold.EndOffset > 0) 
                    {
                        writelog(document, -1, $" 新的位置大於當前摺疊區塊的結束位置，需要重新搜尋結束標記", level);
                        var newEndMatch = regex.Match(text.Substring(startFrom));
                        endP = newEndMatch.Success ? startFrom + newEndMatch.Index : -1;
                        if (endP > 0)
                        {
                            writelog(document, folding.fold.StartOffset, $" 本層開始區塊", level);
                            writelog(document, endP, $" 新結束標記", level);
                            folding.fold.EndOffset = endP + newEndMatch.Length;
                            writelog(document, folding, level);
                        }

                        if (endP < startFrom)
                        {
                            writelog(document, startFrom, $" 新結束標記在下個起始區之前，本區結束，返回本區結束位置", level);
                            // 新結束標記在下一個起始區塊之前，表示本區塊已結束，直接返回當前摺疊區塊的結束位置
                            return startFrom;
                        }
                    }
                }
            }
            return text.Length;
        }

        /// <summary>
        /// 寫入除錯日誌的輔助方法
        /// 記錄指定位置的文字內容和相關資訊到日誌檔案
        /// </summary>
        /// <param name="document">文件物件</param>
        /// <param name="offset">要記錄的字元位置</param>
        /// <param name="note">附加的註解訊息</param>
        /// <param name="level">巢狀層級（用於縮排顯示）</param>
        private void writelog(TextDocument document, int offset, string note, int level)
        {
            //如果位置無效，只記錄註解訊息
            if (offset < 0 || offset >= document.TextLength)
            {
                System.IO.File.AppendAllText(log, $"{"".PadRight(level)}{note}\n");
                return;
            }

            // 取得指定位置所在的行資訊
            DocumentLine line = document.GetLineByOffset(offset);
            string lineText = document.GetText(line.Offset, line.Length);

            // 記錄註解、行號、位置和行內容
            System.IO.File.AppendAllText(log, $"{"".PadRight(level)}{note}:[{line.LineNumber}/{offset}] {lineText}\n");
        }
        /// <summary>
        /// 記錄摺疊區塊資訊的輔助方法
        /// 將摺疊區塊的起始和結束位置資訊寫入日誌檔案
        /// </summary>
        /// <param name="document">文件物件</param>
        /// <param name="folding">要記錄的摺疊區塊</param>
        /// <param name="level">巢狀層級（用於縮排顯示）</param>
        private void writelog(TextDocument document, SQLFolding folding, int level)
        {
            // 記錄摺疊區塊的起始位置和內容
            DocumentLine line = document.GetLineByOffset(folding.fold.StartOffset);
            string lineText = document.GetText(line.Offset, line.Length);
            System.IO.File.AppendAllText(log, $"{"".PadRight(level)}:###[{line.LineNumber}/{line.Offset}] {lineText}\n");

            // 記錄摺疊區塊的結束位置和內容
            line = document.GetLineByOffset(folding.fold.EndOffset);
            lineText = document.GetText(line.Offset, line.Length);
            System.IO.File.AppendAllText(log, $"{"".PadRight(level)}:===[{line.LineNumber}/{line.Offset}] {lineText}\n");
        }

        /// <summary>
        /// 建立新的摺疊區塊集合的主要方法
        /// 掃描整個文件，找出所有可摺疊的 SQL 語法結構並建立對應的摺疊區塊
        /// </summary>
        /// <param name="document">要處理的文件物件</param>
        /// <param name="firstErrorOffset">輸出參數：第一個錯誤的位置（目前未使用）</param>
        /// <returns>所有建立的摺疊區塊集合</returns>
        public IEnumerable<NewFolding> CreateNewFoldings(TextDocument document, out int firstErrorOffset)
        {
            firstErrorOffset = -1; // 目前沒有錯誤處理，設為 -1
            var text = document.Text;
            var sqlfoldings = new List<SQLFolding>();

            // 清空日誌檔案，開始新的分析過程
            System.IO.File.WriteAllText(log, string.Empty);

            string endBlock = string.Empty;
            int index = 0;
            
            // 從文件開頭開始掃描，尋找所有頂層的可摺疊區塊
            while (index < text.Length - 2 && index > -1)
            {
                var block0 = searchBlockStart(text, index, ref endBlock);
                if (block0.Success)
                {
                    writelog(document, index + block0.Index, "root", 0);

                    // 建立新的頂層摺疊區塊
                    var sqlfolding = new SQLFolding(0, 0);
                    sqlfolding.fold.StartOffset = index + block0.Index;
                    sqlfolding.fold.Name = block0.Value;
                    sqlfolding.endPattern = endBlock;
                    sqlfolding.fold.EndOffset = sqlfolding.fold.StartOffset;
                    sqlfoldings.Add(sqlfolding);

                    // 遞迴處理此區塊內的子區塊
                    index = constructFolding(document, index + block0.Index + block0.Length, sqlfolding, sqlfoldings, 0);
                } else
                {
                    // 沒有找到更多區塊，結束掃描
                    break;
                }
                //writelog(document, index, "rend", 0);
            }

            // 轉換為 AvalonEdit 的摺疊物件格式
            var foldings = sqlfoldings.Select(a => a.fold).OrderBy(a => a.StartOffset).ToList();

            // 如果外層區塊沒有完整包覆內層區塊，則將其結束位置設為內層區塊的結束位置
            var foldingCount = foldings.Count;
            for (int i = foldingCount - 1; i > 0; i--)
            {
                var f = foldings[i];
                var prevf = foldings[i - 1];
                if (prevf.EndOffset > f.StartOffset && prevf.EndOffset < f.EndOffset)
                {
                    prevf.EndOffset = f.EndOffset;
                }
            }

            // 後處理：修正可能的空區塊問題
            for (int i = 0; i < foldings.Count; i++)
            {
                var f = foldings[i];
                // 如果摺疊區塊的起始和結束位置相同，使用前一個區塊的結束位置
                if (i > 0 && f.StartOffset == f.EndOffset)
                {
                    f.EndOffset = foldings[i - 1].EndOffset;
                }
                //// 記錄最終的摺疊區塊範圍到日誌
                writelog(document, f.StartOffset, $"/*", 0);
                writelog(document, f.EndOffset, $"*/", 0);
                writelog(document, -1, $" ", 0);
            }
            return foldings;
        }

        /// <summary>
        /// 更新摺疊管理器中的摺疊區塊
        /// 這是對外提供的主要方法，用於更新編輯器中的摺疊顯示
        /// </summary>
        /// <param name="manager">AvalonEdit 的摺疊管理器</param>
        /// <param name="document">要處理的文件物件</param>
        public void UpdateFoldings(FoldingManager manager, TextDocument document)
        {
            int firstErrorOffset;
            // 建立新的摺疊區塊集合
            var cloneDoc = new TextDocument();
            string masked = Regex.Replace(
                document.Text,
                @"/\*.*?\*/",
                match => {
                    var original = match.Value;
                    var masked = new StringBuilder();
                    foreach (char c in original)
                    {
                        if (c == '\r' || c == '\n' || c == '\t')
                            masked.Append(c);
                        else
                            masked.Append(' '); // 用空白遮蔽
                    }
                    return masked.ToString();
                },
                RegexOptions.Singleline
            );

            masked = Regex.Replace(
                masked,
                @"--.*?$",
                match => {
                    var original = match.Value;
                    var masked = new StringBuilder();
                    foreach (char c in original)
                    {
                        if (c == '\r' || c == '\n' || c == '\t')
                            masked.Append(c);
                        else
                            masked.Append(' ');
                    }
                    return masked.ToString();
                },
                RegexOptions.Multiline
            );

            cloneDoc.Text = masked; // 複製文本內容

            var newFoldings = CreateNewFoldings(cloneDoc, out firstErrorOffset);
            // 更新摺疊管理器
            manager.UpdateFoldings(newFoldings, firstErrorOffset);
        }
    }
}
