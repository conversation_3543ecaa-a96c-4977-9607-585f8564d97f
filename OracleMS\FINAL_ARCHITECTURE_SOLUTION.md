# Oracle 管理系統架構文件

*最後更新: 2025-07-20*

## 資料庫物件編輯器架構

### 概述

資料庫物件編輯器是 Oracle 管理系統的核心功能之一，提供了視覺化的介面來檢視和編輯各種 Oracle 資料庫物件。本文件描述了物件編輯器的架構設計和實作方式。

### 架構圖

```mermaid
graph TB
    subgraph "UI Layer"
        OEV[ObjectExplorerView] --> |雙擊物件| DbSession
        DbSession --> |創建TabItem| EditorFactory[ObjectEditorFactory]
        EditorFactory --> TableEditor[TableEditorView]
        EditorFactory --> ViewEditor[ViewEditorView]
        EditorFactory --> ProcedureEditor[ProcedureEditorView]
        EditorFactory --> FunctionEditor[FunctionEditorView]
        EditorFactory --> PackageEditor[PackageEditorView]
        EditorFactory --> SequenceEditor[SequenceEditorView]
        EditorFactory --> TriggerEditor[TriggerEditorView]
        EditorFactory --> IndexEditor[IndexEditorView]
    end

    subgraph "ViewModel Layer"
        TableEditor --> TableEditorVM[TableEditorViewModel]
        ViewEditor --> ViewEditorVM[ViewEditorViewModel]
        ProcedureEditor --> ProcedureEditorVM[ProcedureEditorViewModel]
        FunctionEditor --> FunctionEditorVM[FunctionEditorViewModel]
        PackageEditor --> PackageEditorVM[PackageEditorViewModel]
        SequenceEditor --> SequenceEditorVM[SequenceEditorViewModel]
        TriggerEditor --> TriggerEditorVM[TriggerEditorViewModel]
        IndexEditor --> IndexEditorVM[IndexEditorViewModel]
        
        BaseEditorVM[BaseObjectEditorViewModel] --> TableEditorVM
        BaseEditorVM --> ViewEditorVM
        BaseEditorVM --> ProcedureEditorVM
        BaseEditorVM --> FunctionEditorVM
        BaseEditorVM --> PackageEditorVM
        BaseEditorVM --> SequenceEditorVM
        BaseEditorVM --> TriggerEditorVM
        BaseEditorVM --> IndexEditorVM
    end

    subgraph "Service Layer"
        TableEditorVM --> DatabaseService[IDatabaseService]
        ViewEditorVM --> DatabaseService
        ProcedureEditorVM --> DatabaseService
        FunctionEditorVM --> DatabaseService
        PackageEditorVM --> DatabaseService
        SequenceEditorVM --> DatabaseService
        TriggerEditorVM --> DatabaseService
        IndexEditorVM --> DatabaseService
        
        TableEditorVM --> ScriptService[IScriptGeneratorService]
        ViewEditorVM --> ScriptService
        ProcedureEditorVM --> ScriptService
        FunctionEditorVM --> ScriptService
        PackageEditorVM --> ScriptService
        SequenceEditorVM --> ScriptService
        TriggerEditorVM --> ScriptService
        IndexEditorVM --> ScriptService
        
        TableEditorVM --> ObjectService[IObjectEditorService]
        ViewEditorVM --> ObjectService
        ProcedureEditorVM --> ObjectService
        FunctionEditorVM --> ObjectService
        PackageEditorVM --> ObjectService
        SequenceEditorVM --> ObjectService
        TriggerEditorVM --> ObjectService
        IndexEditorVM --> ObjectService
    end

    subgraph "Data Layer"
        DatabaseService --> Database[(Oracle Database)]
        ScriptService --> Database
        ObjectService --> Database
    end
```

### 核心設計原則

1. **一致性**: 所有編輯器遵循相同的 UI 模式和操作流程
2. **可擴展性**: 使用工廠模式和基底類別，便於新增其他物件類型
3. **重用性**: 最大化利用現有的服務和基礎設施
4. **分離關注點**: UI、業務邏輯和資料存取清楚分離
5. **效能**: 使用延遲載入和快取機制

### 關鍵元件

#### 1. 基底類別和介面

- **BaseObjectEditorViewModel**: 所有物件編輯器 ViewModel 的基底類別
- **IObjectEditorService**: 定義與資料庫物件互動的方法
- **ISaveable**: 定義儲存功能的介面
- **ObjectEditorErrorHandler**: 統一的錯誤處理機制

#### 2. 工廠模式

- **ObjectEditorFactory**: 根據物件類型創建對應的編輯器

#### 3. 資料模型

- **TableDefinition**: 資料表結構定義
- **ColumnDefinition**: 欄位定義
- **ConstraintDefinition**: 約束條件定義
- **IndexDefinition**: 索引定義
- **PackageDefinition**: 套件定義
- **SequenceDefinition**: 序列定義
- **TriggerDefinition**: 觸發器定義

#### 4. 依賴注入

- 使用 Microsoft.Extensions.DependencyInjection 進行服務註冊
- 在 ServiceCollectionExtensions 中註冊 IObjectEditorService 和各 ViewModel

### 工作流程

#### 開啟物件編輯器

1. 用戶在 ObjectExplorerView 中雙擊物件
2. ObjectExplorerViewModel 觸發開啟編輯器事件
3. DbSession 接收事件並呼叫 CreateObjectEditorTab 方法
4. CreateObjectEditorTab 使用 ObjectEditorFactory 創建對應的編輯器
5. 編輯器被加入到 DbSession 的 TabControl 中
6. 編輯器的 ViewModel 呼叫 InitializeAsync 方法載入物件定義

#### 編輯和儲存物件

1. 用戶在編輯器中修改物件定義
2. ViewModel 追蹤變更並設置 HasUnsavedChanges 屬性
3. 用戶點擊儲存按鈕，觸發 SaveCommand
4. ViewModel 呼叫 SaveAsync 方法
5. SaveAsync 呼叫 IObjectEditorService 的對應方法儲存變更
6. 儲存成功後，重置 HasUnsavedChanges 屬性

### 效能考量

1. **延遲載入**: 物件定義在編輯器開啟時才載入，而不是預先載入
2. **資料快取**: 避免重複載入相同物件的定義
3. **非同步操作**: 所有資料庫操作都使用非同步方法
4. **UI 虛擬化**: 使用 UI 虛擬化技術處理大量資料

### 錯誤處理

1. **統一錯誤處理**: 使用 ObjectEditorErrorHandler 處理所有錯誤
2. **使用者友善訊息**: 將技術錯誤訊息轉換為使用者友善的訊息
3. **錯誤記錄**: 將錯誤記錄到日誌檔案
4. **錯誤恢復**: 提供錯誤恢復機制，如復原編輯

### 擴展性

1. **新增物件類型**: 可以通過以下步驟新增新的物件類型編輯器
   - 創建物件定義類別
   - 在 IObjectEditorService 中新增相關方法
   - 實作 ObjectEditorService 中的方法
   - 創建 ViewModel 類別（繼承 BaseObjectEditorViewModel）
   - 創建 View 類別
   - 在 ObjectEditorFactory 中新增創建方法
   - 在 DatabaseObjectType 中新增物件類型
   - 在 ServiceCollectionExtensions 中註冊 ViewModel

2. **自訂編輯器行為**: 可以通過覆寫 BaseObjectEditorViewModel 的方法來自訂編輯器行為

### 測試策略

1. **單元測試**: 測試 ViewModel 和 Service 的業務邏輯
2. **整合測試**: 測試編輯器與 DbSession 和資料庫的整合
3. **效能測試**: 測試多個編輯器同時開啟的效能

### 結論

資料庫物件編輯器架構採用了 MVVM 模式、工廠模式和依賴注入等設計模式，提供了一個可擴展、可維護和高效能的解決方案。通過統一的介面和一致的使用者體驗，使用者可以輕鬆地管理各種 Oracle 資料庫物件。