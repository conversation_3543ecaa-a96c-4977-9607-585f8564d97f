using Microsoft.Extensions.Logging;
using OracleMS.Exceptions;
using System.Windows;
using Oracle.ManagedDataAccess.Client;

namespace OracleMS.Services;

/// <summary>
/// 全域例外處理服務
/// </summary>
public class GlobalExceptionHandler
{
    private readonly ILogger<GlobalExceptionHandler> _logger;

    public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 處理例外並顯示使用者友善的錯誤訊息
    /// </summary>
    /// <param name="ex">例外物件</param>
    /// <param name="context">發生例外的上下文</param>
    public void HandleException(Exception ex, string context = "")
    {
        // 記錄詳細錯誤資訊
        _logger.LogError(ex, "Error occurred in context: {Context}", context);

        // 取得使用者友善的錯誤訊息
        var userMessage = GetUserFriendlyMessage(ex);
        var title = GetErrorTitle(ex);

        // 顯示錯誤對話框
        Application.Current.Dispatcher.Invoke(() =>
        {
            MessageBox.Show(userMessage, title, MessageBoxButton.OK, MessageBoxImage.Error);
        });
    }

    /// <summary>
    /// 處理例外但不顯示對話框，僅記錄日誌
    /// </summary>
    /// <param name="ex">例外物件</param>
    /// <param name="context">發生例外的上下文</param>
    public void LogException(Exception ex, string context = "")
    {
        _logger.LogError(ex, "Error occurred in context: {Context}", context);
    }

    /// <summary>
    /// 取得使用者友善的錯誤訊息
    /// </summary>
    /// <param name="ex">例外物件</param>
    /// <returns>使用者友善的錯誤訊息</returns>
    public string GetUserFriendlyMessage(Exception ex)
    {
        return ex switch
        {
            DatabaseConnectionException dbEx => $"資料庫連線失敗：{dbEx.Message}\n連線名稱：{dbEx.ConnectionName}",
            
            SqlExecutionException sqlEx when sqlEx.LineNumber > 0 => 
                $"SQL 執行錯誤（第 {sqlEx.LineNumber} 行）：{sqlEx.Message}",
            
            SqlExecutionException sqlEx => $"SQL 執行錯誤：{sqlEx.Message}",
            
            DataValidationException validEx => $"資料驗證失敗：{validEx.Message}\n欄位：{validEx.FieldName}",
            
            ConfigurationException configEx => $"設定錯誤：{configEx.Message}\n設定項目：{configEx.ConfigurationKey}",
            
            OracleException oracleEx => GetOracleErrorMessage(oracleEx),
            
            TimeoutException => "操作逾時，請檢查網路連線或資料庫狀態",
            
            UnauthorizedAccessException => "存取被拒絕，請檢查檔案權限或使用者權限",
            
            OutOfMemoryException => "記憶體不足，請關閉其他應用程式或減少資料查詢範圍",
            
            ArgumentException argEx => $"參數錯誤：{argEx.Message}",
            
            InvalidOperationException => $"無效的操作：{ex.Message}",
            
            OracleManagementException omsEx => $"應用程式錯誤 ({omsEx.ErrorCode})：{omsEx.Message}",
            
            _ => $"發生未預期的錯誤：{ex.Message}"
        };
    }

    /// <summary>
    /// 取得錯誤對話框標題
    /// </summary>
    /// <param name="ex">例外物件</param>
    /// <returns>錯誤對話框標題</returns>
    private string GetErrorTitle(Exception ex)
    {
        return ex switch
        {
            DatabaseConnectionException => "資料庫連線錯誤",
            SqlExecutionException => "SQL 執行錯誤",
            DataValidationException => "資料驗證錯誤",
            ConfigurationException => "設定錯誤",
            OracleException => "Oracle 資料庫錯誤",
            TimeoutException => "操作逾時",
            UnauthorizedAccessException => "存取權限錯誤",
            OutOfMemoryException => "記憶體不足",
            _ => "應用程式錯誤"
        };
    }

    /// <summary>
    /// 取得 Oracle 特定錯誤的友善訊息
    /// </summary>
    /// <param name="oracleEx">Oracle 例外</param>
    /// <returns>友善的錯誤訊息</returns>
    private string GetOracleErrorMessage(OracleException oracleEx)
    {
        return oracleEx.Number switch
        {
            1017 => "使用者名稱或密碼無效",
            1045 => "使用者沒有 CREATE SESSION 權限",
            12154 => "TNS: 無法解析指定的連線識別碼",
            12514 => "TNS: 監聽程式目前無法識別連線描述元中請求的服務",
            12541 => "TNS: 沒有監聽程式",
            28000 => "帳戶已被鎖定",
            28001 => "密碼已過期",
            942 => "資料表或檢視不存在",
            955 => "物件名稱已被使用",
            1031 => "權限不足",
            1400 => "無法將 NULL 插入必填欄位",
            1 => "違反唯一約束條件",
            2291 => "違反完整性約束條件 - 找不到父鍵",
            2292 => "違反完整性約束條件 - 找到子記錄",
            _ => $"Oracle 錯誤 (ORA-{oracleEx.Number:D5})：{oracleEx.Message}"
        };
    }

    /// <summary>
    /// 判斷例外是否為可重試的錯誤
    /// </summary>
    /// <param name="ex">例外物件</param>
    /// <returns>是否可重試</returns>
    public bool IsRetryableError(Exception ex)
    {
        return ex switch
        {
            TimeoutException => true,
            OracleException oracleEx => oracleEx.Number switch
            {
                12154 or 12514 or 12541 => true, // 網路相關錯誤
                _ => false
            },
            _ => false
        };
    }

    /// <summary>
    /// 判斷例外是否為嚴重錯誤（需要關閉應用程式）
    /// </summary>
    /// <param name="ex">例外物件</param>
    /// <returns>是否為嚴重錯誤</returns>
    public bool IsCriticalError(Exception ex)
    {
        return ex switch
        {
            OutOfMemoryException => true,
            StackOverflowException => true,
            AccessViolationException => true,
            _ => false
        };
    }
}