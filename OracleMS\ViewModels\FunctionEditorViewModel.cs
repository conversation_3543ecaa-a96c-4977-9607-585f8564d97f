using System;
using System.Collections.ObjectModel;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels
{
    /// <summary>
    /// 函數編輯器 ViewModel
    /// </summary>
    public class FunctionEditorViewModel : BaseObjectEditorViewModel
    {
        private string _functionSource = string.Empty;
        private ObservableCollection<FunctionParameter> _parameters = new();
        private ObservableCollection<DependencyInfo> _dependencies = new();
        private ObservableCollection<CompilationError> _compilationErrors = new();
        private string _compilationStatus = "尚未編譯";
        private bool _isCompilationSuccessful;
        private bool _hasCompilationErrors;
        private TimeSpan _lastCompilationTime;
        private string _executionStatus = "尚未執行";
        private string _executionResult = string.Empty;
        private string _executionErrorMessage = string.Empty;
        private bool _hasExecutionResult;
        private bool _hasExecutionError;
        private TimeSpan _executionTime;
        private bool _hasParameters;

        /// <summary>
        /// 函數原始碼
        /// </summary>
        public string FunctionSource
        {
            get => _functionSource;
            set
            {
                if (SetProperty(ref _functionSource, value))
                {
                    if (!_isInitializing)
                    {
                        HasUnsavedChanges = true;
                    }
                }
            }
        }

        /// <summary>
        /// 參數資訊集合
        /// </summary>
        public ObservableCollection<FunctionParameter> Parameters
        {
            get => _parameters;
            private set => SetProperty(ref _parameters, value);
        }

        /// <summary>
        /// 相依性資訊集合
        /// </summary>
        public ObservableCollection<DependencyInfo> Dependencies
        {
            get => _dependencies;
            private set => SetProperty(ref _dependencies, value);
        }

        /// <summary>
        /// 編譯錯誤集合
        /// </summary>
        public ObservableCollection<CompilationError> CompilationErrors
        {
            get => _compilationErrors;
            private set => SetProperty(ref _compilationErrors, value);
        }

        /// <summary>
        /// 編譯狀態
        /// </summary>
        public string CompilationStatus
        {
            get => _compilationStatus;
            private set => SetProperty(ref _compilationStatus, value);
        }

        /// <summary>
        /// 編譯是否成功
        /// </summary>
        public bool IsCompilationSuccessful
        {
            get => _isCompilationSuccessful;
            private set => SetProperty(ref _isCompilationSuccessful, value);
        }

        /// <summary>
        /// 是否有編譯錯誤
        /// </summary>
        public bool HasCompilationErrors
        {
            get => _hasCompilationErrors;
            private set => SetProperty(ref _hasCompilationErrors, value);
        }

        /// <summary>
        /// 最後編譯時間
        /// </summary>
        public TimeSpan LastCompilationTime
        {
            get => _lastCompilationTime;
            private set => SetProperty(ref _lastCompilationTime, value);
        }

        /// <summary>
        /// 執行狀態
        /// </summary>
        public string ExecutionStatus
        {
            get => _executionStatus;
            private set => SetProperty(ref _executionStatus, value);
        }

        /// <summary>
        /// 執行結果
        /// </summary>
        public string ExecutionResult
        {
            get => _executionResult;
            private set => SetProperty(ref _executionResult, value);
        }

        /// <summary>
        /// 執行錯誤訊息
        /// </summary>
        public string ExecutionErrorMessage
        {
            get => _executionErrorMessage;
            private set => SetProperty(ref _executionErrorMessage, value);
        }

        /// <summary>
        /// 是否有執行結果
        /// </summary>
        public bool HasExecutionResult
        {
            get => _hasExecutionResult;
            private set => SetProperty(ref _hasExecutionResult, value);
        }

        /// <summary>
        /// 是否有執行錯誤
        /// </summary>
        public bool HasExecutionError
        {
            get => _hasExecutionError;
            private set => SetProperty(ref _hasExecutionError, value);
        }

        /// <summary>
        /// 執行時間
        /// </summary>
        public TimeSpan ExecutionTime
        {
            get => _executionTime;
            private set => SetProperty(ref _executionTime, value);
        }

        /// <summary>
        /// 是否有參數
        /// </summary>
        public bool HasParameters
        {
            get => _hasParameters;
            private set => SetProperty(ref _hasParameters, value);
        }

        /// <summary>
        /// 編譯命令
        /// </summary>
        public ICommand CompileCommand { get; }

        /// <summary>
        /// 驗證命令
        /// </summary>
        public ICommand ValidateCommand { get; }

        /// <summary>
        /// 執行測試命令
        /// </summary>
        public ICommand ExecuteTestCommand { get; }

        /// <summary>
        /// 清除編譯結果命令
        /// </summary>
        public ICommand ClearCompilationResultsCommand { get; }

        /// <summary>
        /// 清除測試結果命令
        /// </summary>
        public ICommand ClearTestResultsCommand { get; }

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="functionName">函數名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        public FunctionEditorViewModel(
            string functionName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
            : base(functionName, DatabaseObjectType.Function, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger)
        {
            // 初始化命令
            CompileCommand = new AsyncRelayCommand(OnCompileAsync, CanCompile);
            ValidateCommand = new AsyncRelayCommand(OnValidateAsync, CanValidate);
            ExecuteTestCommand = new AsyncRelayCommand(OnExecuteTestAsync, CanExecuteTest);
            ClearCompilationResultsCommand = new RelayCommand(OnClearCompilationResults, CanClearCompilationResults);
            ClearTestResultsCommand = new RelayCommand(OnClearTestResults, CanClearTestResults);
        }

        /// <summary>
        /// 載入物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task LoadObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 載入函數原始碼
            FunctionSource = await _objectEditorService.GetFunctionSourceAsync(connection, ObjectName);

            // 載入參數資訊
            await LoadParametersAsync(connection);

            // 載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 儲存物件
        /// </summary>
        /// <returns>非同步工作</returns>
        protected override async Task SaveObjectAsync()
        {
            var connection = _getConnection();
            if (connection == null)
            {
                throw new InvalidOperationException("無法取得資料庫連線");
            }

            // 編譯並儲存函數
            await _objectEditorService.CompileFunctionAsync(connection, ObjectName, FunctionSource);

            // 重新載入參數資訊
            await LoadParametersAsync(connection);

            // 重新載入相依性資訊
            await LoadDependenciesAsync(connection);
        }

        /// <summary>
        /// 產生腳本
        /// </summary>
        /// <returns>腳本</returns>
        protected override string GenerateScript()
        {
            return FunctionSource;
        }

        /// <summary>
        /// 驗證物件
        /// </summary>
        /// <returns>驗證結果</returns>
        protected override ValidationResult ValidateObject()
        {
            var result = new ValidationResult();

            if (string.IsNullOrWhiteSpace(FunctionSource))
            {
                result.AddError("函數原始碼不能為空");
            }
            else
            {
                var upperSource = FunctionSource.Trim().ToUpper();
                if (!upperSource.StartsWith("CREATE OR REPLACE FUNCTION") &&
                    !upperSource.StartsWith("CREATE FUNCTION"))
                {
                    result.AddError("函數原始碼必須以 CREATE [OR REPLACE] FUNCTION 開頭");
                }
            }

            return result;
        }

        /// <summary>
        /// 載入參數資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadParametersAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        ARGUMENT_NAME AS Name,
                        DATA_TYPE AS DataType,
                        IN_OUT AS Direction,
                        DATA_LENGTH AS Length,
                        DATA_PRECISION AS Precision,
                        DATA_SCALE AS Scale,
                        DEFAULT_VALUE AS DefaultValue,
                        POSITION AS Position,
                        '' AS Comments
                    FROM 
                        ALL_ARGUMENTS
                    WHERE 
                        OBJECT_NAME = '{ObjectName}'
                        AND PACKAGE_NAME IS NULL
                    ORDER BY 
                        POSITION";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                Parameters.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        var paramName = row["Name"].ToString() ?? string.Empty;
                        var direction = row["Direction"].ToString() ?? string.Empty;
                        var position = row["Position"] != DBNull.Value ? Convert.ToInt32(row["Position"]) : 0;
                        
                        // 如果是返回值 (position = 0)，不顯示在參數列表中
                        if (position == 0)
                            continue;
                            
                        var parameter = new FunctionParameter
                        {
                            Name = paramName,
                            DataType = row["DataType"].ToString() ?? string.Empty,
                            Direction = direction,
                            Length = row["Length"] != DBNull.Value ? Convert.ToInt32(row["Length"]) : null,
                            Precision = row["Precision"] != DBNull.Value ? Convert.ToInt32(row["Precision"]) : null,
                            Scale = row["Scale"] != DBNull.Value ? Convert.ToInt32(row["Scale"]) : null,
                            DefaultValue = row["DefaultValue"]?.ToString() ?? string.Empty,
                            Comments = row["Comments"]?.ToString() ?? string.Empty,
                            Position = position,
                            IsInput = direction == "IN" || direction == "IN/OUT"
                        };
                        
                        Parameters.Add(parameter);
                    }
                    
                    HasParameters = Parameters.Count > 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入參數資訊失敗：{ex}");
            }
        }

        /// <summary>
        /// 載入相依性資訊
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task LoadDependenciesAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        d.REFERENCED_NAME AS Name,
                        d.REFERENCED_TYPE AS Type,
                        d.REFERENCED_OWNER AS Owner,
                        'DEPENDS ON' AS DependencyType,
                        'This function depends on this object' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.NAME = '{ObjectName}'
                        AND d.TYPE = 'FUNCTION'
                    UNION ALL
                    SELECT 
                        d.NAME AS Name,
                        d.TYPE AS Type,
                        d.OWNER AS Owner,
                        'DEPENDENT' AS DependencyType,
                        'This object depends on this function' AS Description
                    FROM 
                        ALL_DEPENDENCIES d
                    WHERE 
                        d.REFERENCED_NAME = '{ObjectName}'
                        AND d.REFERENCED_TYPE = 'FUNCTION'
                    ORDER BY 
                        DependencyType, Name";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                Dependencies.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        Dependencies.Add(new DependencyInfo
                        {
                            Name = row["Name"].ToString() ?? string.Empty,
                            Type = row["Type"].ToString() ?? string.Empty,
                            Owner = row["Owner"].ToString() ?? string.Empty,
                            DependencyType = row["DependencyType"].ToString() ?? string.Empty,
                            Description = row["Description"].ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"載入相依性資訊失敗：{ex}");
            }
        }

        /// <summary>
        /// 編譯函數
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnCompileAsync()
        {
            if (string.IsNullOrWhiteSpace(FunctionSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                CompilationStatus = "編譯中";
                HasError = false;
                ErrorMessage = string.Empty;
                CompilationErrors.Clear();
                IsCompilationSuccessful = false;
                HasCompilationErrors = false;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 執行編譯
                var stopwatch = Stopwatch.StartNew();
                await _objectEditorService.CompileFunctionAsync(connection, ObjectName, FunctionSource);
                stopwatch.Stop();

                // 檢查編譯錯誤
                await CheckCompilationErrorsAsync(connection);

                LastCompilationTime = stopwatch.Elapsed;

                if (CompilationErrors.Count == 0)
                {
                    CompilationStatus = "編譯成功";
                    IsCompilationSuccessful = true;
                    HasCompilationErrors = false;
                    StatusMessage = $"函數編譯成功，耗時 {LastCompilationTime.TotalSeconds:F2} 秒";
                    
                    // 編譯成功後重新載入參數和相依性資訊
                    await LoadParametersAsync(connection);
                    await LoadDependenciesAsync(connection);
                    
                    // 標記為已儲存
                    HasUnsavedChanges = false;
                }
                else
                {
                    CompilationStatus = "編譯失敗";
                    IsCompilationSuccessful = false;
                    HasCompilationErrors = true;
                    StatusMessage = $"函數編譯失敗，發現 {CompilationErrors.Count} 個錯誤";
                }
            }
            catch (Exception ex)
            {
                CompilationStatus = "編譯失敗";
                IsCompilationSuccessful = false;
                HasCompilationErrors = false;
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"函數編譯失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以編譯
        /// </summary>
        /// <returns>是否可以編譯</returns>
        private bool CanCompile()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(FunctionSource) && _getConnection() != null;
        }

        /// <summary>
        /// 驗證 PL/SQL 語法
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnValidateAsync()
        {
            if (string.IsNullOrWhiteSpace(FunctionSource) || _getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                StatusMessage = "正在驗證 PL/SQL 語法...";
                HasError = false;
                ErrorMessage = string.Empty;

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 執行語法驗證
                // 在 Oracle 中，可以使用 DBMS_SQL.PARSE 來驗證語法
                var validateSql = $@"
                    DECLARE
                        c INTEGER;
                        sql_text CLOB := '{FunctionSource.Replace("'", "''")}';
                    BEGIN
                        c := DBMS_SQL.OPEN_CURSOR;
                        DBMS_SQL.PARSE(c, sql_text, DBMS_SQL.NATIVE);
                        DBMS_SQL.CLOSE_CURSOR(c);
                    END;";

                var rowsAffected = await _databaseService.ExecuteNonQueryAsync(connection, validateSql);
                StatusMessage = "PL/SQL 語法驗證通過";
            }
            catch (Exception ex)
            {
                HasError = true;
                ErrorMessage = ex.Message;
                StatusMessage = $"PL/SQL 語法驗證失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 是否可以驗證語法
        /// </summary>
        /// <returns>是否可以驗證</returns>
        private bool CanValidate()
        {
            return !IsLoading && !IsSaving && !string.IsNullOrWhiteSpace(FunctionSource) && _getConnection() != null;
        }

        /// <summary>
        /// 執行函數測試
        /// </summary>
        /// <returns>非同步工作</returns>
        private async Task OnExecuteTestAsync()
        {
            if (_getConnection() == null)
                return;

            try
            {
                IsLoading = true;
                ExecutionStatus = "執行中...";
                HasExecutionResult = false;
                HasExecutionError = false;
                ExecutionResult = string.Empty;
                ExecutionErrorMessage = string.Empty;
                StatusMessage = "正在執行函數測試...";

                var connection = _getConnection();
                if (connection == null)
                {
                    throw new InvalidOperationException("無法取得資料庫連線");
                }

                // 建立函數呼叫 SQL
                var sql = BuildFunctionCallSql();

                // 執行函數
                var stopwatch = Stopwatch.StartNew();
                var result = await _databaseService.ExecuteScalarAsync(connection, sql);
                stopwatch.Stop();

                ExecutionTime = stopwatch.Elapsed;

                if (result.IsSuccess)
                {
                    ExecutionStatus = "執行成功";
                    // 從 DataTable 中提取標量值
                    var scalarValue = result.Data?.Rows.Count > 0 ? result.Data.Rows[0]["Result"] : null;
                    ExecutionResult = scalarValue?.ToString() ?? "NULL";
                    HasExecutionResult = true;
                    HasExecutionError = false;
                    StatusMessage = $"函數執行成功，耗時 {ExecutionTime.TotalSeconds:F2} 秒";
                }
                else
                {
                    ExecutionStatus = "執行失敗";
                    ExecutionErrorMessage = result.ErrorMessage;
                    HasExecutionResult = false;
                    HasExecutionError = true;
                    StatusMessage = $"函數執行失敗：{result.ErrorMessage}";
                }
            }
            catch (Exception ex)
            {
                ExecutionStatus = "執行失敗";
                ExecutionErrorMessage = ex.Message;
                HasExecutionResult = false;
                HasExecutionError = true;
                StatusMessage = $"函數執行失敗：{ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 建立函數呼叫 SQL
        /// </summary>
        /// <returns>SQL 語句</returns>
        private string BuildFunctionCallSql()
        {
            // 建立函數呼叫 SQL
            var sql = new System.Text.StringBuilder();
            sql.AppendLine("DECLARE");
            sql.AppendLine("  v_result VARCHAR2(4000);");
            sql.AppendLine("BEGIN");
            
            // 函數呼叫
            sql.Append("  v_result := ");
            sql.Append(ObjectName);
            sql.Append("(");
            
            // 參數
            var paramCount = 0;
            foreach (var param in Parameters)
            {
                if (paramCount > 0)
                    sql.Append(", ");
                
                // 根據參數類型處理參數值
                if (string.IsNullOrEmpty(param.Value))
                {
                    sql.Append("NULL");
                }
                else if (param.DataType.ToUpper().Contains("VARCHAR") || 
                         param.DataType.ToUpper().Contains("CHAR") || 
                         param.DataType.ToUpper().Contains("DATE") || 
                         param.DataType.ToUpper().Contains("TIMESTAMP"))
                {
                    sql.Append($"'{param.Value.Replace("'", "''")}'");
                }
                else
                {
                    sql.Append(param.Value);
                }
                
                paramCount++;
            }
            
            sql.AppendLine(");");
            
            // 輸出結果
            sql.AppendLine("  :result := v_result;");
            sql.AppendLine("END;");
            
            return sql.ToString();
        }

        /// <summary>
        /// 是否可以執行測試
        /// </summary>
        /// <returns>是否可以執行</returns>
        private bool CanExecuteTest()
        {
            return !IsLoading && !IsSaving && IsCompilationSuccessful && _getConnection() != null;
        }

        /// <summary>
        /// 清除編譯結果
        /// </summary>
        private void OnClearCompilationResults()
        {
            CompilationErrors.Clear();
            CompilationStatus = "尚未編譯";
            IsCompilationSuccessful = false;
            HasCompilationErrors = false;
            StatusMessage = "編譯結果已清除";
        }

        /// <summary>
        /// 是否可以清除編譯結果
        /// </summary>
        /// <returns>是否可以清除</returns>
        private bool CanClearCompilationResults()
        {
            return CompilationErrors.Count > 0 || IsCompilationSuccessful;
        }

        /// <summary>
        /// 清除測試結果
        /// </summary>
        private void OnClearTestResults()
        {
            ExecutionStatus = "尚未執行";
            ExecutionResult = string.Empty;
            ExecutionErrorMessage = string.Empty;
            HasExecutionResult = false;
            HasExecutionError = false;
            StatusMessage = "測試結果已清除";
        }

        /// <summary>
        /// 是否可以清除測試結果
        /// </summary>
        /// <returns>是否可以清除</returns>
        private bool CanClearTestResults()
        {
            return HasExecutionResult || HasExecutionError;
        }

        /// <summary>
        /// 檢查編譯錯誤
        /// </summary>
        /// <param name="connection">資料庫連線</param>
        /// <returns>非同步工作</returns>
        private async Task CheckCompilationErrorsAsync(IDbConnection connection)
        {
            try
            {
                var sql = $@"
                    SELECT 
                        LINE AS LineNumber,
                        POSITION AS ColumnNumber,
                        TEXT AS Message,
                        ATTRIBUTE AS ErrorCode
                    FROM 
                        ALL_ERRORS
                    WHERE 
                        OWNER = USER
                        AND NAME = '{ObjectName}'
                        AND TYPE = 'FUNCTION'
                    ORDER BY 
                        SEQUENCE";

                var result = await _databaseService.ExecuteSqlAsync(connection, sql);
                
                CompilationErrors.Clear();
                if (result.IsSuccess && result.Data != null)
                {
                    foreach (DataRow row in result.Data.Rows)
                    {
                        CompilationErrors.Add(new CompilationError
                        {
                            LineNumber = row["LineNumber"] != DBNull.Value ? Convert.ToInt32(row["LineNumber"]) : 0,
                            ColumnNumber = row["ColumnNumber"] != DBNull.Value ? Convert.ToInt32(row["ColumnNumber"]) : 0,
                            Message = row["Message"]?.ToString() ?? string.Empty,
                            ErrorCode = row["ErrorCode"]?.ToString() ?? string.Empty
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"檢查編譯錯誤失敗：{ex}");
            }
        }
    }

    /// <summary>
    /// 函數參數模型
    /// </summary>
    public class FunctionParameter : NotifyPropertyChangedBase
    {
        private string _name = string.Empty;
        private string _dataType = string.Empty;
        private string _direction = string.Empty;
        private int? _length;
        private int? _precision;
        private int? _scale;
        private string _defaultValue = string.Empty;
        private string _comments = string.Empty;
        private int _position;
        private bool _isInput;
        private string _value = string.Empty;

        /// <summary>
        /// 參數名稱
        /// </summary>
        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        /// <summary>
        /// 資料類型
        /// </summary>
        public string DataType
        {
            get => _dataType;
            set => SetProperty(ref _dataType, value);
        }

        /// <summary>
        /// 參數方向 (IN, OUT, IN OUT)
        /// </summary>
        public string Direction
        {
            get => _direction;
            set => SetProperty(ref _direction, value);
        }

        /// <summary>
        /// 長度
        /// </summary>
        public int? Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        /// <summary>
        /// 精度
        /// </summary>
        public int? Precision
        {
            get => _precision;
            set => SetProperty(ref _precision, value);
        }

        /// <summary>
        /// 小數位數
        /// </summary>
        public int? Scale
        {
            get => _scale;
            set => SetProperty(ref _scale, value);
        }

        /// <summary>
        /// 預設值
        /// </summary>
        public string DefaultValue
        {
            get => _defaultValue;
            set => SetProperty(ref _defaultValue, value);
        }

        /// <summary>
        /// 備註
        /// </summary>
        public string Comments
        {
            get => _comments;
            set => SetProperty(ref _comments, value);
        }

        /// <summary>
        /// 參數位置
        /// </summary>
        public int Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }

        /// <summary>
        /// 是否為輸入參數
        /// </summary>
        public bool IsInput
        {
            get => _isInput;
            set => SetProperty(ref _isInput, value);
        }

        /// <summary>
        /// 參數值
        /// </summary>
        public string Value
        {
            get => _value;
            set => SetProperty(ref _value, value);
        }
    }




}