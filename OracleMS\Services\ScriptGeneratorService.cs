using System.Data;
using System.Text;
using Microsoft.Extensions.Logging;
using Oracle.ManagedDataAccess.Client;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.Services;

/// <summary>
/// 腳本產生服務，負責產生各種資料庫物件的 SQL 腳本
/// </summary>
public class ScriptGeneratorService : IScriptGeneratorService
{
    private readonly IDatabaseRepository _databaseRepository;
    private readonly ILogger<ScriptGeneratorService> _logger;

    public ScriptGeneratorService(
        IDatabaseRepository databaseRepository,
        ILogger<ScriptGeneratorService> logger)
    {
        _databaseRepository = databaseRepository ?? throw new ArgumentNullException(nameof(databaseRepository));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 產生建立物件的 SQL 腳本
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="objectName">物件名稱</param>
    /// <param name="type">物件類型</param>
    /// <returns>CREATE 腳本</returns>
    public async Task<string> GenerateCreateScriptAsync(IDbConnection connection, string objectName, DatabaseObjectType type)
    {
        try
        {
            _logger.LogInformation("正在產生 CREATE 腳本，物件: {ObjectName}，類型: {ObjectType}", objectName, type);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(objectName))
                throw new ArgumentException("物件名稱不能為空", nameof(objectName));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            var script = type switch
            {
                DatabaseObjectType.Table => await GenerateCreateTableScriptAsync(connection, objectName),
                DatabaseObjectType.View => await GenerateCreateViewScriptAsync(connection, objectName),
                DatabaseObjectType.Procedure => await GenerateCreateProcedureScriptAsync(connection, objectName),
                DatabaseObjectType.Function => await GenerateCreateFunctionScriptAsync(connection, objectName),
                DatabaseObjectType.Package => await GenerateCreatePackageScriptAsync(connection, objectName),
                DatabaseObjectType.Sequence => await GenerateCreateSequenceScriptAsync(connection, objectName),
                DatabaseObjectType.Trigger => await GenerateCreateTriggerScriptAsync(connection, objectName),
                DatabaseObjectType.Index => await GenerateCreateIndexScriptAsync(connection, objectName),
                _ => throw new ArgumentException($"不支援的物件類型: {type}")
            };

            _logger.LogInformation("CREATE 腳本產生成功，物件: {ObjectName}，腳本長度: {ScriptLength}", 
                objectName, script.Length);

            return script;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "產生 CREATE 腳本失敗，物件: {ObjectName}，類型: {ObjectType}", objectName, type);
            throw new OracleManagementException($"產生 CREATE 腳本失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 產生插入資料的 SQL 腳本
    /// </summary>
    /// <param name="connection">資料庫連線</param>
    /// <param name="tableName">資料表名稱</param>
    /// <returns>INSERT 腳本</returns>
    public async Task<string> GenerateInsertScriptAsync(IDbConnection connection, string tableName)
    {
        try
        {
            _logger.LogInformation("正在產生 INSERT 腳本，資料表: {TableName}", tableName);

            if (connection == null)
                throw new ArgumentNullException(nameof(connection));

            if (string.IsNullOrWhiteSpace(tableName))
                throw new ArgumentException("資料表名稱不能為空", nameof(tableName));

            if (connection.State != System.Data.ConnectionState.Open)
                throw new InvalidOperationException("資料庫連線未開啟");

            // 取得資料表結構
            var schema = await _databaseRepository.GetTableSchemaAsync(connection, tableName);
            
            // 取得資料表資料
            var dataTable = await _databaseRepository.ExecuteQueryAsync(connection, $"SELECT * FROM {tableName}");

            var script = GenerateInsertStatements(tableName, schema.Columns, dataTable);

            _logger.LogInformation("INSERT 腳本產生成功，資料表: {TableName}，資料筆數: {RowCount}，腳本長度: {ScriptLength}", 
                tableName, dataTable.Rows.Count, script.Length);

            return script;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "產生 INSERT 腳本失敗，資料表: {TableName}", tableName);
            throw new OracleManagementException($"產生 INSERT 腳本失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 產生刪除物件的 SQL 腳本
    /// </summary>
    /// <param name="objectName">物件名稱</param>
    /// <param name="type">物件類型</param>
    /// <returns>DROP 腳本</returns>
    public async Task<string> GenerateDropScriptAsync(string objectName, DatabaseObjectType type)
    {
        try
        {
            _logger.LogInformation("正在產生 DROP 腳本，物件: {ObjectName}，類型: {ObjectType}", objectName, type);

            if (string.IsNullOrWhiteSpace(objectName))
                throw new ArgumentException("物件名稱不能為空", nameof(objectName));

            var script = type switch
            {
                DatabaseObjectType.Table => GenerateDropTableScript(objectName),
                DatabaseObjectType.View => GenerateDropViewScript(objectName),
                DatabaseObjectType.Procedure => GenerateDropProcedureScript(objectName),
                DatabaseObjectType.Function => GenerateDropFunctionScript(objectName),
                DatabaseObjectType.Package => GenerateDropPackageScript(objectName),
                DatabaseObjectType.Sequence => GenerateDropSequenceScript(objectName),
                DatabaseObjectType.Trigger => GenerateDropTriggerScript(objectName),
                DatabaseObjectType.Index => GenerateDropIndexScript(objectName),
                _ => throw new ArgumentException($"不支援的物件類型: {type}")
            };

            _logger.LogInformation("DROP 腳本產生成功，物件: {ObjectName}", objectName);

            return await Task.FromResult(script);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "產生 DROP 腳本失敗，物件: {ObjectName}，類型: {ObjectType}", objectName, type);
            throw new OracleManagementException($"產生 DROP 腳本失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 產生建立資料表的腳本
    /// </summary>
    private async Task<string> GenerateCreateTableScriptAsync(IDbConnection connection, string tableName)
    {
        var schema = await _databaseRepository.GetTableSchemaAsync(connection, tableName);
        var script = new StringBuilder();

        script.AppendLine($"-- 建立資料表: {tableName}");
        script.AppendLine($"CREATE TABLE {tableName} (");

        // 產生欄位定義
        var columnDefinitions = new List<string>();
        foreach (var column in schema.Columns)
        {
            var columnDef = GenerateColumnDefinition(column);
            columnDefinitions.Add($"    {columnDef}");
        }

        script.AppendLine(string.Join(",\n", columnDefinitions));

        // 產生主鍵約束
        var primaryKeyColumns = schema.Columns.Where(c => c.IsPrimaryKey).ToList();
        if (primaryKeyColumns.Any())
        {
            var pkColumns = string.Join(", ", primaryKeyColumns.Select(c => c.ColumnName));
            script.AppendLine($",    CONSTRAINT PK_{tableName} PRIMARY KEY ({pkColumns})");
        }

        script.AppendLine(");");

        // 產生索引
        foreach (var index in schema.Indexes.Where(i => !i.IndexName.StartsWith("PK_")))
        {
            script.AppendLine();
            script.AppendLine(GenerateCreateIndexScript(index, tableName));
        }

        // 產生外鍵約束
        foreach (var constraint in schema.Constraints.Where(c => c.ConstraintType == "Foreign Key"))
        {
            script.AppendLine();
            script.AppendLine(GenerateAlterTableAddConstraintScript(tableName, constraint));
        }

        return script.ToString();
    }

    /// <summary>
    /// 產生建立檢視的腳本
    /// </summary>
    private async Task<string> GenerateCreateViewScriptAsync(IDbConnection connection, string viewName)
    {
        const string sql = @"
            SELECT text 
            FROM all_views 
            WHERE view_name = :viewName 
            AND owner = USER";

        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("viewName", viewName.ToUpper()));
        }

        var viewText = await Task.Run(() => command.ExecuteScalar()?.ToString());
        
        if (string.IsNullOrEmpty(viewText))
            throw new OracleManagementException($"找不到檢視定義: {viewName}");

        return $"-- 建立檢視: {viewName}\nCREATE OR REPLACE VIEW {viewName} AS\n{viewText};";
    }

    /// <summary>
    /// 產生建立預存程序的腳本
    /// </summary>
    private async Task<string> GenerateCreateProcedureScriptAsync(IDbConnection connection, string procedureName)
    {
        return await GetObjectSourceAsync(connection, procedureName, "PROCEDURE");
    }

    /// <summary>
    /// 產生建立函數的腳本
    /// </summary>
    private async Task<string> GenerateCreateFunctionScriptAsync(IDbConnection connection, string functionName)
    {
        return await GetObjectSourceAsync(connection, functionName, "FUNCTION");
    }

    /// <summary>
    /// 產生建立套件的腳本
    /// </summary>
    private async Task<string> GenerateCreatePackageScriptAsync(IDbConnection connection, string packageName)
    {
        var packageSpec = await GetObjectSourceAsync(connection, packageName, "PACKAGE");
        var packageBody = await GetObjectSourceAsync(connection, packageName, "PACKAGE BODY");
        
        var script = new StringBuilder();
        script.AppendLine($"-- 建立套件規格: {packageName}");
        script.AppendLine(packageSpec);
        
        if (!string.IsNullOrEmpty(packageBody))
        {
            script.AppendLine();
            script.AppendLine($"-- 建立套件主體: {packageName}");
            script.AppendLine(packageBody);
        }
        
        return script.ToString();
    }

    /// <summary>
    /// 產生建立序列的腳本
    /// </summary>
    private async Task<string> GenerateCreateSequenceScriptAsync(IDbConnection connection, string sequenceName)
    {
        const string sql = @"
            SELECT 
                min_value, max_value, increment_by, cycle_flag, 
                order_flag, cache_size, last_number
            FROM all_sequences 
            WHERE sequence_name = :sequenceName 
            AND sequence_owner = USER";

        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("sequenceName", sequenceName.ToUpper()));
        }

        var dataTable = await _databaseRepository.ExecuteQueryAsync(connection, sql);
        
        if (dataTable.Rows.Count == 0)
            throw new OracleManagementException($"找不到序列定義: {sequenceName}");

        var row = dataTable.Rows[0];
        var script = new StringBuilder();
        
        script.AppendLine($"-- 建立序列: {sequenceName}");
        script.Append($"CREATE SEQUENCE {sequenceName}");
        script.Append($" START WITH {row["last_number"]}");
        script.Append($" INCREMENT BY {row["increment_by"]}");
        script.Append($" MINVALUE {row["min_value"]}");
        script.Append($" MAXVALUE {row["max_value"]}");
        script.Append($" CACHE {row["cache_size"]}");
        
        if (row["cycle_flag"].ToString() == "Y")
            script.Append(" CYCLE");
        else
            script.Append(" NOCYCLE");
            
        if (row["order_flag"].ToString() == "Y")
            script.Append(" ORDER");
        else
            script.Append(" NOORDER");
            
        script.AppendLine(";");
        
        return script.ToString();
    }

    /// <summary>
    /// 產生建立觸發器的腳本
    /// </summary>
    private async Task<string> GenerateCreateTriggerScriptAsync(IDbConnection connection, string triggerName)
    {
        return await GetObjectSourceAsync(connection, triggerName, "TRIGGER");
    }

    /// <summary>
    /// 產生建立索引的腳本
    /// </summary>
    private async Task<string> GenerateCreateIndexScriptAsync(IDbConnection connection, string indexName)
    {
        const string sql = @"
            SELECT 
                i.index_name, i.table_name, i.index_type, i.uniqueness,
                LISTAGG(ic.column_name, ', ') WITHIN GROUP (ORDER BY ic.column_position) as columns
            FROM all_indexes i
            JOIN all_ind_columns ic ON i.index_name = ic.index_name AND i.owner = ic.index_owner
            WHERE i.index_name = :indexName
            AND i.owner = USER
            GROUP BY i.index_name, i.table_name, i.index_type, i.uniqueness";

        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("indexName", indexName.ToUpper()));
        }

        var dataTable = await _databaseRepository.ExecuteQueryAsync(connection, sql);
        
        if (dataTable.Rows.Count == 0)
            throw new OracleManagementException($"找不到索引定義: {indexName}");

        var row = dataTable.Rows[0];
        var isUnique = row["uniqueness"].ToString() == "UNIQUE";
        var tableName = row["table_name"].ToString();
        var columns = row["columns"].ToString();
        
        var script = $"-- 建立索引: {indexName}\n";
        script += $"CREATE {(isUnique ? "UNIQUE " : "")}INDEX {indexName} ON {tableName} ({columns});";
        
        return script;
    }

    /// <summary>
    /// 產生 INSERT 語句
    /// </summary>
    private static string GenerateInsertStatements(string tableName, List<ColumnInfo> columns, DataTable dataTable)
    {
        var script = new StringBuilder();
        script.AppendLine($"-- 插入資料到資料表: {tableName}");
        
        if (dataTable.Rows.Count == 0)
        {
            script.AppendLine("-- 資料表無資料");
            return script.ToString();
        }

        var columnNames = string.Join(", ", columns.Select(c => c.ColumnName));
        
        foreach (DataRow row in dataTable.Rows)
        {
            var values = new List<string>();
            
            for (int i = 0; i < columns.Count; i++)
            {
                var column = columns[i];
                var value = row[i];
                
                if (value == null || value == DBNull.Value)
                {
                    values.Add("NULL");
                }
                else
                {
                    values.Add(FormatValueForInsert(value, column.DataType));
                }
            }
            
            script.AppendLine($"INSERT INTO {tableName} ({columnNames}) VALUES ({string.Join(", ", values)});");
        }
        
        return script.ToString();
    }

    /// <summary>
    /// 產生欄位定義
    /// </summary>
    private static string GenerateColumnDefinition(ColumnInfo column)
    {
        var definition = new StringBuilder();
        definition.Append($"{column.ColumnName} {column.DataType}");
        
        // 加入長度或精確度
        if (column.MaxLength.HasValue && column.DataType.ToUpper().Contains("CHAR"))
        {
            definition.Append($"({column.MaxLength})");
        }
        else if (column.Precision.HasValue && column.Scale.HasValue)
        {
            definition.Append($"({column.Precision},{column.Scale})");
        }
        else if (column.Precision.HasValue)
        {
            definition.Append($"({column.Precision})");
        }
        
        // 加入 NULL 約束
        if (!column.IsNullable)
        {
            definition.Append(" NOT NULL");
        }
        
        // 加入預設值
        if (!string.IsNullOrEmpty(column.DefaultValue))
        {
            definition.Append($" DEFAULT {column.DefaultValue}");
        }
        
        return definition.ToString();
    }

    /// <summary>
    /// 產生建立索引的腳本
    /// </summary>
    private static string GenerateCreateIndexScript(IndexInfo index, string tableName)
    {
        var uniqueKeyword = index.IsUnique ? "UNIQUE " : "";
        var columns = string.Join(", ", index.Columns);
        return $"CREATE {uniqueKeyword}INDEX {index.IndexName} ON {tableName} ({columns});";
    }

    /// <summary>
    /// 產生 ALTER TABLE ADD CONSTRAINT 腳本
    /// </summary>
    private static string GenerateAlterTableAddConstraintScript(string tableName, ConstraintInfo constraint)
    {
        return constraint.ConstraintType switch
        {
            "Foreign Key" => $"ALTER TABLE {tableName} ADD CONSTRAINT {constraint.ConstraintName} " +
                           $"FOREIGN KEY ({constraint.Columns}) REFERENCES {constraint.ReferencedTable} ({constraint.ReferencedColumns});",
            "Unique" => $"ALTER TABLE {tableName} ADD CONSTRAINT {constraint.ConstraintName} " +
                       $"UNIQUE ({constraint.Columns});",
            "Check" => $"ALTER TABLE {tableName} ADD CONSTRAINT {constraint.ConstraintName} " +
                      $"CHECK ({constraint.Columns});",
            _ => $"-- 不支援的約束類型: {constraint.ConstraintType}"
        };
    }

    /// <summary>
    /// 取得物件原始碼
    /// </summary>
    private async Task<string> GetObjectSourceAsync(IDbConnection connection, string objectName, string objectType)
    {
        const string sql = @"
            SELECT text 
            FROM all_source 
            WHERE name = :objectName 
            AND type = :objectType 
            AND owner = USER 
            ORDER BY line";

        using var command = connection.CreateCommand();
        command.CommandText = sql;
        
        if (command is OracleCommand oracleCommand)
        {
            oracleCommand.Parameters.Add(new OracleParameter("objectName", objectName.ToUpper()));
            oracleCommand.Parameters.Add(new OracleParameter("objectType", objectType.ToUpper()));
        }

        var dataTable = await _databaseRepository.ExecuteQueryAsync(connection, sql);
        
        if (dataTable.Rows.Count == 0)
            throw new OracleManagementException($"找不到物件原始碼: {objectName} ({objectType})");

        var script = new StringBuilder();
        script.AppendLine($"-- 建立{objectType}: {objectName}");
        
        foreach (DataRow row in dataTable.Rows)
        {
            script.Append(row["text"].ToString());
        }
        
        return script.ToString();
    }

    /// <summary>
    /// 格式化值用於 INSERT 語句
    /// </summary>
    private static string FormatValueForInsert(object value, string dataType)
    {
        if (value == null || value == DBNull.Value)
            return "NULL";

        var upperDataType = dataType.ToUpper();
        
        if (upperDataType.Contains("CHAR") || upperDataType.Contains("CLOB"))
        {
            return $"'{value.ToString()?.Replace("'", "''")}'";
        }
        else if (upperDataType.Contains("DATE") || upperDataType.Contains("TIMESTAMP"))
        {
            if (value is DateTime dateTime)
            {
                return $"TO_DATE('{dateTime:yyyy-MM-dd HH:mm:ss}', 'YYYY-MM-DD HH24:MI:SS')";
            }
            return $"'{value}'";
        }
        else
        {
            return value.ToString() ?? "NULL";
        }
    }

    /// <summary>
    /// 產生 DROP TABLE 腳本
    /// </summary>
    private static string GenerateDropTableScript(string tableName)
    {
        return $"-- 刪除資料表: {tableName}\nDROP TABLE {tableName} CASCADE CONSTRAINTS;";
    }

    /// <summary>
    /// 產生 DROP VIEW 腳本
    /// </summary>
    private static string GenerateDropViewScript(string viewName)
    {
        return $"-- 刪除檢視: {viewName}\nDROP VIEW {viewName};";
    }

    /// <summary>
    /// 產生 DROP PROCEDURE 腳本
    /// </summary>
    private static string GenerateDropProcedureScript(string procedureName)
    {
        return $"-- 刪除預存程序: {procedureName}\nDROP PROCEDURE {procedureName};";
    }

    /// <summary>
    /// 產生 DROP FUNCTION 腳本
    /// </summary>
    private static string GenerateDropFunctionScript(string functionName)
    {
        return $"-- 刪除函數: {functionName}\nDROP FUNCTION {functionName};";
    }

    /// <summary>
    /// 產生 DROP PACKAGE 腳本
    /// </summary>
    private static string GenerateDropPackageScript(string packageName)
    {
        return $"-- 刪除套件: {packageName}\nDROP PACKAGE {packageName};";
    }

    /// <summary>
    /// 產生 DROP SEQUENCE 腳本
    /// </summary>
    private static string GenerateDropSequenceScript(string sequenceName)
    {
        return $"-- 刪除序列: {sequenceName}\nDROP SEQUENCE {sequenceName};";
    }

    /// <summary>
    /// 產生 DROP TRIGGER 腳本
    /// </summary>
    private static string GenerateDropTriggerScript(string triggerName)
    {
        return $"-- 刪除觸發器: {triggerName}\nDROP TRIGGER {triggerName};";
    }

    /// <summary>
    /// 產生 DROP INDEX 腳本
    /// </summary>
    private static string GenerateDropIndexScript(string indexName)
    {
        return $"-- 刪除索引: {indexName}\nDROP INDEX {indexName};";
    }
}