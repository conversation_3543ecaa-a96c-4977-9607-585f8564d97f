using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Media.Animation;
using System.Windows.Threading;

namespace OracleMS.Views
{
    /// <summary>
    /// 連線進度對話框
    /// </summary>
    public partial class ConnectionProgressDialog : Window
    {
        private CancellationTokenSource? _cancellationTokenSource;
        private bool _isCompleted = false;
        private Storyboard? _waveAnimation;
        private DispatcherTimer? _timeoutTimer;

        public ConnectionProgressDialog()
        {
            InitializeComponent();
            Loaded += OnLoaded;
        }

        /// <summary>
        /// 取消令牌，用於取消連線操作
        /// </summary>
        public CancellationToken CancellationToken => _cancellationTokenSource?.Token ?? CancellationToken.None;

        /// <summary>
        /// 連線是否被取消
        /// </summary>
        public bool IsCancelled { get; private set; }

        /// <summary>
        /// 設定連線資訊
        /// </summary>
        /// <param name="connectionName">連線名稱</param>
        /// <param name="serverInfo">伺服器資訊（可選）</param>
        public void SetConnectionInfo(string connectionName, string? serverInfo = null)
        {
            ConnectionNameText.Text = connectionName;
            
            if (!string.IsNullOrEmpty(serverInfo))
            {
                StatusText.Text = $"正在連線到 {serverInfo}...";
            }
        }

        /// <summary>
        /// 更新狀態文字
        /// </summary>
        /// <param name="status">狀態文字</param>
        public void UpdateStatus(string status)
        {
            if (!_isCompleted)
            {
                StatusText.Text = status;
            }
        }

        /// <summary>
        /// 顯示取消按鈕（在連線超過一定時間後）
        /// </summary>
        public void ShowCancelButton()
        {
            CancelButton.Visibility = Visibility.Visible;
            CloseButton.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 隱藏取消按鈕
        /// </summary>
        public void HideCancelButton()
        {
            CancelButton.Visibility = Visibility.Collapsed;
            CloseButton.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 設定進度條為確定模式（目前使用動畫效果，此方法保留相容性）
        /// </summary>
        /// <param name="value">進度值 (0-100)</param>
        public void SetProgress(int value)
        {
            // 由於我們使用自訂動畫，這裡可以根據需要實作確定進度的視覺效果
            // 目前保持動畫效果
        }

        /// <summary>
        /// 設定進度條為不確定模式（目前使用動畫效果，此方法保留相容性）
        /// </summary>
        public void SetIndeterminateProgress()
        {
            // 由於我們使用自訂動畫，這裡可以根據需要控制動畫
            // 目前保持動畫效果
        }

        /// <summary>
        /// 設定進度條動畫風格（目前使用 EventTrigger 自動觸發，此方法保留相容性）
        /// </summary>
        /// <param name="animationStyle">動畫風格</param>
        public void SetProgressAnimationStyle(string animationStyle = "sliding")
        {
            // 新的進度條動畫通過 EventTrigger 自動觸發，無需手動控制
        }

        /// <summary>
        /// 連線成功，關閉對話框
        /// </summary>
        public void ConnectionSucceeded()
        {
            if (_isCompleted) return;
            
            _isCompleted = true;
            StatusText.Text = "連線成功！";
            
            // 停止動畫
            StopAnimations();
            
            // 短暫顯示成功訊息後關閉
            var timer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(500) };
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                CloseWithAnimation();
            };
            timer.Start();
        }

        /// <summary>
        /// 連線失敗，關閉對話框
        /// </summary>
        /// <param name="errorMessage">錯誤訊息</param>
        public void ConnectionFailed(string errorMessage)
        {
            if (_isCompleted) return;
            
            _isCompleted = true;
            StatusText.Text = $"連線失敗: {errorMessage}";
            
            // 停止動畫
            StopAnimations();
            
            // 顯示錯誤訊息一段時間後關閉
            var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(2) };
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                CloseWithAnimation();
            };
            timer.Start();
        }

        /// <summary>
        /// 開始連線操作，設定超時和取消令牌
        /// </summary>
        /// <param name="timeoutSeconds">超時秒數，預設30秒</param>
        public void StartConnection(int timeoutSeconds = 30)
        {
            _cancellationTokenSource = new CancellationTokenSource();
            
            // 設定超時計時器，超時後顯示取消按鈕
            _timeoutTimer = new DispatcherTimer 
            { 
                Interval = TimeSpan.FromSeconds(Math.Max(5, timeoutSeconds / 3)) 
            };
            _timeoutTimer.Tick += (s, e) =>
            {
                if (!_isCompleted)
                {
                    ShowCancelButton();
                    UpdateStatus("連線時間較長，您可以選擇取消...");
                }
                _timeoutTimer?.Stop();
            };
            _timeoutTimer.Start();
        }

        private void OnLoaded(object sender, RoutedEventArgs e)
        {
            // 開始淡入動畫
            var fadeInStoryboard = (Storyboard)Resources["FadeInAnimation"];
            fadeInStoryboard.Begin(this);
            
            // 開始波浪動畫
            _waveAnimation = (Storyboard)Resources["WaveAnimation"];
            _waveAnimation.Begin(this);
            
            // 進度條動畫通過 EventTrigger 自動觸發，無需手動啟動
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            CancelConnection();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            CancelConnection();
        }

        private void CancelConnection()
        {
            if (_isCompleted) return;
            
            _isCompleted = true;
            IsCancelled = true;
            
            // 取消連線操作
            _cancellationTokenSource?.Cancel();
            
            StatusText.Text = "正在取消連線...";
            StopAnimations();
            
            // 短暫顯示取消訊息後關閉
            var timer = new DispatcherTimer { Interval = TimeSpan.FromMilliseconds(300) };
            timer.Tick += (s, e) =>
            {
                timer.Stop();
                CloseWithAnimation();
            };
            timer.Start();
        }

        private void StopAnimations()
        {
            _waveAnimation?.Stop(this);
            _timeoutTimer?.Stop();
        }

        private void CloseWithAnimation()
        {
            var fadeOutStoryboard = (Storyboard)Resources["FadeOutAnimation"];
            fadeOutStoryboard.Begin(this);
        }

        private void FadeOutAnimation_Completed(object sender, EventArgs e)
        {
            DialogResult = !IsCancelled;
            Close();
        }

        protected override void OnClosed(EventArgs e)
        {
            // 清理資源
            StopAnimations();
            _cancellationTokenSource?.Dispose();
            _timeoutTimer?.Stop();
            
            base.OnClosed(e);
        }

        /// <summary>
        /// 靜態方法：顯示連線進度對話框並執行連線操作
        /// </summary>
        /// <param name="owner">父視窗</param>
        /// <param name="connectionName">連線名稱</param>
        /// <param name="connectionTask">連線任務</param>
        /// <param name="serverInfo">伺服器資訊（可選）</param>
        /// <param name="timeoutSeconds">超時秒數</param>
        /// <returns>連線結果</returns>
        public static async Task<T?> ShowConnectionDialog<T>(
            Window owner,
            string connectionName,
            Func<CancellationToken, Task<T>> connectionTask,
            string? serverInfo = null,
            int timeoutSeconds = 30) where T : class
        {
            var dialog = new ConnectionProgressDialog
            {
                Owner = owner
            };
            
            dialog.SetConnectionInfo(connectionName, serverInfo);
            dialog.StartConnection(timeoutSeconds);
            
            T? result = null;
            Exception? exception = null;
            
            // 在背景執行連線任務
            var connectionWork = Task.Run(async () =>
            {
                try
                {
                    result = await connectionTask(dialog.CancellationToken);
                    
                    // 在UI執行緒上更新對話框
                    await dialog.Dispatcher.InvokeAsync(() =>
                    {
                        dialog.ConnectionSucceeded();
                    });
                }
                catch (OperationCanceledException)
                {
                    // 連線被取消，不需要特別處理
                }
                catch (Exception ex)
                {
                    exception = ex;
                    
                    // 在UI執行緒上更新對話框
                    await dialog.Dispatcher.InvokeAsync(() =>
                    {
                        dialog.ConnectionFailed(ex.Message);
                    });
                }
            });
            
            // 顯示對話框
            var dialogResult = dialog.ShowDialog();
            
            // 等待連線任務完成
            await connectionWork;
            
            // 如果有例外且不是取消操作，重新拋出
            if (exception != null && !dialog.IsCancelled)
            {
                throw exception;
            }
            
            return dialog.IsCancelled ? null : result;
        }
    }
}