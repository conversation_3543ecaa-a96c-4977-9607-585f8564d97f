using System.Collections.ObjectModel;
using System.Data;
using System.Windows.Input;
using CommunityToolkit.Mvvm.Input;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.ViewModels;

public class ObjectExplorerViewModel : ViewModelBase
{
    private readonly IDatabaseService _databaseService;
    private readonly IScriptGeneratorService _scriptGeneratorService;
    private readonly IObjectEditorService _objectEditorService;
    private IDbConnection? _connection;

    public ObservableCollection<DatabaseObjectNode> RootNodes { get; } = new();
    
    private DatabaseObjectNode? _selectedNode;
    public DatabaseObjectNode? SelectedNode
    {
        get => _selectedNode;
        set => SetProperty(ref _selectedNode, value);
    }

    private string _searchText = string.Empty;
    public string SearchText
    {
        get => _searchText;
        set
        {
            SetProperty(ref _searchText, value);
            FilterNodes();
        }
    }

    private bool _isLoading;
    public bool IsLoading
    {
        get => _isLoading;
        set => SetProperty(ref _isLoading, value);
    }

    private string _statusMessage = string.Empty;
    public string StatusMessage
    {
        get => _statusMessage;
        set => SetProperty(ref _statusMessage, value);
    }

    public ICommand RefreshCommand { get; }
    public ICommand ExpandNodeCommand { get; }
    public ICommand OpenTableDataCommand { get; }
    public ICommand OpenTableDesignCommand { get; }
    public ICommand OpenViewEditorCommand { get; }
    public ICommand OpenProcedureEditorCommand { get; }
    public ICommand OpenFunctionEditorCommand { get; }
    public ICommand OpenPackageEditorCommand { get; }
    public ICommand OpenSequenceEditorCommand { get; }
    public ICommand OpenTriggerEditorCommand { get; }
    public ICommand OpenIndexEditorCommand { get; }
    public ICommand GenerateCreateScriptCommand { get; }
    public ICommand GenerateInsertScriptCommand { get; }
    public ICommand GenerateDropScriptCommand { get; }
    public ICommand CopyObjectNameCommand { get; }
    public ICommand ExpandAllCommand { get; }
    public ICommand CollapseAllCommand { get; }
    public ICommand ClearSearchCommand { get; }

    public event EventHandler<OpenTableDataEventArgs>? OpenTableDataRequested;
    public event EventHandler<OpenTableDesignEventArgs>? OpenTableDesignRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenViewEditorRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenProcedureEditorRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenFunctionEditorRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenPackageEditorRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenSequenceEditorRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenTriggerEditorRequested;
    public event EventHandler<OpenObjectEditorEventArgs>? OpenIndexEditorRequested;
    public event EventHandler<GenerateScriptEventArgs>? GenerateScriptRequested;

    public ObjectExplorerViewModel(IDatabaseService databaseService, IScriptGeneratorService scriptGeneratorService, IObjectEditorService objectEditorService)
    {
        _databaseService = databaseService;
        _scriptGeneratorService = scriptGeneratorService;
        _objectEditorService = objectEditorService;

        RefreshCommand = new AsyncRelayCommand(OnRefreshAsync);
        ExpandNodeCommand = new AsyncRelayCommand<DatabaseObjectNode>(OnExpandNodeAsync);
        OpenTableDataCommand = new RelayCommand<DatabaseObjectNode>(OnOpenTableData);
        OpenTableDesignCommand = new RelayCommand<DatabaseObjectNode>(OnOpenTableDesign);
        OpenViewEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenViewEditor);
        OpenProcedureEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenProcedureEditor);
        OpenFunctionEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenFunctionEditor);
        OpenPackageEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenPackageEditor);
        OpenSequenceEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenSequenceEditor);
        OpenTriggerEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenTriggerEditor);
        OpenIndexEditorCommand = new RelayCommand<DatabaseObjectNode>(OnOpenIndexEditor);
        GenerateCreateScriptCommand = new AsyncRelayCommand<DatabaseObjectNode>(OnGenerateCreateScriptAsync);
        GenerateInsertScriptCommand = new AsyncRelayCommand<DatabaseObjectNode>(OnGenerateInsertScriptAsync);
        GenerateDropScriptCommand = new AsyncRelayCommand<DatabaseObjectNode>(OnGenerateDropScriptAsync);
        CopyObjectNameCommand = new RelayCommand<DatabaseObjectNode>(OnCopyObjectName);
        ExpandAllCommand = new RelayCommand(OnExpandAll);
        CollapseAllCommand = new RelayCommand(OnCollapseAll);
        ClearSearchCommand = new RelayCommand(OnClearSearch);

        InitializeRootNodes();
    }

    public void SetConnection(IDbConnection? connection)
    {
        _connection = connection;

        // 確保在 UI 執行緒中執行 ObservableCollection 的操作
        System.Windows.Application.Current.Dispatcher.Invoke(() =>
        {
            if (connection == null)
            {
                // Clear all loaded objects when connection is null
                ClearAllObjects();
                StatusMessage = "未連線到資料庫";
            }
            else
            {
                // Don't load objects immediately - use load on demand
                // Just clear existing objects and update status
                ClearAllObjects();
                StatusMessage = "已連線到資料庫 - 展開節點以載入物件";
            }
        });
    }

    private void ClearAllObjects()
    {
        foreach (var rootNode in RootNodes)
        {
            rootNode.Children.Clear();
            // 重新添加虛擬項目讓 TreeView 顯示展開圖示
            rootNode.Children.Add(new DatabaseObjectNode("載入中...", rootNode.ObjectType, isFolder: false)
            {
                IsPlaceholder = true
            });
            rootNode.IsLoaded = false;
            rootNode.IsExpanded = false;
        }
    }

    private void InitializeRootNodes()
    {
        RootNodes.Clear();

        // 為每個根節點添加虛擬子項目，讓 TreeView 顯示展開圖示
        var rootNodeTypes = new[]
        {
            ("Tables", DatabaseObjectType.Table),
            ("Views", DatabaseObjectType.View),
            ("Procedures", DatabaseObjectType.Procedure),
            ("Functions", DatabaseObjectType.Function),
            ("Packages", DatabaseObjectType.Package),
            ("Sequences", DatabaseObjectType.Sequence),
            ("Triggers", DatabaseObjectType.Trigger),
            ("Indexes", DatabaseObjectType.Index)
        };

        foreach (var (displayName, objectType) in rootNodeTypes)
        {
            var rootNode = new DatabaseObjectNode(displayName, objectType, isFolder: true);

            // 添加虛擬子項目讓 TreeView 顯示展開圖示
            // 這個虛擬項目會在實際展開時被真實資料替換
            rootNode.Children.Add(new DatabaseObjectNode("載入中...", objectType, isFolder: false)
            {
                IsPlaceholder = true
            });

            RootNodes.Add(rootNode);
        }
    }

    private async Task LoadDatabaseObjectsAsync()
    {
        if (_connection == null) 
        {
            StatusMessage = "未連線到資料庫";
            return;
        }

        IsLoading = true;
        StatusMessage = "載入資料庫物件中...";

        try
        {
            // Check connection state before loading
            if (_connection.State != System.Data.ConnectionState.Open)
            {
                StatusMessage = "資料庫連線未開啟";
                return;
            }

            var totalObjects = 0;
            var loadedCategories = 0;

            foreach (var rootNode in RootNodes)
            {
                try
                {
                    var objects = await _databaseService.GetDatabaseObjectsAsync(_connection, rootNode.ObjectType);
                    var objectList = objects.ToList();
                    
                    rootNode.Children.Clear();
                    
                    foreach (var obj in objectList)
                    {
                        var childNode = new DatabaseObjectNode(obj.Name, obj.Type, isFolder: false)
                        {
                            DatabaseObject = obj,
                            Parent = rootNode
                        };
                        rootNode.Children.Add(childNode);
                    }

                    rootNode.IsLoaded = true;
                    totalObjects += objectList.Count;
                    loadedCategories++;
                    
                    // Update status with progress
                    StatusMessage = $"載入中... {loadedCategories}/{RootNodes.Count} 類別完成";
                }
                catch (Exception ex)
                {
                    // Log individual category errors but continue loading others
                    System.Diagnostics.Debug.WriteLine($"載入 {rootNode.DisplayName} 失敗: {ex.Message}");
                    rootNode.Children.Clear();
                    rootNode.IsLoaded = false;
                }
            }

            StatusMessage = totalObjects > 0 
                ? $"已載入 {totalObjects} 個資料庫物件 ({loadedCategories}/{RootNodes.Count} 類別)"
                : "未找到資料庫物件";
        }
        catch (Exception ex)
        {
            StatusMessage = $"載入資料庫物件失敗: {ex.Message}";
            System.Diagnostics.Debug.WriteLine($"LoadDatabaseObjectsAsync error: {ex}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task OnRefreshAsync()
    {
        if (_connection == null)
        {
            StatusMessage = "未連線到資料庫";
            return;
        }

        IsLoading = true;
        StatusMessage = "重新整理中...";

        try
        {
            // 找出所有已經載入過的根節點
            var loadedNodes = RootNodes.Where(node => node.IsLoaded && !HasOnlyPlaceholder(node)).ToList();

            if (loadedNodes.Any())
            {
                var refreshedCount = 0;

                foreach (var node in loadedNodes)
                {
                    try
                    {
                        // 重新載入已展開過的節點
                        var objects = await _databaseService.GetDatabaseObjectsAsync(_connection, node.ObjectType);

                        // 保存展開狀態
                        var wasExpanded = node.IsExpanded;

                        // 清除並重新載入
                        node.Children.Clear();

                        if (objects.Any())
                        {
                            foreach (var obj in objects)
                            {
                                var childNode = new DatabaseObjectNode(obj.Name, obj.Type, isFolder: false)
                                {
                                    DatabaseObject = obj,
                                    Parent = node
                                };
                                node.Children.Add(childNode);
                            }
                        }
                        else
                        {
                            // 如果沒有物件，添加一個提示項目
                            node.Children.Add(new DatabaseObjectNode($"沒有 {node.DisplayName}", node.ObjectType, isFolder: false)
                            {
                                IsPlaceholder = true
                            });
                        }

                        // 恢復展開狀態
                        node.IsExpanded = wasExpanded;
                        refreshedCount++;
                    }
                    catch (Exception ex)
                    {
                        // 個別節點載入失敗，繼續處理其他節點
                        System.Diagnostics.Debug.WriteLine($"重新整理 {node.DisplayName} 失敗: {ex.Message}");
                    }
                }

                StatusMessage = $"已重新整理 {refreshedCount} 個物件類型";
            }
            else
            {
                // 沒有已載入的節點，重置所有節點為初始狀態
                foreach (var node in RootNodes)
                {
                    node.Children.Clear();
                    node.Children.Add(new DatabaseObjectNode("載入中...", node.ObjectType, isFolder: false)
                    {
                        IsPlaceholder = true
                    });
                    node.IsLoaded = false;
                    node.IsExpanded = false;
                }
                StatusMessage = "已重新整理 - 展開節點以載入物件";
            }
        }
        catch (Exception ex)
        {
            StatusMessage = $"重新整理失敗: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task OnExpandNodeAsync(DatabaseObjectNode? node)
    {
        System.Diagnostics.Debug.WriteLine($"OnExpandNodeAsync called for node: {node?.DisplayName}");

        if (node == null || !node.IsFolder)
        {
            System.Diagnostics.Debug.WriteLine($"Node is null or not folder: {node?.DisplayName}");
            return;
        }

        // 如果已經載入且不是只有虛擬項目，則不需要重新載入
        if (node.IsLoaded && !HasOnlyPlaceholder(node))
        {
            System.Diagnostics.Debug.WriteLine($"Node already loaded: {node.DisplayName}");
            return;
        }

        if (_connection == null)
        {
            StatusMessage = "未連線到資料庫";
            System.Diagnostics.Debug.WriteLine("Connection is null");
            return;
        }

        System.Diagnostics.Debug.WriteLine($"Starting to load {node.DisplayName}...");
        IsLoading = true;
        StatusMessage = $"載入 {node.DisplayName} 中...";

        try
        {
            var objects = await _databaseService.GetDatabaseObjectsAsync(_connection, node.ObjectType);

            // 清除所有子項目（包括虛擬項目）
            node.Children.Clear();

            if (objects.Any())
            {
                foreach (var obj in objects)
                {
                    var childNode = new DatabaseObjectNode(obj.Name, obj.Type, isFolder: false)
                    {
                        DatabaseObject = obj,
                        Parent = node
                    };
                    node.Children.Add(childNode);
                }
                StatusMessage = $"已載入 {node.Children.Count} 個 {node.DisplayName}";
            }
            else
            {
                // 如果沒有物件，添加一個提示項目
                node.Children.Add(new DatabaseObjectNode($"沒有 {node.DisplayName}", node.ObjectType, isFolder: false)
                {
                    IsPlaceholder = true
                });
                StatusMessage = $"沒有找到 {node.DisplayName}";
            }

            node.IsLoaded = true;
            node.IsExpanded = true;
        }
        catch (Exception ex)
        {
            StatusMessage = $"展開節點失敗: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    /// <summary>
    /// 檢查節點是否只有虛擬項目
    /// </summary>
    private bool HasOnlyPlaceholder(DatabaseObjectNode node)
    {
        return node.Children.Count == 1 && node.Children[0].IsPlaceholder;
    }

    private void OnOpenTableData(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Table) return;

        OpenTableDataRequested?.Invoke(this, new OpenTableDataEventArgs(node.DatabaseObject.Name));
        StatusMessage = $"開啟資料表資料: {node.DatabaseObject.Name}";
    }

    private void OnOpenTableDesign(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Table) return;

        OpenTableDesignRequested?.Invoke(this, new OpenTableDesignEventArgs(node.DatabaseObject.Name));
        StatusMessage = $"開啟資料表設計: {node.DatabaseObject.Name}";
    }

    private async Task OnGenerateCreateScriptAsync(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || _connection == null) return;

        try
        {
            var script = await _scriptGeneratorService.GenerateCreateScriptAsync(
                _connection, node.DatabaseObject.Name, node.ObjectType);
            
            GenerateScriptRequested?.Invoke(this, new GenerateScriptEventArgs(script, $"CREATE {node.DisplayName}"));
            StatusMessage = $"已產生 CREATE 腳本: {node.DatabaseObject.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"產生 CREATE 腳本失敗: {ex.Message}";
        }
    }

    private async Task OnGenerateInsertScriptAsync(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || _connection == null || node.ObjectType != DatabaseObjectType.Table) return;

        try
        {
            var script = await _scriptGeneratorService.GenerateInsertScriptAsync(_connection, node.DatabaseObject.Name);
            
            GenerateScriptRequested?.Invoke(this, new GenerateScriptEventArgs(script, $"INSERT {node.DisplayName}"));
            StatusMessage = $"已產生 INSERT 腳本: {node.DatabaseObject.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"產生 INSERT 腳本失敗: {ex.Message}";
        }
    }

    private async Task OnGenerateDropScriptAsync(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null) return;

        try
        {
            var script = await _scriptGeneratorService.GenerateDropScriptAsync(node.DatabaseObject.Name, node.ObjectType);
            
            GenerateScriptRequested?.Invoke(this, new GenerateScriptEventArgs(script, $"DROP {node.DisplayName}"));
            StatusMessage = $"已產生 DROP 腳本: {node.DatabaseObject.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"產生 DROP 腳本失敗: {ex.Message}";
        }
    }

    private void OnCopyObjectName(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null) return;

        try
        {
            System.Windows.Clipboard.SetText(node.DatabaseObject.Name);
            StatusMessage = $"已複製物件名稱: {node.DatabaseObject.Name}";
        }
        catch (Exception ex)
        {
            StatusMessage = $"複製失敗: {ex.Message}";
        }
    }

    private void OnExpandAll()
    {
        foreach (var rootNode in RootNodes)
        {
            ExpandNodeRecursively(rootNode);
        }
        StatusMessage = "已展開所有節點";
    }

    private void OnCollapseAll()
    {
        foreach (var rootNode in RootNodes)
        {
            CollapseNodeRecursively(rootNode);
        }
        StatusMessage = "已摺疊所有節點";
    }

    private void OnClearSearch()
    {
        SearchText = string.Empty;
        StatusMessage = "已清除搜尋條件";
    }

    private void ExpandNodeRecursively(DatabaseObjectNode node)
    {
        if (node.IsFolder)
        {
            node.IsExpanded = true;
            if (!node.IsLoaded && _connection != null)
            {
                _ = OnExpandNodeAsync(node);
            }
        }

        foreach (var child in node.Children)
        {
            ExpandNodeRecursively(child);
        }
    }

    private void CollapseNodeRecursively(DatabaseObjectNode node)
    {
        node.IsExpanded = false;
        foreach (var child in node.Children)
        {
            CollapseNodeRecursively(child);
        }
    }

    private void FilterNodes()
    {
        if (string.IsNullOrWhiteSpace(SearchText))
        {
            // Show all nodes
            foreach (var rootNode in RootNodes)
            {
                rootNode.IsVisible = true;
                foreach (var child in rootNode.Children)
                {
                    child.IsVisible = true;
                }
            }
        }
        else
        {
            // Filter nodes based on search text
            var searchLower = SearchText.ToLower();
            foreach (var rootNode in RootNodes)
            {
                var hasVisibleChildren = false;
                foreach (var child in rootNode.Children)
                {
                    child.IsVisible = child.DisplayName.ToLower().Contains(searchLower);
                    if (child.IsVisible) hasVisibleChildren = true;
                }
                rootNode.IsVisible = hasVisibleChildren;
            }
        }
    }

    private void OnOpenViewEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.View) return;

        OpenViewEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.View));
        StatusMessage = $"開啟檢視表編輯器: {node.DatabaseObject.Name}";
    }

    private void OnOpenProcedureEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Procedure) return;

        OpenProcedureEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.Procedure));
        StatusMessage = $"開啟預存程序編輯器: {node.DatabaseObject.Name}";
    }

    private void OnOpenFunctionEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Function) return;

        OpenFunctionEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.Function));
        StatusMessage = $"開啟函數編輯器: {node.DatabaseObject.Name}";
    }

    private void OnOpenPackageEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Package) return;

        OpenPackageEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.Package));
        StatusMessage = $"開啟套件編輯器: {node.DatabaseObject.Name}";
    }

    private void OnOpenSequenceEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Sequence) return;

        OpenSequenceEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.Sequence));
        StatusMessage = $"開啟序列編輯器: {node.DatabaseObject.Name}";
    }

    private void OnOpenTriggerEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Trigger) return;

        OpenTriggerEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.Trigger));
        StatusMessage = $"開啟觸發器編輯器: {node.DatabaseObject.Name}";
    }

    private void OnOpenIndexEditor(DatabaseObjectNode? node)
    {
        if (node?.DatabaseObject == null || node.ObjectType != DatabaseObjectType.Index) return;

        OpenIndexEditorRequested?.Invoke(this, new OpenObjectEditorEventArgs(node.DatabaseObject.Name, DatabaseObjectType.Index));
        StatusMessage = $"開啟索引編輯器: {node.DatabaseObject.Name}";
    }
}

public class DatabaseObjectNode : ViewModelBase
{
    public string DisplayName { get; set; }
    public DatabaseObjectType ObjectType { get; set; }
    public bool IsFolder { get; set; }
    public DatabaseObject? DatabaseObject { get; set; }
    public DatabaseObjectNode? Parent { get; set; }
    public ObservableCollection<DatabaseObjectNode> Children { get; } = new();

    private bool _isExpanded;
    public bool IsExpanded
    {
        get => _isExpanded;
        set => SetProperty(ref _isExpanded, value);
    }

    private bool _isSelected;
    public bool IsSelected
    {
        get => _isSelected;
        set => SetProperty(ref _isSelected, value);
    }

    private bool _isLoaded;
    public bool IsLoaded
    {
        get => _isLoaded;
        set => SetProperty(ref _isLoaded, value);
    }

    private bool _isVisible = true;
    public bool IsVisible
    {
        get => _isVisible;
        set => SetProperty(ref _isVisible, value);
    }

    private bool _isPlaceholder;
    public bool IsPlaceholder
    {
        get => _isPlaceholder;
        set => SetProperty(ref _isPlaceholder, value);
    }

    public bool IsTableObject
    {
        get
        {
            try
            {
                return ObjectType == DatabaseObjectType.Table && !IsFolder && !IsPlaceholder;
            }
            catch
            {
                return false;
            }
        }
    }

    public DatabaseObjectNode(string displayName, DatabaseObjectType objectType, bool isFolder)
    {
        DisplayName = displayName;
        ObjectType = objectType;
        IsFolder = isFolder;
    }
}

public class OpenTableDataEventArgs : EventArgs
{
    public string TableName { get; }

    public OpenTableDataEventArgs(string tableName)
    {
        TableName = tableName;
    }
}

public class OpenTableDesignEventArgs : EventArgs
{
    public string TableName { get; }

    public OpenTableDesignEventArgs(string tableName)
    {
        TableName = tableName;
    }
}

public class GenerateScriptEventArgs : EventArgs
{
    public string Script { get; }
    public string Title { get; }

    public GenerateScriptEventArgs(string script, string title)
    {
        Script = script;
        Title = title;
    }
}

public class OpenObjectEditorEventArgs : EventArgs
{
    public string ObjectName { get; }
    public DatabaseObjectType ObjectType { get; }

    public OpenObjectEditorEventArgs(string objectName, DatabaseObjectType objectType)
    {
        ObjectName = objectName;
        ObjectType = objectType;
    }
}