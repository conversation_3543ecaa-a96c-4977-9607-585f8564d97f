using System.Data;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;
using System.Timers;

namespace OracleMS.Services;

/// <summary>
/// 連線管理服務，負責資料庫連線的建立、測試和管理
/// </summary>
public class ConnectionService : IConnectionService
{
    private readonly IConnectionProvider _connectionProvider;
    private readonly IConfigurationService _configurationService;
    private readonly ILogger<ConnectionService> _logger;
    private const string ConnectionsKey = "SavedConnections";
    
    // Connection state management
    private readonly Dictionary<string, ActiveConnection> _activeConnections = new();
    private readonly object _connectionLock = new();
    
    public event EventHandler<ConnectionStateChangedEventArgs>? ConnectionStateChanged;

    public ConnectionService(
        IConnectionProvider connectionProvider,
        IConfigurationService configurationService,
        ILogger<ConnectionService> logger)
    {
        _connectionProvider = connectionProvider ?? throw new ArgumentNullException(nameof(connectionProvider));
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 測試資料庫連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>連線測試結果</returns>
    public async Task<bool> TestConnectionAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            _logger.LogInformation("開始測試連線: {ConnectionName}", connectionInfo.Name);

            // 驗證連線資訊
            var (isValid, errorMessage) = connectionInfo.Validate();
            if (!isValid)
            {
                _logger.LogWarning("連線資訊驗證失敗: {ErrorMessage}", errorMessage);
                return false;
            }

            // 使用連線提供者測試連線
            using var connection = await _connectionProvider.CreateConnectionAsync(connectionInfo);
            
            // 執行簡單查詢驗證連線
            using var command = connection.CreateCommand();
            command.CommandText = "SELECT SYSDATE FROM DUAL";
            command.CommandTimeout = 10;
            
            var result = await Task.Run(() => command.ExecuteScalar());
            
            _logger.LogInformation("連線測試成功: {ConnectionName}", connectionInfo.Name);
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "連線測試失敗: {ConnectionName}", connectionInfo.Name);
            return false;
        }
    }

    /// <summary>
    /// 建立資料庫連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>資料庫連線物件</returns>
    public async Task<IDbConnection> CreateConnectionAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            _logger.LogInformation("建立資料庫連線: {ConnectionName}", connectionInfo.Name);

            // 驗證連線資訊
            var (isValid, errorMessage) = connectionInfo.Validate();
            if (!isValid)
            {
                throw new OracleManagementException($"連線資訊無效: {errorMessage}");
            }

            // 使用連線提供者建立連線
            var connection = await _connectionProvider.CreateConnectionAsync(connectionInfo);
            
            // 更新最後連線時間
            connectionInfo.LastConnected = DateTime.Now;
            await UpdateConnectionLastConnectedAsync(connectionInfo);
            
            _logger.LogInformation("資料庫連線建立成功: {ConnectionName}", connectionInfo.Name);
            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立資料庫連線失敗: {ConnectionName}", connectionInfo.Name);
            throw;
        }
    }

    /// <summary>
    /// 儲存連線資訊
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    public async Task SaveConnectionAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            _logger.LogInformation("儲存連線資訊: {ConnectionName}", connectionInfo.Name);

            // 驗證連線資訊
            var (isValid, errorMessage) = connectionInfo.Validate();
            if (!isValid)
            {
                throw new OracleManagementException($"連線資訊無效: {errorMessage}");
            }

            // 如果沒有 ID，產生新的 ID
            if (string.IsNullOrEmpty(connectionInfo.Id))
            {
                connectionInfo.Id = Guid.NewGuid().ToString();
            }

            // 取得現有連線清單
            var connections = await GetSavedConnectionsAsync();
            var connectionList = connections.ToList();

            // 檢查是否為更新現有連線
            connectionInfo.Id = connectionInfo.Username + "@" + connectionInfo.ServiceName + "/" + connectionInfo.Server;
            var existingIndex = connectionList.FindIndex(c => c.Id == connectionInfo.Id);
            if (existingIndex >= 0)
            {
                connectionList[existingIndex] = connectionInfo;
                _logger.LogInformation("更新現有連線: {ConnectionName}", connectionInfo.Name);
            }
            else
            {
                connectionList.Add(connectionInfo);
                _logger.LogInformation("新增連線: {ConnectionName}", connectionInfo.Name);
            }

            // 儲存連線清單
            await SaveConnectionListAsync(connectionList);

            // 如果需要儲存密碼，加密儲存連線字串
            if (connectionInfo.SavePassword)
            {
                var connectionString = connectionInfo.BuildConnectionString();
                await _configurationService.SetConnectionStringAsync(connectionInfo.Id, connectionString);
                _logger.LogDebug("連線字串已加密儲存: {ConnectionId}", connectionInfo.Id);
            }

            _logger.LogInformation("連線資訊儲存成功: {ConnectionName}", connectionInfo.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "儲存連線資訊失敗: {ConnectionName}", connectionInfo.Name);
            throw;
        }
    }

    /// <summary>
    /// 取得已儲存的連線清單
    /// </summary>
    /// <returns>連線資訊清單</returns>
    public async Task<IEnumerable<ConnectionInfo>> GetSavedConnectionsAsync()
    {
        try
        {
            _logger.LogDebug("讀取已儲存的連線清單");

            var connections = await _configurationService.GetSettingAsync<List<ConnectionInfo>>(ConnectionsKey);
            
            if (connections == null)
            {
                _logger.LogDebug("未找到已儲存的連線");
                return new List<ConnectionInfo>();
            }

            // 載入儲存密碼的連線字串
            foreach (var connection in connections.Where(c => c.SavePassword))
            {
                try
                {
                    var connectionString = await _configurationService.GetConnectionStringAsync(connection.Id);
                    if (!string.IsNullOrEmpty(connectionString))
                    {
                        // 從連線字串中解析密碼
                        connection.Password = ExtractPasswordFromConnectionString(connectionString);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "載入連線密碼失敗: {ConnectionId}", connection.Id);
                    connection.Password = string.Empty;
                    connection.SavePassword = false;
                }
            }

            _logger.LogInformation("成功讀取 {Count} 個已儲存的連線", connections.Count);
            return connections;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "讀取已儲存連線清單失敗");
            return new List<ConnectionInfo>();
        }
    }

    /// <summary>
    /// 刪除連線
    /// </summary>
    /// <param name="connectionId">連線識別碼</param>
    public async Task DeleteConnectionAsync(string connectionId)
    {
        try
        {
            _logger.LogInformation("刪除連線: {ConnectionId}", connectionId);

            if (string.IsNullOrEmpty(connectionId))
            {
                throw new ArgumentException("連線識別碼不能為空", nameof(connectionId));
            }

            // 取得現有連線清單
            var connections = await GetSavedConnectionsAsync();
            var connectionList = connections.ToList();

            // 尋找要刪除的連線
            var connectionToDelete = connectionList.FirstOrDefault(c => c.Id == connectionId);
            if (connectionToDelete == null)
            {
                _logger.LogWarning("找不到要刪除的連線: {ConnectionId}", connectionId);
                return;
            }

            // 從清單中移除連線
            connectionList.Remove(connectionToDelete);

            // 儲存更新後的清單
            await SaveConnectionListAsync(connectionList);

            // 刪除加密的連線字串
            try
            {
                await _configurationService.SetConnectionStringAsync(connectionId, string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "刪除連線字串失敗: {ConnectionId}", connectionId);
            }

            _logger.LogInformation("連線刪除成功: {ConnectionName}", connectionToDelete.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刪除連線失敗: {ConnectionId}", connectionId);
            throw;
        }
    }

    /// <summary>
    /// 儲存連線清單
    /// </summary>
    /// <param name="connections">連線清單</param>
    private async Task SaveConnectionListAsync(List<ConnectionInfo> connections)
    {
        try
        {
            // 建立不含密碼的連線清單用於儲存
            var connectionsToSave = connections.Select(c => c.SavePassword ? c : c.CloneWithoutPassword()).ToList();
            
            await _configurationService.SetSettingAsync(ConnectionsKey, connectionsToSave);
            _logger.LogDebug("連線清單儲存成功，共 {Count} 個連線", connections.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "儲存連線清單失敗");
            throw;
        }
    }

    /// <summary>
    /// 更新連線的最後連線時間
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    private async Task UpdateConnectionLastConnectedAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            var connections = await GetSavedConnectionsAsync();
            var connectionList = connections.ToList();
            
            var existingConnection = connectionList.FirstOrDefault(c => c.Id == connectionInfo.Id);
            if (existingConnection != null)
            {
                existingConnection.LastConnected = connectionInfo.LastConnected;
                await SaveConnectionListAsync(connectionList);
                _logger.LogDebug("更新連線最後連線時間: {ConnectionName}", connectionInfo.Name);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "更新連線最後連線時間失敗: {ConnectionName}", connectionInfo.Name);
            // 不拋出例外，因為這不是關鍵功能
        }
    }

    /// <summary>
    /// 從連線字串中提取密碼
    /// </summary>
    /// <param name="connectionString">連線字串</param>
    /// <returns>密碼</returns>
    private static string ExtractPasswordFromConnectionString(string connectionString)
    {
        try
        {
            var parts = connectionString.Split(';', StringSplitOptions.RemoveEmptyEntries);
            var passwordPart = parts.FirstOrDefault(p => p.Trim().StartsWith("Password=", StringComparison.OrdinalIgnoreCase));
            
            if (passwordPart != null)
            {
                return passwordPart.Split('=', 2)[1];
            }
            
            return string.Empty;
        }
        catch
        {
            return string.Empty;
        }
    }

    /// <summary>
    /// 建立並管理活動連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>活動連線物件</returns>
    public async Task<IDbConnection> CreateManagedConnectionAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            var connection = await CreateConnectionAsync(connectionInfo);
            
            lock (_connectionLock)
            {
                // 如果已存在相同ID的連線，先關閉舊連線
                if (_activeConnections.TryGetValue(connectionInfo.Id, out var existingConnection))
                {
                    existingConnection.Dispose();
                    _activeConnections.Remove(connectionInfo.Id);
                }

                // 建立新的活動連線
                var activeConnection = new ActiveConnection(connectionInfo, connection, this);
                _activeConnections[connectionInfo.Id] = activeConnection;
                
                // 如果設定自動重連，啟動心跳檢查
                if (connectionInfo.AutoConnect)
                {
                    activeConnection.StartHeartbeat();
                }
            }

            // 觸發連線狀態變更事件
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(connectionInfo, ConnectionStatus.Connected));
            
            _logger.LogInformation("活動連線建立成功: {ConnectionName}", connectionInfo.Name);
            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立活動連線失敗: {ConnectionName}", connectionInfo.Name);
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(connectionInfo, ConnectionStatus.Disconnected, ex.Message));
            throw;
        }
    }

    /// <summary>
    /// 關閉活動連線
    /// </summary>
    /// <param name="connectionId">連線識別碼</param>
    public void CloseConnection(string connectionId)
    {
        lock (_connectionLock)
        {
            if (_activeConnections.TryGetValue(connectionId, out var activeConnection))
            {
                activeConnection.Dispose();
                _activeConnections.Remove(connectionId);
                
                ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(
                    activeConnection.ConnectionInfo, ConnectionStatus.Disconnected));
                
                _logger.LogInformation("活動連線已關閉: {ConnectionName}", activeConnection.ConnectionInfo.Name);
            }
        }
    }

    /// <summary>
    /// 取得活動連線
    /// </summary>
    /// <param name="connectionId">連線識別碼</param>
    /// <returns>活動連線，如果不存在則返回null</returns>
    public IDbConnection? GetActiveConnection(string connectionId)
    {
        lock (_connectionLock)
        {
            return _activeConnections.TryGetValue(connectionId, out var activeConnection) 
                ? activeConnection.Connection 
                : null;
        }
    }

    /// <summary>
    /// 取得所有活動連線資訊
    /// </summary>
    /// <returns>活動連線資訊清單</returns>
    public IEnumerable<ConnectionInfo> GetActiveConnections()
    {
        lock (_connectionLock)
        {
            return _activeConnections.Values.Select(ac => ac.ConnectionInfo).ToList();
        }
    }

    /// <summary>
    /// 檢查連線是否為活動狀態
    /// </summary>
    /// <param name="connectionId">連線識別碼</param>
    /// <returns>是否為活動狀態</returns>
    public bool IsConnectionActive(string connectionId)
    {
        lock (_connectionLock)
        {
            return _activeConnections.ContainsKey(connectionId);
        }
    }

    /// <summary>
    /// 關閉所有活動連線
    /// </summary>
    public void CloseAllConnections()
    {
        lock (_connectionLock)
        {
            foreach (var activeConnection in _activeConnections.Values)
            {
                activeConnection.Dispose();
                ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(
                    activeConnection.ConnectionInfo, ConnectionStatus.Disconnected));
            }
            _activeConnections.Clear();
            _logger.LogInformation("所有活動連線已關閉");
        }
    }

    /// <summary>
    /// 處理連線重連事件
    /// </summary>
    internal async void OnConnectionReconnected(ActiveConnection activeConnection)
    {
        try
        {
            var newConnection = await CreateConnectionAsync(activeConnection.ConnectionInfo);
            activeConnection.UpdateConnection(newConnection);
            
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(
                activeConnection.ConnectionInfo, ConnectionStatus.Reconnected));
            
            _logger.LogInformation("連線重連成功: {ConnectionName}", activeConnection.ConnectionInfo.Name);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "連線重連失敗: {ConnectionName}", activeConnection.ConnectionInfo.Name);
            ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(
                activeConnection.ConnectionInfo, ConnectionStatus.Disconnected, ex.Message));
        }
    }

    /// <summary>
    /// 處理連線失敗事件
    /// </summary>
    internal void OnConnectionFailed(ActiveConnection activeConnection, string errorMessage)
    {
        ConnectionStateChanged?.Invoke(this, new ConnectionStateChangedEventArgs(
            activeConnection.ConnectionInfo, ConnectionStatus.Disconnected, errorMessage));
        
        _logger.LogWarning("連線失敗: {ConnectionName}, 錯誤: {Error}", 
            activeConnection.ConnectionInfo.Name, errorMessage);
    }
}

/// <summary>
/// 活動連線管理類別
/// </summary>
public class ActiveConnection : IDisposable
{
    private readonly ConnectionService _connectionService;
    private readonly System.Timers.Timer _heartbeatTimer;
    private IDbConnection _connection;
    private bool _disposed;

    public ConnectionInfo ConnectionInfo { get; }
    public IDbConnection Connection => _connection;
    public DateTime LastHeartbeat { get; private set; }
    public bool IsHealthy { get; private set; } = true;

    public ActiveConnection(ConnectionInfo connectionInfo, IDbConnection connection, ConnectionService connectionService)
    {
        ConnectionInfo = connectionInfo ?? throw new ArgumentNullException(nameof(connectionInfo));
        _connection = connection ?? throw new ArgumentNullException(nameof(connection));
        _connectionService = connectionService ?? throw new ArgumentNullException(nameof(connectionService));
        
        LastHeartbeat = DateTime.Now;
        
        // 設定心跳檢查計時器 (每30秒檢查一次)
        _heartbeatTimer = new System.Timers.Timer(30000);
        _heartbeatTimer.Elapsed += OnHeartbeatTimer;
        _heartbeatTimer.AutoReset = true;
    }

    /// <summary>
    /// 啟動心跳檢查
    /// </summary>
    public void StartHeartbeat()
    {
        if (!_disposed)
        {
            _heartbeatTimer.Start();
        }
    }

    /// <summary>
    /// 停止心跳檢查
    /// </summary>
    public void StopHeartbeat()
    {
        _heartbeatTimer.Stop();
    }

    /// <summary>
    /// 更新連線物件
    /// </summary>
    /// <param name="newConnection">新的連線物件</param>
    public void UpdateConnection(IDbConnection newConnection)
    {
        if (_disposed) return;

        var oldConnection = _connection;
        _connection = newConnection;
        LastHeartbeat = DateTime.Now;
        IsHealthy = true;

        // 關閉舊連線
        try
        {
            oldConnection?.Dispose();
        }
        catch
        {
            // 忽略關閉舊連線時的錯誤
        }
    }

    /// <summary>
    /// 心跳檢查事件處理
    /// </summary>
    private async void OnHeartbeatTimer(object? sender, ElapsedEventArgs e)
    {
        if (_disposed) return;

        try
        {
            // 執行簡單查詢檢查連線狀態
            using var command = _connection.CreateCommand();
            command.CommandText = "SELECT 1 FROM DUAL";
            command.CommandTimeout = 5;
            
            await Task.Run(() => command.ExecuteScalar());
            
            LastHeartbeat = DateTime.Now;
            if (!IsHealthy)
            {
                IsHealthy = true;
                // 連線恢復，觸發重連事件
                _connectionService.OnConnectionReconnected(this);
            }
        }
        catch (Exception ex)
        {
            IsHealthy = false;
            
            // 如果設定自動重連，嘗試重新連線
            if (ConnectionInfo.AutoConnect)
            {
                try
                {
                    _connectionService.OnConnectionReconnected(this);
                }
                catch
                {
                    _connectionService.OnConnectionFailed(this, ex.Message);
                }
            }
            else
            {
                _connectionService.OnConnectionFailed(this, ex.Message);
            }
        }
    }

    public void Dispose()
    {
        if (_disposed) return;

        _disposed = true;
        _heartbeatTimer?.Dispose();
        
        try
        {
            _connection?.Dispose();
        }
        catch
        {
            // 忽略關閉連線時的錯誤
        }
    }
}

/// <summary>
/// 連線狀態變更事件參數
/// </summary>
public class ConnectionStateChangedEventArgs : EventArgs
{
    public ConnectionInfo ConnectionInfo { get; }
    public ConnectionStatus State { get; }
    public string? ErrorMessage { get; }
    public DateTime Timestamp { get; }

    public ConnectionStateChangedEventArgs(ConnectionInfo connectionInfo, ConnectionStatus state, string? errorMessage = null)
    {
        ConnectionInfo = connectionInfo;
        State = state;
        ErrorMessage = errorMessage;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 連線狀態列舉
/// </summary>
public enum ConnectionStatus
{
    Disconnected,
    Connecting,
    Connected,
    Reconnected,
    Failed
}