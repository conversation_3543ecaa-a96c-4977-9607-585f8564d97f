# Tab Index 記錄功能實現說明

## 功能描述

實現了在開啟 index、table、function、procedure、package、view 等編輯器時記錄開啟者的 tab index，當編輯器關閉時自動返回到開啟者的 tab。

## 實現細節

### 1. 資料結構

在 `DbSession.xaml.cs` 中添加了一個 Dictionary 來記錄 tab 的開啟關係：

```csharp
// Tab index 記錄機制
private readonly Dictionary<TabItem, int> _tabOpenerIndex = new Dictionary<TabItem, int>();
```

### 2. 記錄開啟者 Tab Index

在 `CreateObjectEditorTab` 和 `CreateIndexEditorTab` 方法中，在創建新的編輯器標籤頁之前記錄當前選中的 tab index：

```csharp
// 記錄開啟者的 tab index
int openerTabIndex = MainTabControl.SelectedIndex;

// ... 創建編輯器邏輯 ...

// 記錄開啟者的 tab index
_tabOpenerIndex[newTabItem] = openerTabIndex;
```

### 3. 返回開啟者 Tab

實現了 `ReturnToOpenerTab` 方法來處理返回邏輯：

```csharp
private void ReturnToOpenerTab(TabItem closedTab)
{
    try
    {
        if (_tabOpenerIndex.TryGetValue(closedTab, out int openerIndex))
        {
            // 移除記錄
            _tabOpenerIndex.Remove(closedTab);

            // 檢查開啟者的 tab index 是否仍然有效
            if (openerIndex >= 0 && openerIndex < MainTabControl.Items.Count)
            {
                MainTabControl.SelectedIndex = openerIndex;
            }
            else if (MainTabControl.Items.Count > 0)
            {
                // 如果原來的 index 無效，選擇最接近的有效 index
                int validIndex = Math.Min(openerIndex, MainTabControl.Items.Count - 1);
                MainTabControl.SelectedIndex = Math.Max(0, validIndex);
            }
        }
        else if (MainTabControl.Items.Count > 0)
        {
            // 如果沒有記錄開啟者，選擇第一個標籤頁
            MainTabControl.SelectedIndex = 0;
        }
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"返回開啟者標籤頁時發生錯誤: {ex.Message}");
        // 發生錯誤時，嘗試選擇第一個標籤頁
        try
        {
            if (MainTabControl.Items.Count > 0)
                MainTabControl.SelectedIndex = 0;
        }
        catch (Exception fallbackEx)
        {
            System.Diagnostics.Debug.WriteLine($"選擇第一個標籤頁時發生錯誤: {fallbackEx.Message}");
        }
    }
}
```

### 4. 更新關閉按鈕處理

在所有編輯器的關閉按鈕點擊事件中，將原來的 `MainTabControl.SelectedIndex = 0` 替換為 `ReturnToOpenerTab(newTabItem)`：

```csharp
// 移除 TabItem 並返回開啟者的 tab index
try
{
    MainTabControl.Items.Remove(newTabItem);
    ReturnToOpenerTab(newTabItem);
}
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"移除標籤頁時發生錯誤: {ex.Message}");
}
```

### 5. IndexEditorView 取消按鈕處理

更新了 `IndexEditorView.xaml.cs` 中的 `CancelButton_Click` 方法，讓它也能正確返回到開啟者的 tab：

```csharp
private void CancelButton_Click(object sender, RoutedEventArgs e)
{
    // 關閉當前的 TabItem
    if (Parent is TabItem tabItem && tabItem.Parent is TabControl tabControl)
    {
        // 尋找包含此 TabControl 的 DbSession
        var dbSession = FindParentDbSession(tabControl);
        if (dbSession != null)
        {
            // 使用 DbSession 的 ReturnToOpenerTab 方法
            tabControl.Items.Remove(tabItem);
            dbSession.ReturnToOpenerTabPublic(tabItem);
        }
        else
        {
            // 如果找不到 DbSession，使用原來的邏輯
            tabControl.Items.Remove(tabItem);
        }
    }
}
```

### 6. 資源清理

在 `CleanupResources` 方法中添加了對 `_tabOpenerIndex` 的清理：

```csharp
// 清理 tab opener index 記錄
_tabOpenerIndex.Clear();
```

## 支援的編輯器類型

此功能支援以下類型的編輯器：

- Index（索引編輯器）
- Table（資料表編輯器）
- Function（函數編輯器）
- Procedure（預存程序編輯器）
- Package（套件編輯器）
- View（檢視表編輯器）

## 錯誤處理

實現了完整的錯誤處理機制：

1. 如果記錄的開啟者 index 無效（例如原來的 tab 已被關閉），會選擇最接近的有效 index
2. 如果沒有記錄開啟者，會選擇第一個標籤頁
3. 如果發生任何錯誤，會回退到選擇第一個標籤頁的安全行為

## 測試

創建了 `TestTabIndexRecording.cs` 測試文件來驗證功能的正確性。

## 使用場景

1. 用戶在查詢標籤頁中工作
2. 切換到資料表編輯器標籤頁
3. 從資料表編輯器開啟索引編輯器
4. 索引編輯器會記錄開啟者為資料表編輯器的 tab index
5. 當用戶關閉索引編輯器時，會自動返回到資料表編輯器標籤頁
6. 而不是返回到第一個標籤頁（查詢標籤頁）

這樣提供了更好的用戶體驗，讓用戶能夠在編輯器之間更自然地導航。