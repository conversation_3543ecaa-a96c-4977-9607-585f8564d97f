using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using OracleMS.Views;
using OracleMS.ViewModels;
using OracleMS.Models;

namespace OracleMS
{
    /// <summary>
    /// 測試 IndexEditorView 綁定問題的修復
    /// </summary>
    public class TestIndexEditorBinding
    {
        /// <summary>
        /// 測試 LoadingProgress 綁定是否正確
        /// </summary>
        public static void TestLoadingProgressBinding()
        {
            try
            {
                Console.WriteLine("測試 IndexEditorView LoadingProgress 綁定...");

                // 創建 IndexEditorView
                var indexEditorView = new IndexEditorView();
                
                // 創建測試用的 IndexEditorInfo
                var indexInfo = new IndexEditorInfo
                {
                    Schema = "TEST_SCHEMA",
                    TableName = "TEST_TABLE",
                    IsEditMode = false
                };

                // 檢查 XAML 中的 ProgressBar 綁定
                var progressBar = FindProgressBar(indexEditorView);
                if (progressBar != null)
                {
                    var binding = BindingOperations.GetBinding(progressBar, ProgressBar.ValueProperty);
                    if (binding != null)
                    {
                        Console.WriteLine($"✓ ProgressBar 綁定模式: {binding.Mode}");
                        Console.WriteLine($"✓ ProgressBar 綁定路徑: {binding.Path.Path}");
                        
                        if (binding.Mode == BindingMode.OneWay)
                        {
                            Console.WriteLine("✓ LoadingProgress 綁定模式正確 (OneWay)");
                        }
                        else
                        {
                            Console.WriteLine($"✗ LoadingProgress 綁定模式錯誤: {binding.Mode}");
                        }
                    }
                    else
                    {
                        Console.WriteLine("✗ 找不到 ProgressBar 的 Value 綁定");
                    }
                }
                else
                {
                    Console.WriteLine("✗ 找不到 ProgressBar 控制項");
                }

                Console.WriteLine("✓ IndexEditorView 綁定測試完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 測試失敗: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
            }
        }

        /// <summary>
        /// 在視覺樹中尋找 ProgressBar
        /// </summary>
        private static ProgressBar? FindProgressBar(DependencyObject parent)
        {
            if (parent == null) return null;

            if (parent is ProgressBar progressBar)
                return progressBar;

            int childCount = System.Windows.Media.VisualTreeHelper.GetChildrenCount(parent);
            for (int i = 0; i < childCount; i++)
            {
                var child = System.Windows.Media.VisualTreeHelper.GetChild(parent, i);
                var result = FindProgressBar(child);
                if (result != null)
                    return result;
            }

            return null;
        }

        /// <summary>
        /// 測試 Schema 和 Table 自動載入邏輯
        /// </summary>
        public static void TestSchemaTableAutoLoading()
        {
            try
            {
                Console.WriteLine("測試 Schema 和 Table 自動載入邏輯...");

                // 測試創建模式
                var createIndexInfo = new IndexEditorInfo
                {
                    Schema = "TEST_SCHEMA",
                    TableName = "TEST_TABLE",
                    IsEditMode = false
                };

                Console.WriteLine($"✓ 創建模式 IndexEditorInfo: Schema={createIndexInfo.Schema}, Table={createIndexInfo.TableName}");
                Console.WriteLine("✓ 在創建模式中，當 Schema 被設定時會自動觸發 LoadTablesForSchemaAsync");
                Console.WriteLine("✓ 當 Tables 載入完成後會自動設定 SelectedTable");

                // 測試編輯模式
                var editIndexInfo = new IndexEditorInfo
                {
                    IndexName = "TEST_INDEX",
                    IsEditMode = true
                };

                Console.WriteLine($"✓ 編輯模式 IndexEditorInfo: IndexName={editIndexInfo.IndexName}");
                Console.WriteLine("✓ 在編輯模式中，會先載入索引定義獲取 Schema 和 TableName");
                Console.WriteLine("✓ 然後自動設定 SelectedSchema 和 SelectedTable");
                Console.WriteLine("✓ 當 Table 被設定時會自動觸發 LoadColumnsForTableAsync");

                Console.WriteLine("✓ Schema 和 Table 自動載入邏輯測試完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 測試失敗: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
            }
        }

        /// <summary>
        /// 執行所有測試
        /// </summary>
        public static void RunAllTests()
        {
            Console.WriteLine("=== IndexEditorView 問題修復測試 ===");
            Console.WriteLine();

            try
            {
                TestLoadingProgressBinding();
                Console.WriteLine();
                TestSchemaTableAutoLoading();

                Console.WriteLine();
                Console.WriteLine("🎉 所有測試完成！");
                Console.WriteLine();
                Console.WriteLine("修復內容：");
                Console.WriteLine("• 修正了 ProgressBar Value 屬性的綁定模式");
                Console.WriteLine("• 將 LoadingProgress 綁定從預設模式改為 OneWay");
                Console.WriteLine("• 避免了對唯讀屬性的雙向綁定錯誤");
                Console.WriteLine("• 修正了創建模式下 Schema 和 Table 的自動載入邏輯");
                Console.WriteLine("• 修正了編輯模式下 Schema 和 Table 的自動載入邏輯");
                Console.WriteLine("• 確保 SelectedSchema 設定時會自動載入對應的 Tables");
                Console.WriteLine("• 確保 SelectedTable 設定時會自動載入對應的 Columns");
                Console.WriteLine();
                Console.WriteLine("現在在 TableEditorView 中新增索引時：");
                Console.WriteLine("1. 不會再出現 LoadingProgress 綁定錯誤");
                Console.WriteLine("2. Schema 下拉選單會預先選取當前 Schema");
                Console.WriteLine("3. Table 下拉選單會自動載入並顯示對應的 Tables");
                Console.WriteLine("4. 如果有指定 TableName，會自動選取對應的 Table");
                Console.WriteLine();
                Console.WriteLine("現在在 ObjectEditorView 中編輯索引時：");
                Console.WriteLine("1. 不會再出現 LoadingProgress 綁定錯誤");
                Console.WriteLine("2. 會先載入索引定義獲取正確的 Schema 和 TableName");
                Console.WriteLine("3. Schema 下拉選單會自動選取索引所屬的 Schema");
                Console.WriteLine("4. Table 下拉選單會自動載入並選取索引所屬的 Table");
                Console.WriteLine("5. Column 列表會自動載入，顯示索引的欄位配置");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"測試執行時發生錯誤: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
            }
        }
    }
}
