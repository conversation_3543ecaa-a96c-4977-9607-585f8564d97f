using System.IO;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;

namespace OracleMS.Services;

/// <summary>
/// 設定管理服務，負責應用程式設定的儲存和讀取
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly ILogger<ConfigurationService> _logger;
    private readonly string _configDirectory;
    private readonly string _settingsFile;
    private readonly string _connectionsFile;
    private readonly byte[] _encryptionKey;

    public ConfigurationService(ILogger<ConfigurationService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // 設定檔案路徑
        _configDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "OracleMS");
        _settingsFile = Path.Combine(_configDirectory, "settings.json");
        _connectionsFile = Path.Combine(_configDirectory, "connections.json");
        
        // 初始化加密金鑰
        _encryptionKey = GenerateOrLoadEncryptionKey();
        
        // 確保設定目錄存在
        EnsureConfigDirectoryExists();
    }

    /// <summary>
    /// 取得設定值
    /// </summary>
    /// <typeparam name="T">設定值類型</typeparam>
    /// <param name="key">設定鍵值</param>
    /// <returns>設定值</returns>
    public async Task<T?> GetSettingAsync<T>(string key)
    {
        try
        {
            if (!File.Exists(_settingsFile))
                return default(T);

            var json = await File.ReadAllTextAsync(_settingsFile);
            var settings = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);
            
            if (settings != null && settings.TryGetValue(key, out var value))
            {
                return JsonSerializer.Deserialize<T>(value.GetRawText());
            }

            return default(T);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "讀取設定失敗: {Key}", key);
            return default(T);
        }
    }

    /// <summary>
    /// 設定值
    /// </summary>
    /// <typeparam name="T">設定值類型</typeparam>
    /// <param name="key">設定鍵值</param>
    /// <param name="value">設定值</param>
    public async Task SetSettingAsync<T>(string key, T value)
    {
        try
        {
            Dictionary<string, object> settings;
            
            if (File.Exists(_settingsFile))
            {
                var json = await File.ReadAllTextAsync(_settingsFile);
                settings = JsonSerializer.Deserialize<Dictionary<string, object>>(json) ?? new Dictionary<string, object>();
            }
            else
            {
                settings = new Dictionary<string, object>();
            }

            settings[key] = value!;
            
            var updatedJson = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(_settingsFile, updatedJson);
            
            _logger.LogDebug("設定已儲存: {Key}", key);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "儲存設定失敗: {Key}", key);
            throw;
        }
    }

    /// <summary>
    /// 取得加密的連線字串
    /// </summary>
    /// <param name="connectionId">連線識別碼</param>
    /// <returns>解密後的連線字串</returns>
    public async Task<string> GetConnectionStringAsync(string connectionId)
    {
        try
        {
            if (!File.Exists(_connectionsFile))
                return string.Empty;

            var json = await File.ReadAllTextAsync(_connectionsFile);
            var connections = JsonSerializer.Deserialize<Dictionary<string, string>>(json);
            
            if (connections != null && connections.TryGetValue(connectionId, out var encryptedConnectionString))
            {
                return DecryptString(encryptedConnectionString);
            }

            return string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "讀取連線字串失敗: {ConnectionId}", connectionId);
            return string.Empty;
        }
    }

    /// <summary>
    /// 設定加密的連線字串
    /// </summary>
    /// <param name="connectionId">連線識別碼</param>
    /// <param name="connectionString">連線字串</param>
    public async Task SetConnectionStringAsync(string connectionId, string connectionString)
    {
        try
        {
            Dictionary<string, string> connections;
            
            if (File.Exists(_connectionsFile))
            {
                var json = await File.ReadAllTextAsync(_connectionsFile);
                connections = JsonSerializer.Deserialize<Dictionary<string, string>>(json) ?? new Dictionary<string, string>();
            }
            else
            {
                connections = new Dictionary<string, string>();
            }

            connections[connectionId] = EncryptString(connectionString);
            
            var updatedJson = JsonSerializer.Serialize(connections, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(_connectionsFile, updatedJson);
            
            _logger.LogDebug("連線字串已儲存: {ConnectionId}", connectionId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "儲存連線字串失敗: {ConnectionId}", connectionId);
            throw;
        }
    }

    /// <summary>
    /// 確保設定目錄存在
    /// </summary>
    private void EnsureConfigDirectoryExists()
    {
        try
        {
            if (!Directory.Exists(_configDirectory))
            {
                Directory.CreateDirectory(_configDirectory);
                _logger.LogInformation("建立設定目錄: {Directory}", _configDirectory);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立設定目錄失敗: {Directory}", _configDirectory);
            throw;
        }
    }

    /// <summary>
    /// 產生或載入加密金鑰
    /// </summary>
    /// <returns>加密金鑰</returns>
    private byte[] GenerateOrLoadEncryptionKey()
    {
        var keyFile = Path.Combine(_configDirectory, "key.dat");
        
        try
        {
            if (File.Exists(keyFile))
            {
                return File.ReadAllBytes(keyFile);
            }
            else
            {
                // 產生新的加密金鑰
                using var aes = Aes.Create();
                aes.GenerateKey();
                
                EnsureConfigDirectoryExists();
                File.WriteAllBytes(keyFile, aes.Key);
                
                _logger.LogInformation("產生新的加密金鑰");
                return aes.Key;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "處理加密金鑰失敗");
            throw;
        }
    }

    /// <summary>
    /// 加密字串
    /// </summary>
    /// <param name="plainText">明文</param>
    /// <returns>加密後的字串</returns>
    private string EncryptString(string plainText)
    {
        try
        {
            using var aes = Aes.Create();
            aes.Key = _encryptionKey;
            aes.GenerateIV();

            using var encryptor = aes.CreateEncryptor();
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using var swEncrypt = new StreamWriter(csEncrypt);
            
            swEncrypt.Write(plainText);
            swEncrypt.Close();
            
            var encrypted = msEncrypt.ToArray();
            var result = new byte[aes.IV.Length + encrypted.Length];
            Array.Copy(aes.IV, 0, result, 0, aes.IV.Length);
            Array.Copy(encrypted, 0, result, aes.IV.Length, encrypted.Length);
            
            return Convert.ToBase64String(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "字串加密失敗");
            throw;
        }
    }

    /// <summary>
    /// 解密字串
    /// </summary>
    /// <param name="cipherText">密文</param>
    /// <returns>解密後的字串</returns>
    private string DecryptString(string cipherText)
    {
        try
        {
            var fullCipher = Convert.FromBase64String(cipherText);
            
            using var aes = Aes.Create();
            aes.Key = _encryptionKey;
            
            var iv = new byte[aes.IV.Length];
            var cipher = new byte[fullCipher.Length - iv.Length];
            
            Array.Copy(fullCipher, 0, iv, 0, iv.Length);
            Array.Copy(fullCipher, iv.Length, cipher, 0, cipher.Length);
            
            aes.IV = iv;
            
            using var decryptor = aes.CreateDecryptor();
            using var msDecrypt = new MemoryStream(cipher);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);
            
            return srDecrypt.ReadToEnd();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "字串解密失敗");
            throw;
        }
    }

    /// <summary>
    /// 取得視窗設定
    /// </summary>
    /// <returns>視窗設定</returns>
    public WindowSettings? GetWindowSettings()
    {
        try
        {
            var windowSettingsFile = Path.Combine(_configDirectory, "window.json");
            
            if (!File.Exists(windowSettingsFile))
                return null;

            var json = File.ReadAllText(windowSettingsFile);
            return JsonSerializer.Deserialize<WindowSettings>(json);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "讀取視窗設定失敗");
            return null;
        }
    }

    /// <summary>
    /// 儲存視窗設定
    /// </summary>
    /// <param name="settings">視窗設定</param>
    public void SaveWindowSettings(WindowSettings settings)
    {
        try
        {
            var windowSettingsFile = Path.Combine(_configDirectory, "window.json");
            var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(windowSettingsFile, json);
            
            _logger.LogDebug("視窗設定已儲存");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "儲存視窗設定失敗");
        }
    }
}