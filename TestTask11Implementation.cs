using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.Services;
using OracleMS.Exceptions;

namespace OracleMS
{
    /// <summary>
    /// Task 11 實作測試：索引創建的資料庫操作
    /// </summary>
    public class TestTask11Implementation
    {
        public TestTask11Implementation()
        {
        }

        /// <summary>
        /// 測試索引定義驗證
        /// </summary>
        public async Task TestIndexDefinitionValidation()
        {
            Console.WriteLine("=== 測試索引定義驗證 ===");

            try
            {
                // 測試空索引名稱
                var invalidIndex = new IndexDefinition
                {
                    Name = "",
                    TableName = "TEST_TABLE",
                    Owner = "TEST_SCHEMA"
                };

                var validationResult = invalidIndex.Validate();
                if (!validationResult.IsValid)
                {
                    Console.WriteLine("✓ 正確檢測到空索引名稱");
                }
                else
                {
                    throw new Exception("應該檢測到空索引名稱錯誤");
                }

                // 測試空表格名稱
                var invalidIndex2 = new IndexDefinition
                {
                    Name = "IDX_TEST",
                    TableName = "",
                    Owner = "TEST_SCHEMA"
                };

                var validationResult2 = invalidIndex2.Validate();
                if (!validationResult2.IsValid)
                {
                    Console.WriteLine("✓ 正確檢測到空表格名稱");
                }
                else
                {
                    throw new Exception("應該檢測到空表格名稱錯誤");
                }

                // 測試沒有欄位
                var invalidIndex3 = new IndexDefinition
                {
                    Name = "IDX_TEST",
                    TableName = "TEST_TABLE",
                    Owner = "TEST_SCHEMA"
                };

                var validationResult3 = invalidIndex3.Validate();
                if (!validationResult3.IsValid)
                {
                    Console.WriteLine("✓ 正確檢測到沒有索引欄位");
                }
                else
                {
                    throw new Exception("應該檢測到沒有索引欄位錯誤");
                }

                Console.WriteLine("✓ 索引定義驗證測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 索引定義驗證測試失敗: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 測試DDL生成
        /// </summary>
        public async Task TestDDLGeneration()
        {
            Console.WriteLine("=== 測試DDL生成 ===");

            try
            {
                // 測試基本索引DDL生成
                var indexDefinition = new IndexDefinition
                {
                    Name = "IDX_TEST_INDEX",
                    TableName = "TEST_TABLE",
                    Owner = "TEST_SCHEMA",
                    IsUnique = false,
                    Type = IndexType.Normal
                };

                indexDefinition.Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = "COLUMN1",
                    Position = 1,
                    IsDescending = false
                });

                // 創建模擬的依賴項
                var mockRepository = new MockDatabaseRepository();
                var mockLogger = new MockLogger();
                var service = new ObjectEditorService(mockRepository, mockLogger);
                
                // 使用反射來測試私有方法 GenerateCreateIndexSql
                var method = typeof(ObjectEditorService).GetMethod("GenerateCreateIndexSql", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (method != null)
                {
                    var sql = (string)method.Invoke(service, new object[] { indexDefinition });
                    
                    if (sql.Contains("CREATE INDEX IDX_TEST_INDEX ON TEST_TABLE"))
                    {
                        Console.WriteLine("✓ 基本DDL生成正確");
                    }
                    else
                    {
                        throw new Exception($"DDL生成錯誤: {sql}");
                    }

                    // 測試唯一索引
                    indexDefinition.IsUnique = true;
                    var uniqueSql = (string)method.Invoke(service, new object[] { indexDefinition });
                    
                    if (uniqueSql.Contains("CREATE UNIQUE INDEX"))
                    {
                        Console.WriteLine("✓ 唯一索引DDL生成正確");
                    }
                    else
                    {
                        throw new Exception($"唯一索引DDL生成錯誤: {uniqueSql}");
                    }
                }
                else
                {
                    Console.WriteLine("⚠ 無法測試私有方法 GenerateCreateIndexSql");
                }

                Console.WriteLine("✓ DDL生成測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ DDL生成測試失敗: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 測試UpdateColumnsFromSelection方法
        /// </summary>
        public async Task TestUpdateColumnsFromSelection()
        {
            Console.WriteLine("=== 測試UpdateColumnsFromSelection方法 ===");

            try
            {
                var indexDefinition = new IndexDefinition
                {
                    Name = "IDX_TEST_INDEX",
                    TableName = "TEST_TABLE",
                    Owner = "TEST_SCHEMA"
                };

                var selectedColumns = new[] { "COLUMN1", "COLUMN2", "COLUMN3" };
                indexDefinition.UpdateColumnsFromSelection(selectedColumns);

                if (indexDefinition.Columns.Count == 3)
                {
                    Console.WriteLine("✓ 正確設定了3個欄位");
                }
                else
                {
                    throw new Exception($"欄位數量錯誤，期望3個，實際{indexDefinition.Columns.Count}個");
                }

                // 檢查欄位順序
                for (int i = 0; i < selectedColumns.Length; i++)
                {
                    if (indexDefinition.Columns[i].ColumnName != selectedColumns[i])
                    {
                        throw new Exception($"欄位順序錯誤，位置{i}期望{selectedColumns[i]}，實際{indexDefinition.Columns[i].ColumnName}");
                    }
                    
                    if (indexDefinition.Columns[i].Position != i + 1)
                    {
                        throw new Exception($"欄位位置錯誤，期望{i + 1}，實際{indexDefinition.Columns[i].Position}");
                    }
                }

                Console.WriteLine("✓ UpdateColumnsFromSelection方法測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ UpdateColumnsFromSelection方法測試失敗: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 測試索引類型
        /// </summary>
        public async Task TestIndexTypes()
        {
            Console.WriteLine("=== 測試索引類型 ===");

            try
            {
                // 測試不同索引類型的DDL生成
                var indexDefinition = new IndexDefinition
                {
                    Name = "IDX_TEST_INDEX",
                    TableName = "TEST_TABLE",
                    Owner = "TEST_SCHEMA",
                    IsUnique = false,
                    Type = IndexType.Bitmap
                };

                indexDefinition.Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = "COLUMN1",
                    Position = 1,
                    IsDescending = false
                });

                // 創建模擬的依賴項
                var mockRepository = new MockDatabaseRepository();
                var mockLogger = new MockLogger();
                var service = new ObjectEditorService(mockRepository, mockLogger);
                
                // 使用反射來測試私有方法 GenerateCreateIndexSql
                var method = typeof(ObjectEditorService).GetMethod("GenerateCreateIndexSql", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (method != null)
                {
                    var sql = (string)method.Invoke(service, new object[] { indexDefinition });
                    
                    if (sql.Contains("CREATE BITMAP INDEX"))
                    {
                        Console.WriteLine("✓ Bitmap索引DDL生成正確");
                    }
                    else
                    {
                        throw new Exception($"Bitmap索引DDL生成錯誤: {sql}");
                    }
                }

                Console.WriteLine("✓ 索引類型測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 索引類型測試失敗: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 測試多欄位索引
        /// </summary>
        public async Task TestMultiColumnIndex()
        {
            Console.WriteLine("=== 測試多欄位索引 ===");

            try
            {
                var indexDefinition = new IndexDefinition
                {
                    Name = "IDX_MULTI_COLUMN",
                    TableName = "TEST_TABLE",
                    Owner = "TEST_SCHEMA",
                    IsUnique = false,
                    Type = IndexType.Normal
                };

                // 新增多個欄位
                indexDefinition.Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = "COLUMN1",
                    Position = 1,
                    IsDescending = false
                });

                indexDefinition.Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = "COLUMN2",
                    Position = 2,
                    IsDescending = true
                });

                indexDefinition.Columns.Add(new IndexColumnDefinition
                {
                    ColumnName = "COLUMN3",
                    Position = 3,
                    IsDescending = false
                });

                // 創建模擬的依賴項
                var mockRepository = new MockDatabaseRepository();
                var mockLogger = new MockLogger();
                var service = new ObjectEditorService(mockRepository, mockLogger);
                
                // 使用反射來測試私有方法 GenerateCreateIndexSql
                var method = typeof(ObjectEditorService).GetMethod("GenerateCreateIndexSql", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (method != null)
                {
                    var sql = (string)method.Invoke(service, new object[] { indexDefinition });
                    
                    if (sql.Contains("COLUMN1, COLUMN2 DESC, COLUMN3"))
                    {
                        Console.WriteLine("✓ 多欄位索引DDL生成正確");
                    }
                    else
                    {
                        throw new Exception($"多欄位索引DDL生成錯誤: {sql}");
                    }
                }

                Console.WriteLine("✓ 多欄位索引測試通過");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ 多欄位索引測試失敗: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 執行所有測試
        /// </summary>
        public async Task RunAllTests()
        {
            Console.WriteLine("開始執行 Task 11 測試...\n");

            try
            {
                await TestIndexDefinitionValidation();
                Console.WriteLine();

                await TestDDLGeneration();
                Console.WriteLine();

                await TestUpdateColumnsFromSelection();
                Console.WriteLine();

                await TestIndexTypes();
                Console.WriteLine();

                await TestMultiColumnIndex();
                Console.WriteLine();

                Console.WriteLine("✓ 所有 Task 11 測試通過！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Task 11 測試失敗: {ex.Message}");
                throw;
            }
        }


    }

    /// <summary>
    /// 模擬的資料庫儲存庫
    /// </summary>
    public class MockDatabaseRepository : IDatabaseRepository
    {
        public Task<IEnumerable<DatabaseObject>> GetDatabaseObjectsAsync(IDbConnection connection, DatabaseObjectType type)
        {
            return Task.FromResult<IEnumerable<DatabaseObject>>(new List<DatabaseObject>());
        }

        public Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql)
        {
            return Task.FromResult(new DataTable());
        }

        public Task<int> ExecuteNonQueryAsync(IDbConnection connection, string sql)
        {
            return Task.FromResult(1);
        }

        public Task<TableSchema> GetTableSchemaAsync(IDbConnection connection, string tableName)
        {
            return Task.FromResult(new TableSchema());
        }

        public Task<IEnumerable<DatabaseObject>> GetTablesAsync(IDbConnection connection)
        {
            return Task.FromResult<IEnumerable<DatabaseObject>>(new List<DatabaseObject>());
        }

        public Task<IEnumerable<DatabaseObject>> GetViewsAsync(IDbConnection connection)
        {
            return Task.FromResult<IEnumerable<DatabaseObject>>(new List<DatabaseObject>());
        }

        public Task<IEnumerable<DatabaseObject>> GetProceduresAsync(IDbConnection connection)
        {
            return Task.FromResult<IEnumerable<DatabaseObject>>(new List<DatabaseObject>());
        }

        public Task<IEnumerable<DatabaseObject>> GetFunctionsAsync(IDbConnection connection)
        {
            return Task.FromResult<IEnumerable<DatabaseObject>>(new List<DatabaseObject>());
        }

        public Task<IEnumerable<string>> GetSchemasAsync(IDbConnection connection)
        {
            return Task.FromResult<IEnumerable<string>>(new List<string>());
        }

        public Task<IEnumerable<DatabaseObject>> GetTablesBySchemaAsync(IDbConnection connection, string schemaName)
        {
            return Task.FromResult<IEnumerable<DatabaseObject>>(new List<DatabaseObject>());
        }
    }

    /// <summary>
    /// 模擬的記錄器
    /// </summary>
    public class MockLogger : ILogger<ObjectEditorService>
    {
        public IDisposable? BeginScope<TState>(TState state) => null;
        public bool IsEnabled(LogLevel logLevel) => true;
        public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter) { }
    }
}