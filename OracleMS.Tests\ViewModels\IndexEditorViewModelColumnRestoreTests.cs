using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using Xunit;

namespace OracleMS.Tests.ViewModels
{
    /// <summary>
    /// IndexEditorViewModel 索引欄位恢復功能測試
    /// </summary>
    public class IndexEditorViewModelColumnRestoreTests
    {
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<IScriptGeneratorService> _mockScriptGeneratorService;
        private readonly Mock<IObjectEditorService> _mockObjectEditorService;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<ILogger> _mockLogger;

        public IndexEditorViewModelColumnRestoreTests()
        {
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            _mockObjectEditorService = new Mock<IObjectEditorService>();
            _mockConnection = new Mock<IDbConnection>();
            _mockLogger = new Mock<ILogger>();
        }

        /// <summary>
        /// 測試當 IndexDefinition 被設定且可用欄位已載入時，索引欄位會立即恢復到已選清單
        /// </summary>
        [Fact]
        public void IndexDefinition_SetWithColumnsAndAvailableColumns_ShouldRestoreColumnsImmediately()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsUnique = false,
                IsEditMode = true, // 編輯模式
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            // 先添加一些可用欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("NAME");
            viewModel.AvailableColumns.Add("EMAIL");
            viewModel.AvailableColumns.Add("CREATED_DATE");

            // 創建有有效 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "ID", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "NAME", Position = 2 }
                }
            };

            // Act - 設定 IndexDefinition（這應該觸發欄位恢復）
            // 使用反射來設定私有屬性
            var property = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            property?.SetValue(viewModel, indexDefinition);

            // Assert
            Assert.Equal(2, viewModel.SelectedColumns.Count);
            Assert.Contains("ID", viewModel.SelectedColumns);
            Assert.Contains("NAME", viewModel.SelectedColumns);
            
            // 確認欄位按照正確順序排列
            Assert.Equal("ID", viewModel.SelectedColumns[0]);
            Assert.Equal("NAME", viewModel.SelectedColumns[1]);
            
            // 確認這些欄位已從可用欄位中移除
            Assert.DoesNotContain("ID", viewModel.AvailableColumns);
            Assert.DoesNotContain("NAME", viewModel.AvailableColumns);
            
            // 確認其他欄位仍在可用欄位中
            Assert.Contains("EMAIL", viewModel.AvailableColumns);
            Assert.Contains("CREATED_DATE", viewModel.AvailableColumns);
        }

        /// <summary>
        /// 測試當 IndexDefinition 被設定但可用欄位尚未載入時，不會立即恢復欄位
        /// </summary>
        [Fact]
        public void IndexDefinition_SetWithColumnsButNoAvailableColumns_ShouldNotRestoreImmediately()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsUnique = false,
                IsEditMode = true, // 編輯模式
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            // 不添加可用欄位（模擬欄位尚未載入的情況）

            // 創建有有效 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "ID", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "NAME", Position = 2 }
                }
            };

            // Act - 設定 IndexDefinition
            var property = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            property?.SetValue(viewModel, indexDefinition);

            // Assert - 由於沒有可用欄位，所以不應該恢復任何欄位
            Assert.Equal(0, viewModel.SelectedColumns.Count);
            Assert.Equal(0, viewModel.AvailableColumns.Count);
        }

        /// <summary>
        /// 測試在創建模式下，IndexDefinition 設定不會觸發欄位恢復
        /// </summary>
        [Fact]
        public void IndexDefinition_SetInCreateMode_ShouldNotRestoreColumns()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsUnique = false,
                IsEditMode = false, // 創建模式
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            // 先添加一些可用欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("NAME");

            // 創建有有效 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "ID", Position = 1 }
                }
            };

            // Act - 設定 IndexDefinition
            var property = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            property?.SetValue(viewModel, indexDefinition);

            // Assert - 在創建模式下，不應該自動恢復欄位
            Assert.Equal(0, viewModel.SelectedColumns.Count);
            Assert.Equal(2, viewModel.AvailableColumns.Count);
            Assert.Contains("ID", viewModel.AvailableColumns);
            Assert.Contains("NAME", viewModel.AvailableColumns);
        }

        /// <summary>
        /// 測試當 IndexDefinition 沒有欄位時，不會觸發恢復
        /// </summary>
        [Fact]
        public void IndexDefinition_SetWithoutColumns_ShouldNotRestoreColumns()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                IndexName = "TEST_INDEX",
                IsUnique = false,
                IsEditMode = true, // 編輯模式
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            // 先添加一些可用欄位
            viewModel.AvailableColumns.Add("ID");
            viewModel.AvailableColumns.Add("NAME");

            // 創建沒有 Columns 的 IndexDefinition
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new List<IndexColumnDefinition>() // 空的欄位清單
            };

            // Act - 設定 IndexDefinition
            var property = typeof(IndexEditorViewModel).GetProperty("IndexDefinition");
            property?.SetValue(viewModel, indexDefinition);

            // Assert - 沒有欄位要恢復
            Assert.Equal(0, viewModel.SelectedColumns.Count);
            Assert.Equal(2, viewModel.AvailableColumns.Count);
        }
    }
}
