using Xunit;
using ICSharpCode.AvalonEdit.Document;
using ICSharpCode.AvalonEdit.Folding;
using OracleMS;
using System.Linq;

namespace OracleMS.Tests
{
    public class SqlFoldingStrategyTests
    {
        private readonly SqlFoldingStrategy _strategy;

        public SqlFoldingStrategyTests()
        {
            _strategy = new SqlFoldingStrategy();
        }

        [Fact]
        public void CreateNewFoldings_WithSimpleBeginEnd_ShouldCreateFolding()
        {
            // Arrange
            var text = @"CREATE PROCEDURE test_proc
AS
BEGIN
    SELECT * FROM users;
    UPDATE users SET name = 'test';
END;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);

            // Debug: Print all foldings
            foreach (var folding in foldings)
            {
                System.Diagnostics.Debug.WriteLine($"Folding: {folding.Name}, Start: {folding.StartOffset}, End: {folding.EndOffset}");
            }

            var beginEndFolding = foldings.FirstOrDefault(f => f.Name == "BEGIN...END");
            Assert.NotNull(beginEndFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithProcedure_ShouldCreateProcedureFolding()
        {
            // Arrange
            var text = @"CREATE OR REPLACE PROCEDURE test_procedure(p_id IN NUMBER)
IS
    v_name VARCHAR2(100);
BEGIN
    SELECT name INTO v_name FROM users WHERE id = p_id;
    DBMS_OUTPUT.PUT_LINE(v_name);
END test_procedure;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var procedureFolding = foldings.FirstOrDefault(f => f.Name.Contains("PROCEDURE"));
            Assert.NotNull(procedureFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithFunction_ShouldCreateFunctionFolding()
        {
            // Arrange
            var text = @"CREATE OR REPLACE FUNCTION get_user_name(p_id IN NUMBER)
RETURN VARCHAR2
IS
    v_name VARCHAR2(100);
BEGIN
    SELECT name INTO v_name FROM users WHERE id = p_id;
    RETURN v_name;
END get_user_name;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var functionFolding = foldings.FirstOrDefault(f => f.Name.Contains("FUNCTION"));
            Assert.NotNull(functionFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithPackage_ShouldCreatePackageFolding()
        {
            // Arrange
            var text = @"CREATE OR REPLACE PACKAGE test_package
AS
    PROCEDURE test_proc(p_id IN NUMBER);
    FUNCTION test_func(p_id IN NUMBER) RETURN VARCHAR2;
END test_package;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var packageFolding = foldings.FirstOrDefault(f => f.Name.Contains("PACKAGE"));
            Assert.NotNull(packageFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithTrigger_ShouldCreateTriggerFolding()
        {
            // Arrange
            var text = @"CREATE OR REPLACE TRIGGER user_audit_trigger
BEFORE INSERT OR UPDATE ON users
FOR EACH ROW
BEGIN
    :NEW.modified_date := SYSDATE;
    INSERT INTO audit_log (table_name, action, user_id) 
    VALUES ('users', 'MODIFY', :NEW.id);
END;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var triggerFolding = foldings.FirstOrDefault(f => f.Name.Contains("TRIGGER"));
            Assert.NotNull(triggerFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithComments_ShouldCreateCommentFolding()
        {
            // Arrange
            var text = @"/*
This is a multi-line comment
that should be foldable
*/
CREATE PROCEDURE test_proc
AS
BEGIN
    SELECT * FROM users;
END;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var commentFolding = foldings.FirstOrDefault(f => f.Name == "/* ... */");
            Assert.NotNull(commentFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithIfStatement_ShouldCreateIfFolding()
        {
            // Arrange
            var text = @"BEGIN
    IF p_id > 0 THEN
        SELECT name FROM users WHERE id = p_id;
        UPDATE users SET last_access = SYSDATE WHERE id = p_id;
    END IF;
END;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var ifFolding = foldings.FirstOrDefault(f => f.Name == "IF...END IF");
            Assert.NotNull(ifFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithCaseStatement_ShouldCreateCaseFolding()
        {
            // Arrange
            var text = @"BEGIN
    CASE p_type
        WHEN 'A' THEN
            SELECT * FROM table_a;
        WHEN 'B' THEN
            SELECT * FROM table_b;
        ELSE
            SELECT * FROM default_table;
    END CASE;
END;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var caseFolding = foldings.FirstOrDefault(f => f.Name == "CASE...END CASE");
            Assert.NotNull(caseFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithLoop_ShouldCreateLoopFolding()
        {
            // Arrange
            var text = @"BEGIN
    FOR i IN 1..10 LOOP
        DBMS_OUTPUT.PUT_LINE('Number: ' || i);
        INSERT INTO test_table VALUES (i);
    END LOOP;
END;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.NotEmpty(foldings);
            var loopFolding = foldings.FirstOrDefault(f => f.Name == "LOOP...END LOOP");
            Assert.NotNull(loopFolding);
        }

        [Fact]
        public void CreateNewFoldings_WithNestedStructures_ShouldCreateMultipleFoldings()
        {
            // Arrange
            var text = @"CREATE OR REPLACE PROCEDURE complex_proc(p_id IN NUMBER)
IS
    v_count NUMBER;
BEGIN
    IF p_id > 0 THEN
        FOR i IN 1..p_id LOOP
            SELECT COUNT(*) INTO v_count FROM users WHERE id = i;
            IF v_count > 0 THEN
                UPDATE users SET status = 'ACTIVE' WHERE id = i;
            END IF;
        END LOOP;
    END IF;
END complex_proc;";
            var document = new TextDocument(text);

            // Act
            var foldings = _strategy.CreateNewFoldings(document, out int firstErrorOffset);

            // Assert
            Assert.True(foldings.Count() >= 4); // PROCEDURE, BEGIN...END, IF, LOOP, nested IF
            Assert.Contains(foldings, f => f.Name.Contains("PROCEDURE"));
            Assert.Contains(foldings, f => f.Name == "IF...END IF");
            Assert.Contains(foldings, f => f.Name == "LOOP...END LOOP");
        }
    }
}
