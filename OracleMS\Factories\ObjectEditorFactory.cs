using System;
using System.Data;
using System.Windows.Controls;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using OracleMS.Views;

namespace OracleMS.Factories
{
    /// <summary>
    /// 資料庫物件編輯器工廠
    /// </summary>
    public static class ObjectEditorFactory
    {
        /// <summary>
        /// 創建物件編輯器
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        /// <returns>物件編輯器 UserControl</returns>
        public static UserControl CreateEditor(
            DatabaseObjectType objectType,
            string objectName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(objectName))
                throw new ArgumentException("物件名稱不能為空", nameof(objectName));

            if (databaseService == null)
                throw new ArgumentNullException(nameof(databaseService));

            if (scriptGeneratorService == null)
                throw new ArgumentNullException(nameof(scriptGeneratorService));

            if (objectEditorService == null)
                throw new ArgumentNullException(nameof(objectEditorService));

            if (getConnection == null)
                throw new ArgumentNullException(nameof(getConnection));

            // 根據物件類型創建對應的編輯器
            return objectType switch
            {
                DatabaseObjectType.Table => CreateTableEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.View => CreateViewEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.Procedure => CreateProcedureEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.Function => CreateFunctionEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.Package => CreatePackageEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.Sequence => CreateSequenceEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.Trigger => CreateTriggerEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                DatabaseObjectType.Index => CreateIndexEditor(objectName, databaseService, scriptGeneratorService, objectEditorService, getConnection, logger),
                _ => throw new NotSupportedException($"不支援的物件類型: {objectType}")
            };
        }

        /// <summary>
        /// 創建索引編輯器（使用IndexEditorInfo參數）
        /// </summary>
        /// <param name="indexInfo">索引編輯器參數</param>
        /// <param name="databaseService">資料庫服務</param>
        /// <param name="scriptGeneratorService">腳本產生服務</param>
        /// <param name="objectEditorService">物件編輯服務</param>
        /// <param name="getConnection">取得資料庫連線的函式</param>
        /// <param name="logger">日誌記錄器</param>
        /// <returns>索引編輯器 UserControl</returns>
        public static UserControl CreateIndexEditor(
            IndexEditorInfo indexInfo,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            if (indexInfo == null)
                throw new ArgumentNullException(nameof(indexInfo));

            if (databaseService == null)
                throw new ArgumentNullException(nameof(databaseService));

            if (scriptGeneratorService == null)
                throw new ArgumentNullException(nameof(scriptGeneratorService));

            if (objectEditorService == null)
                throw new ArgumentNullException(nameof(objectEditorService));

            if (getConnection == null)
                throw new ArgumentNullException(nameof(getConnection));

            // 創建 IndexEditorViewModel
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            // 創建 IndexEditorView 並設置 DataContext
            var indexEditor = new IndexEditorView
            {
                DataContext = viewModel
            };

            return indexEditor;
        }

        /// <summary>
        /// 取得編輯器標題
        /// </summary>
        /// <param name="objectType">物件類型</param>
        /// <param name="objectName">物件名稱</param>
        /// <returns>編輯器標題</returns>
        public static string GetEditorTitle(DatabaseObjectType objectType, string objectName)
        {
            var typeText = objectType switch
            {
                DatabaseObjectType.Table => "Tables",
                DatabaseObjectType.View => "Views",
                DatabaseObjectType.Procedure => "Procedures",
                DatabaseObjectType.Function => "Functions",
                DatabaseObjectType.Package => "Packages",
                DatabaseObjectType.Sequence => "Sequences",
                DatabaseObjectType.Trigger => "Triggers",
                DatabaseObjectType.Index => "Indexes",
                _ => "Objects"
            };

            return $"{typeText}: {objectName}";
        }

        /// <summary>
        /// 創建資料表編輯器
        /// </summary>
        private static UserControl CreateTableEditor(
            string tableName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            var viewModel = new TableEditorViewModel(
                tableName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            var view = new TableEditorView
            {
                DataContext = viewModel
            };

            return view;
        }

        /// <summary>
        /// 創建檢視表編輯器
        /// </summary>
        private static UserControl CreateViewEditor(
            string viewName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            // 創建 ViewEditorViewModel
            var viewModel = new ViewEditorViewModel(
                viewName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            // 創建 ViewEditorView 並設置 DataContext
            var viewEditor = new ViewEditorView
            {
                DataContext = viewModel
            };

            return viewEditor;
        }

        /// <summary>
        /// 創建預存程序編輯器
        /// </summary>
        private static UserControl CreateProcedureEditor(
            string procedureName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            var viewModel = new ProcedureEditorViewModel(
                procedureName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            var view = new ProcedureEditorView
            {
                DataContext = viewModel
            };

            return view;
        }

        /// <summary>
        /// 創建函數編輯器
        /// </summary>
        private static UserControl CreateFunctionEditor(
            string functionName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            var viewModel = new FunctionEditorViewModel(
                functionName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            var view = new FunctionEditorView
            {
                DataContext = viewModel
            };

            return view;
        }

        /// <summary>
        /// 創建套件編輯器
        /// </summary>
        private static UserControl CreatePackageEditor(
            string packageName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            // 創建 PackageEditorViewModel
            var viewModel = new PackageEditorViewModel(
                packageName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            // 創建 PackageEditorView 並設置 DataContext
            var packageEditor = new PackageEditorView
            {
                DataContext = viewModel
            };

            return packageEditor;
        }

        /// <summary>
        /// 創建序列編輯器
        /// </summary>
        private static UserControl CreateSequenceEditor(
            string sequenceName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            var viewModel = new SequenceEditorViewModel(
                sequenceName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            var view = new SequenceEditorView
            {
                DataContext = viewModel
            };

            return view;
        }

        /// <summary>
        /// 創建觸發器編輯器
        /// </summary>
        private static UserControl CreateTriggerEditor(
            string triggerName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            // 創建 TriggerEditorViewModel
            var viewModel = new TriggerEditorViewModel(
                triggerName,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            // 創建 TriggerEditorView 並設置 DataContext
            var triggerEditor = new TriggerEditorView
            {
                DataContext = viewModel
            };

            return triggerEditor;
        }

        /// <summary>
        /// 創建索引編輯器
        /// </summary>
        private static UserControl CreateIndexEditor(
            string indexName,
            IDatabaseService databaseService,
            IScriptGeneratorService scriptGeneratorService,
            IObjectEditorService objectEditorService,
            Func<IDbConnection?> getConnection,
            ILogger logger)
        {
            // 創建 IndexEditorInfo 用於編輯現有索引
            // 注意：在編輯模式下，Schema 和 TableName 會在 IndexEditorViewModel 初始化時
            // 通過 GetIndexDefinitionAsync 方法從資料庫載入
            var indexInfo = new IndexEditorInfo
            {
                IndexName = indexName,
                IsEditMode = true // 從ObjectExplorer開啟的是編輯模式
            };

            // 創建 IndexEditorViewModel
            var viewModel = new IndexEditorViewModel(
                indexInfo,
                databaseService,
                scriptGeneratorService,
                objectEditorService,
                getConnection,
                logger);

            // 創建 IndexEditorView 並設置 DataContext
            var indexEditor = new IndexEditorView
            {
                DataContext = viewModel
            };

            return indexEditor;
        }
    }
}