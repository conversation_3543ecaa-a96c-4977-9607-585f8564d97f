<?xml version="1.0"?>
<SyntaxDefinition name="SQL" extensions=".sql" xmlns="http://icsharpcode.net/sharpdevelop/syntaxdefinition/2008">
  <Color name="Comment" foreground="#008000" />
  <Color name="String" foreground="#A31515" />
  <Color name="Keyword" foreground="#0000FF" fontWeight="bold" />
  <Color name="Function" foreground="#FF8C00" fontWeight="bold" />
  <Color name="DataType" foreground="#2B91AF" fontWeight="bold" />
  <Color name="Operator" foreground="#8B008B" />
  <Color name="Number" foreground="#FF0000" />

  <RuleSet ignoreCase="true">
    <!-- Comments -->
    <Span color="Comment" begin="--" />
    <Span color="Comment" multiline="true" begin="/\*" end="\*/" />
    
    <!-- Strings -->
    <Span color="String">
      <Begin>'</Begin>
      <End>'</End>
      <RuleSet>
        <Span begin="''" end="" />
      </RuleSet>
    </Span>
    
    <!-- Numbers -->
    <Rule color="Number">
      \b0[xX][0-9a-fA-F]+|(\b\d+(\.[0-9]+)?([eE][+-]?[0-9]+)?)\b
    </Rule>
    
    <!-- SQL Keywords -->
    <Keywords color="Keyword">
      <Word>SELECT</Word>
      <Word>FROM</Word>
      <Word>WHERE</Word>
      <Word>INSERT</Word>
      <Word>UPDATE</Word>
      <Word>DELETE</Word>
      <Word>CREATE</Word>
	  <Word>TRUNCATE</Word>
	  <Word>DROP</Word>
      <Word>ALTER</Word>
      <Word>TABLE</Word>
      <Word>INDEX</Word>
      <Word>VIEW</Word>
      <Word>PROCEDURE</Word>
      <Word>FUNCTION</Word>
      <Word>TRIGGER</Word>
      <Word>DATABASE</Word>
      <Word>SCHEMA</Word>
      <Word>CONSTRAINT</Word>
      <Word>PRIMARY</Word>
      <Word>FOREIGN</Word>
      <Word>KEY</Word>
      <Word>UNIQUE</Word>
      <Word>NOT</Word>
      <Word>NULL</Word>
      <Word>DEFAULT</Word>
      <Word>CHECK</Word>
      <Word>REFERENCES</Word>
      <Word>ON</Word>
      <Word>CASCADE</Word>
      <Word>RESTRICT</Word>
      <Word>SET</Word>
      <Word>ACTION</Word>
      <Word>INNER</Word>
      <Word>LEFT</Word>
      <Word>RIGHT</Word>
      <Word>FULL</Word>
      <Word>OUTER</Word>
      <Word>JOIN</Word>
      <Word>UNION</Word>
      <Word>ALL</Word>
      <Word>DISTINCT</Word>
      <Word>ORDER</Word>
      <Word>BY</Word>
      <Word>GROUP</Word>
      <Word>HAVING</Word>
      <Word>AS</Word>
      <Word>ASC</Word>
      <Word>DESC</Word>
      <Word>LIMIT</Word>
      <Word>OFFSET</Word>
      <Word>TOP</Word>
      <Word>WITH</Word>
      <Word>CASE</Word>
      <Word>WHEN</Word>
      <Word>THEN</Word>
      <Word>ELSE</Word>
      <Word>END</Word>
      <Word>IF</Word>
      <Word>EXISTS</Word>
      <Word>IN</Word>
      <Word>BETWEEN</Word>
      <Word>LIKE</Word>
      <Word>IS</Word>
      <Word>AND</Word>
      <Word>OR</Word>
      <Word>BEGIN</Word>
      <Word>COMMIT</Word>
      <Word>ROLLBACK</Word>
      <Word>TRANSACTION</Word>
      <Word>GRANT</Word>
      <Word>REVOKE</Word>
      <Word>DECLARE</Word>
      <Word>CURSOR</Word>
      <Word>OPEN</Word>
      <Word>FETCH</Word>
      <Word>CLOSE</Word>
      <Word>DEALLOCATE</Word>
      <Word>EXEC</Word>
      <Word>EXECUTE</Word>
      <Word>RETURN</Word>
      <Word>WHILE</Word>
      <Word>FOR</Word>
      <Word>LOOP</Word>
      <Word>BREAK</Word>
      <Word>CONTINUE</Word>
      <Word>GOTO</Word>
      <Word>LABEL</Word>
      <Word>TRY</Word>
      <Word>CATCH</Word>
      <Word>THROW</Word>
      <Word>RAISERROR</Word>
    </Keywords>
    
    <!-- Oracle Specific Keywords -->
    <Keywords color="Keyword">
      <Word>CONNECT</Word>
      <Word>START</Word>
      <Word>PRIOR</Word>
      <Word>LEVEL</Word>
      <Word>ROWNUM</Word>
      <Word>ROWID</Word>
      <Word>DUAL</Word>
      <Word>SYSDATE</Word>
      <Word>SYSTIMESTAMP</Word>
      <Word>USER</Word>
      <Word>UID</Word>
      <Word>USERENV</Word>
      <Word>SEQUENCE</Word>
      <Word>NEXTVAL</Word>
      <Word>CURRVAL</Word>
      <Word>SYNONYM</Word>
      <Word>PACKAGE</Word>
      <Word>BODY</Word>
      <Word>TYPE</Word>
      <Word>OBJECT</Word>
      <Word>VARRAY</Word>
      <Word>NESTED</Word>
      <Word>REF</Word>
      <Word>CURSOR</Word>
      <Word>EXCEPTION</Word>
      <Word>PRAGMA</Word>
      <Word>AUTONOMOUS_TRANSACTION</Word>
      <Word>BULK</Word>
      <Word>COLLECT</Word>
      <Word>FORALL</Word>
      <Word>SAVE</Word>
      <Word>EXCEPTIONS</Word>
      <Word>MERGE</Word>
      <Word>USING</Word>
      <Word>MATCHED</Word>
      <Word>WHEN</Word>
      <Word>THEN</Word>
      <Word>UPSERT</Word>
    </Keywords>
    
    <!-- Data Types -->
    <Keywords color="DataType">
      <Word>VARCHAR</Word>
      <Word>VARCHAR2</Word>
      <Word>CHAR</Word>
      <Word>NCHAR</Word>
      <Word>NVARCHAR2</Word>
      <Word>CLOB</Word>
      <Word>NCLOB</Word>
      <Word>BLOB</Word>
      <Word>BFILE</Word>
      <Word>NUMBER</Word>
      <Word>INTEGER</Word>
      <Word>INT</Word>
      <Word>SMALLINT</Word>
      <Word>DECIMAL</Word>
      <Word>NUMERIC</Word>
      <Word>FLOAT</Word>
      <Word>REAL</Word>
      <Word>DOUBLE</Word>
      <Word>PRECISION</Word>
      <Word>DATE</Word>
      <Word>TIMESTAMP</Word>
      <Word>INTERVAL</Word>
      <Word>YEAR</Word>
      <Word>MONTH</Word>
      <Word>DAY</Word>
      <Word>HOUR</Word>
      <Word>MINUTE</Word>
      <Word>SECOND</Word>
      <Word>TIMEZONE</Word>
      <Word>LOCAL</Word>
      <Word>RAW</Word>
      <Word>LONG</Word>
      <Word>ROWID</Word>
      <Word>UROWID</Word>
      <Word>XMLTYPE</Word>
      <Word>BOOLEAN</Word>
      <Word>BINARY_INTEGER</Word>
      <Word>PLS_INTEGER</Word>
      <Word>BINARY_FLOAT</Word>
      <Word>BINARY_DOUBLE</Word>
    </Keywords>
    
    <!-- Built-in Functions -->
    <Keywords color="Function">
      <Word>COUNT</Word>
      <Word>SUM</Word>
      <Word>AVG</Word>
      <Word>MIN</Word>
      <Word>MAX</Word>
      <Word>STDDEV</Word>
      <Word>VARIANCE</Word>
      <Word>UPPER</Word>
      <Word>LOWER</Word>
      <Word>INITCAP</Word>
      <Word>LENGTH</Word>
      <Word>SUBSTR</Word>
      <Word>INSTR</Word>
      <Word>REPLACE</Word>
      <Word>TRANSLATE</Word>
      <Word>TRIM</Word>
      <Word>LTRIM</Word>
      <Word>RTRIM</Word>
      <Word>LPAD</Word>
      <Word>RPAD</Word>
      <Word>CONCAT</Word>
      <Word>CHR</Word>
      <Word>ASCII</Word>
      <Word>TO_CHAR</Word>
      <Word>TO_NUMBER</Word>
      <Word>TO_DATE</Word>
      <Word>CAST</Word>
      <Word>CONVERT</Word>
      <Word>DECODE</Word>
      <Word>NVL</Word>
      <Word>NVL2</Word>
      <Word>NULLIF</Word>
      <Word>COALESCE</Word>
      <Word>GREATEST</Word>
      <Word>LEAST</Word>
      <Word>ABS</Word>
      <Word>CEIL</Word>
      <Word>FLOOR</Word>
      <Word>ROUND</Word>
      <Word>TRUNC</Word>
      <Word>MOD</Word>
      <Word>POWER</Word>
      <Word>SQRT</Word>
      <Word>EXP</Word>
      <Word>LN</Word>
      <Word>LOG</Word>
      <Word>SIN</Word>
      <Word>COS</Word>
      <Word>TAN</Word>
      <Word>ASIN</Word>
      <Word>ACOS</Word>
      <Word>ATAN</Word>
      <Word>ATAN2</Word>
      <Word>SIGN</Word>
      <Word>ADD_MONTHS</Word>
      <Word>MONTHS_BETWEEN</Word>
      <Word>NEXT_DAY</Word>
      <Word>LAST_DAY</Word>
      <Word>EXTRACT</Word>
      <Word>CURRENT_DATE</Word>
      <Word>CURRENT_TIMESTAMP</Word>
      <Word>LOCALTIMESTAMP</Word>
      <Word>DBTIMEZONE</Word>
      <Word>SESSIONTIMEZONE</Word>
      <Word>SYS_CONTEXT</Word>
      <Word>USER</Word>
      <Word>UID</Word>
      <Word>USERENV</Word>
      <Word>ROW_NUMBER</Word>
      <Word>RANK</Word>
      <Word>DENSE_RANK</Word>
      <Word>FIRST_VALUE</Word>
      <Word>LAST_VALUE</Word>
      <Word>LAG</Word>
      <Word>LEAD</Word>
      <Word>LISTAGG</Word>
      <Word>XMLAGG</Word>
      <Word>XMLELEMENT</Word>
      <Word>XMLFOREST</Word>
      <Word>XMLATTRIBUTES</Word>
      <Word>EXTRACTVALUE</Word>
      <Word>XMLQUERY</Word>
      <Word>XMLTABLE</Word>
      <Word>REGEXP_LIKE</Word>
      <Word>REGEXP_REPLACE</Word>
      <Word>REGEXP_SUBSTR</Word>
      <Word>REGEXP_INSTR</Word>
      <Word>REGEXP_COUNT</Word>
    </Keywords>
    
    <!-- Operators -->
    <Rule color="Operator">
      [+\-*/=&lt;&gt;!&amp;|^~%]|(&lt;=)|(&gt;=)|(&lt;&gt;)|(!=)
    </Rule>
  </RuleSet>
</SyntaxDefinition>
