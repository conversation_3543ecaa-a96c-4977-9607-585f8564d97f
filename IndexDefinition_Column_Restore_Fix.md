# IndexDefinition 欄位恢復問題修復

## 問題描述

在 `IndexEditorViewModel.cs` 的第 1110 行，`IndexDefinition` 已經成功取得索引欄位資料，但是這些欄位沒有顯示在 `SelectedColumnsListBox` 中。

## 問題分析

原本的程式碼流程：
1. 在 `LoadObjectAsync` 方法中，第 1110 行設定 `IndexDefinition = definition`
2. 然後在背景執行 `LoadRelatedDataInBackground` 方法
3. 這個方法會呼叫 `LoadColumnsForTableAsync`
4. `LoadColumnsForTableAsync` 會呼叫 `UpdateColumnsUI`
5. 在 `UpdateColumnsUI` 中，會檢查是否為編輯模式並恢復索引欄位

**問題根源**：存在時序問題。當 `IndexDefinition` 被設定時，可用欄位可能還沒有載入，所以無法立即恢復索引欄位。而當可用欄位載入完成後，`IndexDefinition` 已經設定，但沒有觸發欄位恢復的機制。

## 解決方案

### 1. 修改 `IndexDefinition` 屬性的 setter

在 `IndexDefinition` 屬性的 setter 中添加邏輯，當 `IndexDefinition` 被設定時，如果是編輯模式且有欄位資料，嘗試立即恢復索引欄位。

```csharp
public IndexDefinition IndexDefinition
{
    get => _indexDefinition;
    private set
    {
        if (SetProperty(ref _indexDefinition, value))
        {
            NotifyCanExecuteChanged();
            
            // 當 IndexDefinition 被設定時，如果是編輯模式且有欄位資料，嘗試恢復索引欄位
            if (IsEditMode && value?.Columns?.Any() == true)
            {
                _logger.LogInformation("IndexDefinition 已設定，欄位數量: {Count}，可用欄位數量: {AvailableCount}", 
                    value.Columns.Count, _columnSelectionManager.AvailableColumns.Count);
                
                // 如果可用欄位已經載入，立即恢復索引欄位
                if (_columnSelectionManager.AvailableColumns.Any())
                {
                    _logger.LogInformation("可用欄位已載入，立即恢復索引欄位");
                    RestoreIndexColumnsToSelected();
                }
                else
                {
                    _logger.LogInformation("可用欄位尚未載入，將在欄位載入完成後恢復");
                }
            }
        }
    }
}
```

### 2. 創建 `RestoreIndexColumnsToSelected` 方法

創建一個專門的方法來處理索引欄位恢復，避免程式碼重複：

```csharp
/// <summary>
/// 恢復索引欄位到已選欄位清單
/// </summary>
private void RestoreIndexColumnsToSelected()
{
    if (IndexDefinition?.Columns?.Any() != true)
        return;

    _logger.LogInformation("開始恢復索引欄位到已選清單，欄位數量: {Count}", IndexDefinition.Columns.Count);

    // 按照原始順序恢復已選欄位
    var orderedColumns = IndexDefinition.Columns.OrderBy(c => c.Position).Select(c => c.ColumnName);
    foreach (var columnName in orderedColumns)
    {
        if (_columnSelectionManager.AvailableColumns.Contains(columnName))
        {
            _logger.LogInformation("恢復欄位 {ColumnName} 到已選清單", columnName);
            _columnSelectionManager.MoveToSelected(columnName);
        }
        else
        {
            _logger.LogWarning("欄位 {ColumnName} 不在可用清單中，無法恢復", columnName);
        }
    }

    // 更新 IndexDefinition 的 Columns（確保同步）
    UpdateIndexDefinitionColumns();
    
    _logger.LogInformation("索引欄位恢復完成，已選欄位數量: {Count}", SelectedColumns.Count);
}
```

### 3. 修改 `UpdateColumnsUI` 方法

修改 `UpdateColumnsUI` 方法，使其使用新的 `RestoreIndexColumnsToSelected` 方法：

```csharp
// 如果是編輯模式且有預設的欄位，將它們移到已選欄位
if (IsEditMode && IndexDefinition?.Columns?.Any() == true)
{
    _logger.LogInformation("編輯模式：在 UpdateColumnsUI 中恢復索引欄位");
    RestoreIndexColumnsToSelected();
}
```

## 修改的檔案

- `OracleMS\ViewModels\IndexEditorViewModel.cs`
  - 修改 `IndexDefinition` 屬性的 setter（第 57-77 行）
  - 新增 `RestoreIndexColumnsToSelected` 方法（第 617-646 行）
  - 修改 `UpdateColumnsUI` 方法（第 1056-1061 行）

## 解決的問題

1. **時序問題**：無論 `IndexDefinition` 和可用欄位哪個先載入，都能正確恢復索引欄位
2. **程式碼重複**：統一使用 `RestoreIndexColumnsToSelected` 方法處理欄位恢復
3. **日誌記錄**：添加詳細的日誌記錄，便於除錯和監控

## 測試場景

修改後的程式碼能夠處理以下場景：

1. **IndexDefinition 先設定，可用欄位後載入**：在 `UpdateColumnsUI` 中恢復欄位
2. **可用欄位先載入，IndexDefinition 後設定**：在 `IndexDefinition` setter 中立即恢復欄位
3. **創建模式**：不會觸發自動恢復，保持原有行為
4. **沒有欄位資料**：不會執行恢復邏輯

## 向後相容性

這些修改完全向後相容，不會影響現有的功能：
- 創建模式的行為保持不變
- 所有現有的欄位操作命令仍然正常工作
- 只是增強了編輯模式下的欄位恢復功能
