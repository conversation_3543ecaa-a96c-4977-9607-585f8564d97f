# 查詢交易上下文修正

## 問題描述

您發現了一個關鍵問題：

### 執行流程分析
1. **執行 UPDATE 時**：會在 `ExecuteNonQueryInTransactionAsync` 中執行 `await Task.Run(() => command.ExecuteNonQuery())`，command 有正確的交易上下文
2. **創建第二個 QueryEditor 執行 SELECT 時**：會在 `ExecuteQueryAsync` (line 68) 調用 `_databaseRepository.ExecuteQueryAsync(connection, sql)` (line 91)
3. **問題**：`_databaseRepository.ExecuteQueryAsync` 沒有交易上下文，導致無法看到未提交的變更

### 根本原因
```csharp
// 問題：直接調用 repository，沒有交易上下文
public async Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql)
{
    // ...
    var result = await _databaseRepository.ExecuteQueryAsync(connection, sql); // 沒有交易！
    // ...
}
```

## 解決方案

### 1. 創建支援交易的查詢方法

新增 `ExecuteQueryWithCancellationAndTransactionAsync` 方法：

```csharp
private async Task<DataTable> ExecuteQueryWithCancellationAndTransactionAsync(
    IDbConnection connection, 
    string sql, 
    IDbTransaction? transaction, 
    CancellationToken cancellationToken)
{
    // 直接創建 command，不使用 repository，以便支援交易
    using var command = connection.CreateCommand();
    command.CommandText = sql;
    command.CommandTimeout = 300;
    
    // 關鍵：如果有交易，指派給 command
    if (transaction != null)
    {
        command.Transaction = transaction;
    }

    // 執行查詢並支援取消...
}
```

### 2. 修改交易感知的執行邏輯

```csharp
private async Task<DataTable> ExecuteQueryWithTransactionAsync(
    IDbConnection connection,
    string sql,
    ITransactionManager transactionManager,
    CancellationToken cancellationToken)
{
    // 使用支援交易的查詢方法
    var transaction = transactionManager.HasActiveTransaction ? transactionManager.CurrentTransaction : null;
    return await ExecuteQueryWithCancellationAndTransactionAsync(connection, sql, transaction, cancellationToken);
}
```

### 3. 保持原有 API 不變

`ExecuteQueryAsync` 方法保持不變，繼續使用 repository，確保向後相容性：

```csharp
public async Task<DataTable> ExecuteQueryAsync(IDbConnection connection, string sql)
{
    // 保持原有實現，用於不需要交易的場景
    var result = await _databaseRepository.ExecuteQueryAsync(connection, sql);
    return result;
}
```

## 修正效果

### 之前的行為
- UPDATE 在交易中執行（有 `command.Transaction`）
- SELECT 通過 repository 執行（沒有 `command.Transaction`）
- 兩者在不同的交易上下文中，無法看到未提交的變更

### 修正後的行為
- **UPDATE**：在交易中執行（`ExecuteNonQueryInTransactionAsync`）
- **SELECT**：也在同一個交易中執行（`ExecuteQueryWithCancellationAndTransactionAsync`）
- **交易共享**：兩者在相同的交易上下文中，可以看到未提交的變更

## 關鍵程式碼

### 交易指派的核心邏輯
```csharp
// 在 ExecuteQueryWithCancellationAndTransactionAsync 中
using var command = connection.CreateCommand();
command.CommandText = sql;
command.CommandTimeout = 300;

// 關鍵：如果有交易，指派給 command
if (transaction != null)
{
    command.Transaction = transaction;
}
```

### 執行流程
```
ExecuteSqlWithTransactionAsync
├── 檢查是否為 SELECT 查詢
├── 如果是 SELECT：
│   └── ExecuteQueryWithTransactionAsync
│       └── ExecuteQueryWithCancellationAndTransactionAsync
│           └── command.Transaction = transaction (關鍵！)
└── 如果是非查詢：
    └── ExecuteNonQueryWithTransactionAsync
        └── ExecuteNonQueryInTransactionAsync
            └── command.Transaction = transaction (關鍵！)
```

## 測試場景

### 完整的交易共享測試
1. **創建 DbSession**，第一個 QueryEditor 被創建
2. **執行 UPDATE**：`UPDATE test_table SET name = 'updated' WHERE id = 1`（不 commit）
3. **創建第二個 QueryEditor**
4. **執行 SELECT**：`SELECT * FROM test_table WHERE id = 1`
5. **預期結果**：應該看到 `name = 'updated'`（未提交的變更）

### 驗證點
- ✅ UPDATE 的 command 有正確的交易上下文
- ✅ SELECT 的 command 也有相同的交易上下文
- ✅ 兩個 QueryEditor 可以看到彼此的未提交變更
- ✅ 交易狀態在所有 QueryEditor 之間同步

## 相關檔案

- `OracleMS\Services\DatabaseService.cs` - 主要修改
- `OracleMS\Repositories\OracleDatabaseRepository.cs` - 原始問題所在（間接修正）
- `OracleMS\Interfaces\ITransactionManager.cs` - 使用 CurrentTransaction 屬性

這個修正確保了所有 SQL 命令（包括 SELECT 查詢）都在正確的交易上下文中執行，實現了真正的交易共享功能。
