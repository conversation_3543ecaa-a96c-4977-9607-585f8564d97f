<UserControl x:Class="OracleMS.Views.TableEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:controls="clr-namespace:OracleMS.Views.Controls"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:TableEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Oracle Data Types Collection -->
        <x:Array x:Key="OracleDataTypes" Type="sys:String" xmlns:sys="clr-namespace:System;assembly=mscorlib">
            <sys:String>VARCHAR2</sys:String>
            <sys:String>NVARCHAR2</sys:String>
            <sys:String>CHAR</sys:String>
            <sys:String>NCHAR</sys:String>
            <sys:String>NUMBER</sys:String>
            <sys:String>DATE</sys:String>
            <sys:String>TIMESTAMP</sys:String>
            <sys:String>TIMESTAMP WITH TIME ZONE</sys:String>
            <sys:String>TIMESTAMP WITH LOCAL TIME ZONE</sys:String>
            <sys:String>INTERVAL YEAR TO MONTH</sys:String>
            <sys:String>INTERVAL DAY TO SECOND</sys:String>
            <sys:String>BINARY_FLOAT</sys:String>
            <sys:String>BINARY_DOUBLE</sys:String>
            <sys:String>FLOAT</sys:String>
            <sys:String>LONG</sys:String>
            <sys:String>RAW</sys:String>
            <sys:String>LONG RAW</sys:String>
            <sys:String>BLOB</sys:String>
            <sys:String>CLOB</sys:String>
            <sys:String>NCLOB</sys:String>
            <sys:String>BFILE</sys:String>
            <sys:String>ROWID</sys:String>
            <sys:String>UROWID</sys:String>
            <sys:String>XMLType</sys:String>
        </x:Array>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MinWidth" Value="75"/>
            <Setter Property="Height" Value="28"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/> <!-- Content Area -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Command="{Binding SaveCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="儲存資料表變更 (Ctrl+S)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" Margin="0,0,4,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding RefreshCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重新載入資料表定義">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" Margin="0,0,4,0"/>
                        <TextBlock Text="重新載入"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding GenerateScriptCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="產生 DDL 腳本">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" Margin="0,0,4,0"/>
                        <TextBlock Text="產生腳本"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <!-- Progress indicator -->
            <ProgressBar Width="100" Height="16" 
                         IsIndeterminate="True"
                         Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                         Margin="10,0"/>
        </ToolBar>

        <!-- Progress Indicator -->
        <controls:ProgressIndicatorControl Grid.Row="1" 
                                          HorizontalAlignment="Center" 
                                          VerticalAlignment="Center" 
                                          Panel.ZIndex="100" 
                                          Width="300"/>
                                          
        <!-- Content Area -->
        <TabControl Grid.Row="1">
            <!-- Columns Tab -->
            <TabItem Header="欄位">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Column Toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Command="{Binding AddColumnCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="新增欄位">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➕" Margin="0,0,4,0"/>
                                    <TextBlock Text="新增欄位"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding DeleteColumnCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=ColumnsDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="刪除選取的欄位">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➖" Margin="0,0,4,0"/>
                                    <TextBlock Text="刪除欄位"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding MoveColumnUpCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=ColumnsDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="上移欄位">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⬆️" Margin="0,0,4,0"/>
                                    <TextBlock Text="上移"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding MoveColumnDownCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=ColumnsDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="下移欄位">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⬇️" Margin="0,0,4,0"/>
                                    <TextBlock Text="下移"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Columns DataGrid -->
                    <DataGrid x:Name="ColumnsDataGrid"
                              Grid.Row="1"
                              ItemsSource="{Binding TableDefinition.Columns}"
                              SelectedItem="{Binding SelectedColumn}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="False"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              AlternatingRowBackground="#eeeeee"
                              SelectionMode="Single"
                              SelectionUnit="FullRow">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="欄位名稱" 
                                                Binding="{Binding Name}" 
                                                Width="150"/>
                            <DataGridTemplateColumn Header="資料類型" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding DataType}" VerticalAlignment="Center" Margin="5,0"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                                <DataGridTemplateColumn.CellEditingTemplate>
                                    <DataTemplate>
                                        <ComboBox ItemsSource="{StaticResource OracleDataTypes}"
                                                  SelectedItem="{Binding DataType, UpdateSourceTrigger=PropertyChanged}"
                                                  IsEditable="False"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellEditingTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTextColumn Header="長度" 
                                                Binding="{Binding Length}" 
                                                Width="60"/>
                            <DataGridTextColumn Header="精度" 
                                                Binding="{Binding Precision}" 
                                                Width="60"/>
                            <DataGridTextColumn Header="小數位數" 
                                                Binding="{Binding Scale}" 
                                                Width="80"/>
                            <DataGridCheckBoxColumn Header="可為空值" 
                                                    Binding="{Binding IsNullable}" 
                                                    Width="80"/>
                            <DataGridTextColumn Header="預設值" 
                                                Binding="{Binding DefaultValue}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="備註" 
                                                Binding="{Binding Comments}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Indexes Tab -->
            <TabItem Header="索引">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Index Toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Command="{Binding AddIndexCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="新增索引">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➕" Margin="0,0,4,0"/>
                                    <TextBlock Text="新增索引"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding DeleteIndexCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=IndexesDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="刪除選取的索引">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➖" Margin="0,0,4,0"/>
                                    <TextBlock Text="刪除索引"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding EditIndexCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=IndexesDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="編輯選取的索引">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="✏️" Margin="0,0,4,0"/>
                                    <TextBlock Text="編輯索引"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Indexes DataGrid -->
                    <DataGrid x:Name="IndexesDataGrid"
                              Grid.Row="1"
                              ItemsSource="{Binding TableDefinition.Indexes}"
                              SelectedItem="{Binding SelectedIndex}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="False"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              AlternatingRowBackground="#eeeeee"
                              SelectionMode="Single"
                              SelectionUnit="FullRow"
                              IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="索引名稱" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            <DataGridCheckBoxColumn Header="唯一性" 
                                                    Binding="{Binding IsUnique}" 
                                                    Width="80"/>
                            <DataGridTextColumn Header="欄位" 
                                                Binding="{Binding ColumnList}" 
                                                Width="*"/>
                            <DataGridTextColumn Header="類型" 
                                                Binding="{Binding IndexType}" 
                                                Width="100"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Constraints Tab -->
            <TabItem Header="約束條件">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Constraint Toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Command="{Binding AddConstraintCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="新增約束條件">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➕" Margin="0,0,4,0"/>
                                    <TextBlock Text="新增約束條件"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding DeleteConstraintCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=ConstraintsDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="刪除選取的約束條件">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➖" Margin="0,0,4,0"/>
                                    <TextBlock Text="刪除約束條件"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding EditConstraintCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=ConstraintsDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="編輯選取的約束條件">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="✏️" Margin="0,0,4,0"/>
                                    <TextBlock Text="編輯約束條件"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Constraints DataGrid -->
                    <DataGrid x:Name="ConstraintsDataGrid"
                              Grid.Row="1"
                              ItemsSource="{Binding TableDefinition.Constraints}"
                              SelectedItem="{Binding SelectedConstraint}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="False"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              AlternatingRowBackground="#eeeeee"
                              SelectionMode="Single"
                              SelectionUnit="FullRow">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="約束條件名稱" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            <DataGridTextColumn Header="類型" 
                                                Binding="{Binding Type}" 
                                                Width="100"/>
                            <DataGridTextColumn Header="欄位" 
                                                Binding="{Binding ColumnList}" 
                                                Width="200"/>
                            <DataGridTextColumn Header="參考表" 
                                                Binding="{Binding ReferencedTable}" 
                                                Width="150"/>
                            <DataGridTextColumn Header="參考欄位" 
                                                Binding="{Binding ReferencedColumns}" 
                                                Width="150"/>
                            <DataGridTextColumn Header="條件" 
                                                Binding="{Binding Condition}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Triggers Tab -->
            <TabItem Header="觸發器">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Trigger Toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Command="{Binding AddTriggerCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="新增觸發器">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➕" Margin="0,0,4,0"/>
                                    <TextBlock Text="新增觸發器"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding DeleteTriggerCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=TriggersDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="刪除選取的觸發器">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="➖" Margin="0,0,4,0"/>
                                    <TextBlock Text="刪除觸發器"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding EditTriggerCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=TriggersDataGrid}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="編輯選取的觸發器">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="✏️" Margin="0,0,4,0"/>
                                    <TextBlock Text="編輯觸發器"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Triggers DataGrid -->
                    <DataGrid x:Name="TriggersDataGrid"
                              Grid.Row="1"
                              ItemsSource="{Binding TableDefinition.Triggers}"
                              SelectedItem="{Binding SelectedTrigger}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="False"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              AlternatingRowBackground="#eeeeee"
                              SelectionMode="Single"
                              SelectionUnit="FullRow">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="觸發器名稱" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            <DataGridTextColumn Header="觸發事件" 
                                                Binding="{Binding TriggerEvent}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="觸發時機" 
                                                Binding="{Binding TriggerTiming}" 
                                                Width="120"/>
                            <DataGridCheckBoxColumn Header="啟用狀態" 
                                                    Binding="{Binding IsEnabled}" 
                                                    Width="80"/>
                            <DataGridTextColumn Header="描述" 
                                                Binding="{Binding Description}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- DDL Preview Tab -->
            <TabItem Header="DDL 預覽">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- DDL Toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Command="{Binding CopyDdlCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="複製 DDL 腳本">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="📋" Margin="0,0,4,0"/>
                                    <TextBlock Text="複製腳本"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding SaveDdlCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="儲存 DDL 腳本至檔案">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="💾" Margin="0,0,4,0"/>
                                    <TextBlock Text="儲存腳本"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>

                        <Button Command="{Binding ExecuteDdlCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="執行 DDL 腳本">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="▶" Margin="0,0,4,0" FontWeight="Bold" Foreground="Green"/>
                                    <TextBlock Text="執行腳本"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- DDL Preview Editor -->
                    <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
                        <avalonedit:TextEditor x:Name="DdlPreviewEditor"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               ShowLineNumbers="True"
                                               WordWrap="False"
                                               IsReadOnly="True"
                                               HorizontalScrollBarVisibility="Auto"
                                               VerticalScrollBarVisibility="Auto"
                                               Background="White"
                                               Foreground="Black">
                            <avalonedit:TextEditor.Options>
                                <avalonedit:TextEditorOptions ShowSpaces="False"
                                                              ShowTabs="False"
                                                              ShowEndOfLine="False"
                                                              ShowBoxForControlCharacters="False"
                                                              ConvertTabsToSpaces="True"
                                                              IndentationSize="4"/>
                            </avalonedit:TextEditor.Options>
                        </avalonedit:TextEditor>
                    </Border>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding TableDefinition.Columns.Count, StringFormat='欄位數: {0}'}" 
                           Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Text" Value="載入中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding HasUnsavedChanges}" Value="True">
                                    <Setter Property="Text" Value="已修改"/>
                                    <Setter Property="Foreground" Value="Green"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
