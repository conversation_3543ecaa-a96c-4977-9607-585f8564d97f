# 資料庫物件編輯器使用指南

## 概述

資料庫物件編輯器是 OracleMS 的核心功能，提供專用的介面來檢視和編輯各種 Oracle 資料庫物件。本指南將介紹如何使用這些編輯器，以及它們提供的功能。

## 支援的物件類型

OracleMS 目前支援以下資料庫物件類型的專用編輯器：

1. **資料表 (Tables)**
2. **檢視表 (Views)**
3. **預存程序 (Procedures)**
4. **函數 (Functions)**
5. **套件 (Packages)**
6. **序列 (Sequences)**
7. **觸發器 (Triggers)**
8. **索引 (Indexes)**

## 開啟物件編輯器

有兩種方式可以開啟物件編輯器：

1. **從物件瀏覽器開啟**：
   - 在物件瀏覽器中找到您想要編輯的物件
   - 雙擊該物件，或右鍵點擊並選擇「編輯」選項
   - 系統將在新的標籤頁中開啟對應的物件編輯器

2. **從命令列開啟**：
   - 在查詢編輯器中，您可以使用 `EDIT [物件類型] [物件名稱]` 命令
   - 例如：`EDIT TABLE EMPLOYEES` 或 `EDIT PROCEDURE UPDATE_SALARY`

## 共同功能

所有物件編輯器都提供以下共同功能：

- **儲存變更**：使用工具列上的儲存按鈕或按下 `Ctrl+S`
- **重新載入**：使用工具列上的重新載入按鈕或按下 `F5`
- **產生 DDL**：產生物件的完整 DDL 定義
- **未儲存變更指示**：標籤頁標題中的星號 (*) 表示有未儲存的變更

## 資料表編輯器

資料表編輯器提供視覺化介面來編輯資料表結構。

### 功能

- **欄位管理**：新增、編輯、刪除資料表欄位
- **索引管理**：檢視和管理資料表索引
- **約束條件**：管理主鍵、外鍵和其他約束條件
- **觸發器**：檢視和編輯資料表觸發器
- **DDL 預覽**：即時預覽變更產生的 DDL 語句

### 使用方式

1. 在「欄位」標籤頁中，您可以：
   - 點擊「新增欄位」按鈕來新增欄位
   - 雙擊現有欄位來編輯其屬性
   - 使用右鍵選單刪除欄位

2. 在「索引」標籤頁中，您可以：
   - 檢視資料表的所有索引
   - 點擊「新增索引」按鈕來創建新索引
   - 雙擊索引來開啟索引編輯器

3. 在「約束條件」標籤頁中，您可以：
   - 管理主鍵、外鍵、唯一性和檢查約束條件
   - 點擊「新增約束條件」按鈕來新增約束條件
   - 編輯或刪除現有約束條件

## 檢視表編輯器

檢視表編輯器提供 SQL 編輯介面來修改檢視表定義。

### 功能

- **SQL 編輯**：使用語法高亮和自動完成功能編輯 SQL
- **測試執行**：執行查詢並預覽結果
- **結構檢視**：檢視檢視表的欄位結構

### 使用方式

1. 在「SQL 定義」標籤頁中編輯檢視表的 SQL 定義
2. 點擊「測試」按鈕執行查詢並在「結果」標籤頁中檢視結果
3. 點擊「儲存」按鈕儲存變更

## 預存程序編輯器

預存程序編輯器提供 PL/SQL 編輯介面來修改預存程序。

### 功能

- **PL/SQL 編輯**：使用語法高亮和自動完成功能編輯 PL/SQL
- **編譯**：編譯程序並檢視編譯錯誤
- **參數檢視**：檢視和編輯程序參數

### 使用方式

1. 在編輯區域中編輯程序的 PL/SQL 程式碼
2. 點擊「編譯」按鈕來編譯程序
3. 在「編譯結果」區域中檢視編譯錯誤或警告
4. 點擊「儲存」按鈕儲存變更

## 函數編輯器

函數編輯器提供 PL/SQL 編輯介面來修改函數，並支援函數測試。

### 功能

- **PL/SQL 編輯**：使用語法高亮和自動完成功能編輯 PL/SQL
- **編譯**：編譯函數並檢視編譯錯誤
- **函數測試**：輸入參數並執行函數測試
- **結果檢視**：檢視函數執行結果

### 使用方式

1. 在編輯區域中編輯函數的 PL/SQL 程式碼
2. 點擊「編譯」按鈕來編譯函數
3. 在「測試」標籤頁中輸入函數參數
4. 點擊「執行」按鈕測試函數並檢視結果
5. 點擊「儲存」按鈕儲存變更

## 套件編輯器

套件編輯器提供分頁介面來編輯套件規格和套件主體。

### 功能

- **規格編輯**：編輯套件規格 (Package Specification)
- **主體編輯**：編輯套件主體 (Package Body)
- **分別編譯**：分別編譯套件規格和主體
- **狀態檢視**：檢視套件規格和主體的編譯狀態

### 使用方式

1. 在「規格」標籤頁中編輯套件規格
2. 在「主體」標籤頁中編輯套件主體
3. 使用工具列上的「編譯規格」或「編譯主體」按鈕進行編譯
4. 在狀態列中檢視編譯狀態和錯誤訊息
5. 點擊「儲存」按鈕儲存變更

## 序列編輯器

序列編輯器提供表單介面來編輯序列屬性。

### 功能

- **屬性編輯**：編輯序列的起始值、增量、最大值、最小值等屬性
- **DDL 預覽**：即時預覽變更產生的 DDL 語句
- **當前值檢視**：檢視序列的當前值

### 使用方式

1. 在屬性表單中編輯序列屬性
2. 在「DDL 預覽」區域中檢視產生的 DDL 語句
3. 點擊「儲存」按鈕儲存變更

## 觸發器編輯器

觸發器編輯器提供介面來編輯觸發器設定和 PL/SQL 程式碼。

### 功能

- **觸發器設定**：編輯觸發事件、時機和條件
- **PL/SQL 編輯**：編輯觸發器的 PL/SQL 程式碼
- **編譯**：編譯觸發器並檢視編譯錯誤

### 使用方式

1. 在「設定」區域中編輯觸發器的事件、時機和條件
2. 在編輯區域中編輯觸發器的 PL/SQL 程式碼
3. 點擊「編譯」按鈕來編譯觸發器
4. 點擊「儲存」按鈕儲存變更

## 索引編輯器

索引編輯器提供介面來檢視和管理索引屬性。

### 功能

- **索引屬性檢視**：檢視索引類型、欄位、唯一性等屬性
- **統計資訊**：檢視索引的統計資訊
- **索引重建**：執行索引重建操作
- **索引分析**：執行索引分析操作

### 使用方式

1. 在「屬性」標籤頁中檢視索引屬性
2. 在「統計」標籤頁中檢視索引統計資訊
3. 點擊「重建索引」按鈕執行索引重建操作
4. 點擊「分析索引」按鈕執行索引分析操作

## 錯誤處理

當操作失敗時，系統會顯示錯誤訊息，包含以下資訊：

- 錯誤類型
- 錯誤描述
- 建議的解決方案

常見錯誤包括：

- **權限錯誤**：您沒有足夠的權限執行操作
- **語法錯誤**：SQL 或 PL/SQL 語法錯誤
- **名稱衝突**：物件名稱已存在
- **依賴關係錯誤**：無法修改或刪除有依賴關係的物件

## 快捷鍵

所有物件編輯器都支援以下快捷鍵：

- `Ctrl+S`：儲存變更
- `F5`：重新載入
- `Ctrl+Z`：復原
- `Ctrl+Y`：重做
- `F7`：編譯 (適用於 PL/SQL 物件)
- `F9`：執行測試 (適用於檢視表、函數)
- `Esc`：取消長時間操作

## 最佳實踐

1. **定期儲存**：定期儲存您的變更以避免資料遺失
2. **測試變更**：在儲存前測試您的變更
3. **檢查依賴關係**：修改物件前檢查其依賴關係
4. **使用交易**：執行重要變更時使用交易
5. **備份物件**：在進行重大變更前備份物件定義

## 疑難排解

如果您遇到問題，請嘗試以下步驟：

1. **重新載入編輯器**：點擊重新載入按鈕或按 F5
2. **重新連接資料庫**：關閉並重新開啟資料庫連接
3. **檢查權限**：確保您有足夠的權限執行操作
4. **檢查錯誤日誌**：查看應用程式錯誤日誌以獲取詳細資訊
5. **重新啟動應用程式**：如果問題持續，嘗試重新啟動應用程式

## 技術支援

如果您需要進一步的協助，請聯繫技術支援團隊：

- 電子郵件：<EMAIL>
- 內部支援系統：http://support.internal/oraclems
- 文件中心：http://docs.oraclems.com/object-editors