<Window x:Class="OracleMS.Views.ConnectionEditDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:OracleMS.Views"
        mc:Ignorable="d"
        Title="連線設定" Height="500" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        ShowInTaskbar="False">
    
    <Window.Resources>
        <local:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Oracle 資料庫連線設定" 
                   FontSize="16" FontWeight="Bold" Margin="0,0,0,16"/>
        
        <!-- Connection Settings -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Basic Settings Group -->
                <!--<GroupBox Header="基本設定" Margin="0,0,0,12">
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        --><!-- Connection Name --><!--
                        <Label Grid.Row="0" Grid.Column="0" Content="連線名稱:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="ConnectionNameTextBox"
                                 Text="{Binding Name, UpdateSourceTrigger=PropertyChanged}" 
                                 Margin="4" ToolTip="為此連線指定一個易於識別的名稱"/>
                        
                        --><!-- Connection Type --><!--
                        <Label Grid.Row="1" Grid.Column="0" Content="連線類型:" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="1" Grid.Column="1" 
                                  SelectedValue="{Binding Type}" Margin="4">
                            <ComboBoxItem Content="基本連線 (Basic)" Tag="Basic"/>
                            <ComboBoxItem Content="TNS 名稱" Tag="TNS"/>
                            <ComboBoxItem Content="進階連線" Tag="Advanced"/>
                        </ComboBox>
                        
                        --><!-- Description --><!--
                        <Label Grid.Row="2" Grid.Column="0" Content="描述:" VerticalAlignment="Top" Margin="0,8,0,0"/>
                        <TextBox Grid.Row="2" Grid.Column="1" 
                                 Text="{Binding Description, UpdateSourceTrigger=PropertyChanged}"
                                 Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Auto" Margin="4,8,4,4"
                                 ToolTip="選填：連線的詳細描述"/>
                    </Grid>
                </GroupBox>-->
                
                <!-- Server Settings Group -->
                <GroupBox Header="伺服器設定" Margin="0,0,0,12">
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="80"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Server -->
                        <Label Grid.Row="0" Grid.Column="0" Content="伺服器:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="1" x:Name="ServerBox"
                                 Text="{Binding Server, UpdateSourceTrigger=PropertyChanged}" 
                                 Margin="4" ToolTip="Oracle 伺服器的 IP 位址或主機名稱"/>
                        <Label Grid.Row="0" Grid.Column="3" Content="(選填)" VerticalAlignment="Center"/>

                        <!-- Port -->
                        <Label Grid.Row="1" Grid.Column="0" Content="埠號:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="1" Grid.Column="1" 
                                 Text="{Binding Port, UpdateSourceTrigger=PropertyChanged}" 
                                 Margin="4" ToolTip="Oracle 監聽器埠號，預設為 1521"/>
                        <Label Grid.Row="1" Grid.Column="3" Content="(選填)" VerticalAlignment="Center"/>

                        <!-- Service Name -->
                        <Label Grid.Row="2" Grid.Column="0" Content="服務名稱:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="1" x:Name="ServiceNameBox"
                                 Text="{Binding ServiceName, UpdateSourceTrigger=PropertyChanged}" 
                                 Margin="4" ToolTip="Oracle TNS名稱或 SID"/>
                    </Grid>
                </GroupBox>
                
                <!-- Authentication Group -->
                <GroupBox Header="驗證設定" Margin="0,0,0,12">
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Username -->
                        <Label Grid.Row="0" Grid.Column="0" Content="使用者名稱:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="UsernameBox"
                                 Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}" 
                                 Margin="4" ToolTip="Oracle 資料庫使用者名稱"/>
                        
                        <!-- Password -->
                        <Label Grid.Row="1" Grid.Column="0" Content="密碼:" VerticalAlignment="Center"/>
                        <PasswordBox Grid.Row="1" Grid.Column="1" x:Name="PasswordBox"
                                     Margin="4" PasswordChanged="OnPasswordChanged"
                                     ToolTip="Oracle 資料庫密碼"/>
                        
                        <!-- Save Password -->
                        <CheckBox Grid.Row="2" Grid.Column="1" Content="儲存密碼" 
                                  IsChecked="{Binding SavePassword}" Margin="4,8,4,4"
                                  ToolTip="勾選此項目將密碼加密儲存到本機設定檔"/>
                    </Grid>
                </GroupBox>
                
                <!-- Advanced Options Group -->
                <GroupBox Header="進階選項" Margin="0,0,0,12">
                    <Grid Margin="8">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="100"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Connection Timeout -->
                        <Label Grid.Row="0" Grid.Column="0" Content="連線逾時:" VerticalAlignment="Center"/>
                        <StackPanel Grid.Row="0" Grid.Column="1" Orientation="Horizontal" Margin="4">
                            <TextBox Text="{Binding ConnectionTimeout, UpdateSourceTrigger=PropertyChanged}" 
                                     Width="60" ToolTip="連線逾時時間（秒）"/>
                            <TextBlock Text="秒" VerticalAlignment="Center" Margin="4,0,0,0"/>
                        </StackPanel>
                        
                        <!-- Auto Connect -->
                        <CheckBox Grid.Row="1" Grid.Column="1" Content="啟動時自動連線" 
                                  IsChecked="{Binding AutoConnect}" Margin="4,8,4,4"
                                  ToolTip="應用程式啟動時自動連線到此資料庫"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Test Connection Status -->
        <Border Grid.Row="2" Background="#F8F8F8" BorderBrush="#E0E0E0" BorderThickness="1" 
                CornerRadius="4" Margin="0,8,0,8" Padding="8"
                Visibility="{Binding TestResult, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource StringToVisibilityConverter}}">
            <StackPanel Orientation="Horizontal">
                <!-- Loading Indicator -->
                <ProgressBar Width="16" Height="16" IsIndeterminate="True" 
                             Visibility="{Binding IsTesting, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}"
                             Margin="0,0,8,0"/>
                
                <!-- Test Result Icon -->
                <TextBlock Text="✅" FontSize="14" Margin="0,0,8,0"
                           Visibility="{Binding IsTestSuccessful, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                <TextBlock Text="❌" FontSize="14" Margin="0,0,8,0"
                           Visibility="{Binding IsTestFailed, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                
                <!-- Test Result Text -->
                <TextBlock Text="{Binding TestResult, RelativeSource={RelativeSource AncestorType=Window}}" VerticalAlignment="Center" TextWrapping="Wrap"/>
            </StackPanel>
        </Border>
        
        <!-- Action Buttons -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right">
            <Button Content="測試連線" Click="OnTestConnection" 
                    Margin="0,0,8,0" Padding="12,6" MinWidth="80"
                    IsEnabled="{Binding IsTesting, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource InverseBooleanConverter}}"
                    ToolTip="測試與 Oracle 資料庫的連線"/>
            <Button x:Name="OnOk_button" Content="確定" Click="OnOK" 
                    Margin="0,0,8,0" Padding="12,6" MinWidth="80"
                    IsDefault="True" ToolTip="儲存連線設定"/>
            <Button Content="取消" Click="OnCancel" 
                    Padding="12,6" MinWidth="80"
                    IsCancel="True" ToolTip="取消變更"/>
        </StackPanel>
    </Grid>
</Window>