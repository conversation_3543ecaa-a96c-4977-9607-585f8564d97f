<UserControl x:Class="OracleMS.Views.ProcedureEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:ProcedureEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="MinWidth" Value="75"/>
            <Setter Property="Height" Value="28"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Style for compilation result text -->
        <Style x:Key="CompilationResultStyle" TargetType="TextBlock">
            <Setter Property="FontFamily" Value="Consolas"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Toolbar -->
            <RowDefinition Height="*"/> <!-- Content Area -->
            <RowDefinition Height="Auto"/> <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Command="{Binding SaveCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="儲存預存程序變更 (Ctrl+S)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" Margin="0,0,4,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding RefreshCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="重新載入預存程序原始碼">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔄" Margin="0,0,4,0"/>
                        <TextBlock Text="重新載入"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding CompileCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="編譯預存程序 (F5)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🔨" Margin="0,0,4,0"/>
                        <TextBlock Text="編譯"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding ValidateCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="驗證 PL/SQL 語法">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✓" Margin="0,0,4,0" FontWeight="Bold" Foreground="Blue"/>
                        <TextBlock Text="驗證語法"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding GenerateScriptCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="產生 DDL 腳本">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📝" Margin="0,0,4,0"/>
                        <TextBlock Text="產生腳本"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <!-- Progress indicator -->
            <ProgressBar Width="100" Height="16" 
                         IsIndeterminate="True"
                         Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                         Margin="10,0"/>
        </ToolBar>

        <!-- Content Area -->
        <TabControl Grid.Row="1">
            <!-- Source Code Tab -->
            <TabItem Header="程序原始碼">
                <Grid>
                    <Border BorderBrush="Gray" BorderThickness="1">
                        <avalonedit:TextEditor x:Name="SourceCodeEditor"
                                               FontFamily="Consolas"
                                               FontSize="12"
                                               ShowLineNumbers="True"
                                               WordWrap="False"
                                               HorizontalScrollBarVisibility="Auto"
                                               VerticalScrollBarVisibility="Auto"
                                               Background="White"
                                               Foreground="Black">
                            <avalonedit:TextEditor.Options>
                                <avalonedit:TextEditorOptions ShowSpaces="False"
                                                              ShowTabs="False"
                                                              ShowEndOfLine="False"
                                                              ShowBoxForControlCharacters="False"
                                                              ConvertTabsToSpaces="True"
                                                              IndentationSize="4"/>
                            </avalonedit:TextEditor.Options>
                        </avalonedit:TextEditor>
                    </Border>
                </Grid>
            </TabItem>

            <!-- Compilation Results Tab -->
            <TabItem Header="編譯結果">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Compilation toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <TextBlock Text="{Binding CompilationStatus}" 
                                   FontWeight="Bold">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                                    <Setter Property="Foreground" Value="Gray"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding CompilationStatus}" Value="編譯成功">
                                            <Setter Property="Foreground" Value="Green"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding CompilationStatus}" Value="編譯失敗">
                                            <Setter Property="Foreground" Value="Red"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding CompilationStatus}" Value="編譯中">
                                            <Setter Property="Foreground" Value="Blue"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                        <Separator/>
                        <Button Command="{Binding ClearCompilationResultsCommand}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="清除編譯結果">
                            <Button.Content>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="🗑️" Margin="0,0,4,0"/>
                                    <TextBlock Text="清除結果"/>
                                </StackPanel>
                            </Button.Content>
                        </Button>
                    </ToolBar>

                    <!-- Compilation results content -->
                    <Grid Grid.Row="1">
                        <!-- Success message -->
                        <TextBlock Text="編譯成功！預存程序已成功建立或更新。"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="Green"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Visibility="{Binding IsCompilationSuccessful, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                        <!-- Error messages -->
                        <ScrollViewer VerticalScrollBarVisibility="Auto"
                                      HorizontalScrollBarVisibility="Auto"
                                      Visibility="{Binding HasCompilationErrors, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ItemsControl ItemsSource="{Binding CompilationErrors}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="Red" BorderThickness="1" Margin="2" Padding="5" Background="LightPink">
                                            <StackPanel>
                                                <TextBlock Text="{Binding ErrorCode, StringFormat='錯誤代碼: {0}'}" 
                                                           Style="{StaticResource CompilationResultStyle}"
                                                           FontWeight="Bold"/>
                                                <TextBlock Text="{Binding LineNumber, StringFormat='行號: {0}'}" 
                                                           Style="{StaticResource CompilationResultStyle}"/>
                                                <TextBlock Text="{Binding ColumnNumber, StringFormat='欄位: {0}'}" 
                                                           Style="{StaticResource CompilationResultStyle}"/>
                                                <TextBlock Text="{Binding Message}" 
                                                           Style="{StaticResource CompilationResultStyle}"
                                                           TextWrapping="Wrap"
                                                           Foreground="DarkRed"
                                                           FontWeight="Bold"/>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>

                        <!-- No compilation results message -->
                        <TextBlock Text="尚未執行編譯操作"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   Foreground="Gray"
                                   FontSize="14">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <MultiDataTrigger>
                                            <MultiDataTrigger.Conditions>
                                                <Condition Binding="{Binding IsCompilationSuccessful}" Value="False"/>
                                                <Condition Binding="{Binding HasCompilationErrors}" Value="False"/>
                                            </MultiDataTrigger.Conditions>
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </MultiDataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Grid>
            </TabItem>

            <!-- Parameters Tab -->
            <TabItem Header="參數資訊">
                <Grid>
                    <!-- Parameters DataGrid -->
                    <DataGrid x:Name="ParametersDataGrid"
                              ItemsSource="{Binding Parameters}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="參數名稱" 
                                                Binding="{Binding Name}" 
                                                Width="150"/>
                            <DataGridTextColumn Header="資料類型" 
                                                Binding="{Binding DataType}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="方向" 
                                                Binding="{Binding Direction}" 
                                                Width="80"/>
                            <DataGridTextColumn Header="長度" 
                                                Binding="{Binding Length}" 
                                                Width="60"/>
                            <DataGridTextColumn Header="精度" 
                                                Binding="{Binding Precision}" 
                                                Width="60"/>
                            <DataGridTextColumn Header="小數位數" 
                                                Binding="{Binding Scale}" 
                                                Width="80"/>
                            <DataGridTextColumn Header="預設值" 
                                                Binding="{Binding DefaultValue}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="備註" 
                                                Binding="{Binding Comments}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>

            <!-- Dependencies Tab -->
            <TabItem Header="相依性">
                <Grid>
                    <!-- Dependencies DataGrid -->
                    <DataGrid x:Name="DependenciesDataGrid"
                              ItemsSource="{Binding Dependencies}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="All"
                              CanUserReorderColumns="False"
                              CanUserResizeColumns="True"
                              CanUserSortColumns="True"
                              AlternatingRowBackground="LightGray">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="物件名稱" 
                                                Binding="{Binding Name}" 
                                                Width="200"/>
                            <DataGridTextColumn Header="物件類型" 
                                                Binding="{Binding Type}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="擁有者" 
                                                Binding="{Binding Owner}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="相依類型" 
                                                Binding="{Binding DependencyType}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="描述" 
                                                Binding="{Binding Description}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="2" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding ObjectName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding LastCompilationTime, StringFormat='編譯時間: {0:F2} 秒'}" 
                           Style="{StaticResource StatusTextStyle}"
                           Visibility="{Binding LastCompilationTime, Converter={StaticResource BooleanToVisibilityConverter}}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding StatusMessage}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Text" Value="載入中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding HasUnsavedChanges}" Value="True">
                                    <Setter Property="Text" Value="已修改"/>
                                    <Setter Property="Foreground" Value="Green"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>