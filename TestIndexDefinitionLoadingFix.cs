using System;
using System.Threading.Tasks;

namespace OracleMS
{
    /// <summary>
    /// 測試索引定義載入修復
    /// </summary>
    public class TestIndexDefinitionLoadingFix
    {
        public static async Task Main(string[] args)
        {
            try
            {
                Console.WriteLine("=== 索引定義載入修復測試 ===");
                Console.WriteLine();

                await TestIndexDefinitionLoadingFix();

                Console.WriteLine();
                Console.WriteLine("測試完成！按任意鍵結束...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"測試過程中發生錯誤: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
                Console.ReadKey();
            }
        }

        private static async Task TestIndexDefinitionLoadingFix()
        {
            Console.WriteLine("1. 索引定義載入問題分析");
            Console.WriteLine("   錯誤訊息：'取得索引定義失敗: 索引 TOB_AU1Index1 不存在'");
            Console.WriteLine("   問題原因：SQL 查詢沒有指定 Schema，導致找不到索引");
            Console.WriteLine();

            Console.WriteLine("=== 問題原因分析 ===");
            Console.WriteLine();

            Console.WriteLine("1. 原始 SQL 查詢問題");
            Console.WriteLine("   原始查詢：SELECT * FROM ALL_INDEXES WHERE INDEX_NAME = :indexName");
            Console.WriteLine("   問題：沒有指定 TABLE_OWNER (Schema)，可能找不到索引");
            Console.WriteLine();

            Console.WriteLine("2. IndexEditorViewModel 調用問題");
            Console.WriteLine("   原始調用：GetIndexDefinitionAsync(connection, ObjectName)");
            Console.WriteLine("   問題：ObjectName 只包含索引名稱，不包含 Schema 資訊");
            Console.WriteLine();

            Console.WriteLine("3. Schema 資訊來源");
            Console.WriteLine("   IndexEditorInfo.Schema：包含索引所屬的 Schema");
            Console.WriteLine("   但原始方法沒有使用這個資訊");
            Console.WriteLine();

            Console.WriteLine("=== 修復方案 ===");
            Console.WriteLine();

            Console.WriteLine("1. 新增 GetIndexDefinitionAsync 重載方法");
            Console.WriteLine("   新方法簽名：GetIndexDefinitionAsync(connection, indexName, schemaName)");
            Console.WriteLine("   支援可選的 Schema 參數");
            Console.WriteLine();

            Console.WriteLine("2. 修改 SQL 查詢邏輯");
            Console.WriteLine("   有 Schema：WHERE INDEX_NAME = :indexName AND TABLE_OWNER = :schemaName");
            Console.WriteLine("   無 Schema：WHERE INDEX_NAME = :indexName");
            Console.WriteLine();

            Console.WriteLine("3. 修改 IndexEditorViewModel 調用邏輯");
            Console.WriteLine("   優先使用：GetIndexDefinitionAsync(connection, ObjectName, _indexInfo.Schema)");
            Console.WriteLine("   備用方案：GetIndexDefinitionAsync(connection, ObjectName)");
            Console.WriteLine();

            Console.WriteLine("=== 修復後的執行流程 ===");
            Console.WriteLine();

            Console.WriteLine("步驟1：檢查 IndexEditorInfo.Schema");
            Console.WriteLine("  - 如果有 Schema，使用 Schema 參數查詢");
            Console.WriteLine("  - SQL: WHERE INDEX_NAME = 'TOB_AU1Index1' AND TABLE_OWNER = 'SCHEMA_NAME'");
            Console.WriteLine();

            Console.WriteLine("步驟2：如果 Schema 查詢失敗");
            Console.WriteLine("  - 記錄警告訊息");
            Console.WriteLine("  - 嘗試不指定 Schema 的查詢");
            Console.WriteLine("  - SQL: WHERE INDEX_NAME = 'TOB_AU1Index1'");
            Console.WriteLine();

            Console.WriteLine("步驟3：如果兩種查詢都失敗");
            Console.WriteLine("  - 拋出 '索引不存在' 的錯誤");
            Console.WriteLine("  - 提供詳細的錯誤資訊");
            Console.WriteLine();

            Console.WriteLine("=== 新增的診斷日誌 ===");
            Console.WriteLine();

            Console.WriteLine("1. IndexEditorViewModel 中的日誌");
            Console.WriteLine("   - '嘗試載入索引定義，IndexName: XXX, Schema: XXX'");
            Console.WriteLine("   - '使用 Schema XXX 查詢索引 XXX 失敗，嘗試不指定 Schema'");
            Console.WriteLine();

            Console.WriteLine("2. ObjectEditorService 中的日誌");
            Console.WriteLine("   - '正在取得索引定義: XXX, Schema: XXX'");
            Console.WriteLine("   - 詳細的 SQL 查詢資訊");
            Console.WriteLine();

            Console.WriteLine("=== 測試案例 ===");
            Console.WriteLine();

            Console.WriteLine("案例1：正確的 Schema 和索引名稱");
            Console.WriteLine("  IndexName: 'TOB_AU1Index1'");
            Console.WriteLine("  Schema: 'CORRECT_SCHEMA'");
            Console.WriteLine("  預期結果：成功載入索引定義");
            Console.WriteLine();

            Console.WriteLine("案例2：錯誤的 Schema 但正確的索引名稱");
            Console.WriteLine("  IndexName: 'TOB_AU1Index1'");
            Console.WriteLine("  Schema: 'WRONG_SCHEMA'");
            Console.WriteLine("  預期結果：第一次查詢失敗，第二次查詢成功");
            Console.WriteLine();

            Console.WriteLine("案例3：索引確實不存在");
            Console.WriteLine("  IndexName: 'NON_EXISTENT_INDEX'");
            Console.WriteLine("  Schema: 'ANY_SCHEMA'");
            Console.WriteLine("  預期結果：兩次查詢都失敗，拋出錯誤");
            Console.WriteLine();

            Console.WriteLine("=== 建議的測試步驟 ===");
            Console.WriteLine();

            Console.WriteLine("1. 關閉並重新啟動應用程式");
            Console.WriteLine("   - 確保使用最新的編譯版本");
            Console.WriteLine();

            Console.WriteLine("2. 開啟索引編輯模式");
            Console.WriteLine("   - 選擇一個已知存在的索引");
            Console.WriteLine("   - 檢查日誌中的診斷訊息");
            Console.WriteLine();

            Console.WriteLine("3. 檢查日誌輸出");
            Console.WriteLine("   - 查看 '嘗試載入索引定義' 的訊息");
            Console.WriteLine("   - 查看 '正在取得索引定義' 的訊息");
            Console.WriteLine("   - 確認是否成功載入索引定義");
            Console.WriteLine();

            Console.WriteLine("4. 如果仍然失敗");
            Console.WriteLine("   - 檢查索引是否真的存在");
            Console.WriteLine("   - 檢查 Schema 名稱是否正確");
            Console.WriteLine("   - 檢查資料庫權限");
            Console.WriteLine();

            Console.WriteLine("=== SQL 驗證查詢 ===");
            Console.WriteLine();

            Console.WriteLine("手動驗證索引是否存在：");
            Console.WriteLine("SELECT INDEX_NAME, TABLE_OWNER, TABLE_NAME");
            Console.WriteLine("FROM ALL_INDEXES");
            Console.WriteLine("WHERE INDEX_NAME = 'TOB_AU1Index1';");
            Console.WriteLine();

            Console.WriteLine("檢查特定 Schema 的索引：");
            Console.WriteLine("SELECT INDEX_NAME, TABLE_OWNER, TABLE_NAME");
            Console.WriteLine("FROM ALL_INDEXES");
            Console.WriteLine("WHERE INDEX_NAME = 'TOB_AU1Index1'");
            Console.WriteLine("  AND TABLE_OWNER = 'YOUR_SCHEMA';");
            Console.WriteLine();

            Console.WriteLine("🔧 索引定義載入修復測試完成！");
            Console.WriteLine("請重新啟動應用程式並測試索引編輯功能。");
        }
    }
}
