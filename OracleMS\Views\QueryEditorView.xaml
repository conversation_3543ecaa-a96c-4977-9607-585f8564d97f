<UserControl x:Class="OracleMS.Views.QueryEditorView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:avalonedit="http://icsharpcode.net/sharpdevelop/avalonedit"
             xmlns:viewmodels="clr-namespace:OracleMS.ViewModels"
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:dgx="urn:tom-englert.de/DataGridExtensions"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             d:DataContext="{d:DesignInstance Type=viewmodels:QueryEditorViewModel}">

    <UserControl.Resources>
        <!-- Converter for boolean to visibility -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Style for toolbar buttons -->
        <Style x:Key="ToolbarButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="1"/>
            <Setter Property="Padding" Value="6,1"/>
            <Setter Property="MinWidth" Value="45"/>
            <Setter Property="Height" Value="21"/>
        </Style>

        <!-- Style for status bar text -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>

        <!-- Style for transaction buttons -->
        <Style x:Key="TransactionButtonStyle" TargetType="Button" BasedOn="{StaticResource ToolbarButtonStyle}">
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <!-- Style for transaction status indicator -->
        <Style x:Key="TransactionStatusStyle" TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
            <Setter Property="FontWeight" Value="Bold"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding HasActiveTransaction}" Value="True">
                    <Setter Property="Foreground" Value="Green"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding HasActiveTransaction}" Value="False">
                    <Setter Property="Foreground" Value="Gray"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding TransactionState}" Value="Error">
                    <Setter Property="Foreground" Value="Red"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- Toolbar -->
            <RowDefinition Height="1.1*"/>
            <!-- SQL Editor -->
            <RowDefinition Height="3"/>
            <!-- Splitter -->
            <RowDefinition Height="*"/>
            <!-- Results and Messages -->
            <RowDefinition Height="Auto"/>
            <!-- Status Bar -->
        </Grid.RowDefinitions>

        <!-- Toolbar -->
        <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
            <Button Command="{Binding ExecuteCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="執行所有 SQL 語句 (F5)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="▶" Margin="0,0,4,0" FontWeight="Bold" Foreground="Green"/>
                        <TextBlock Text="執行"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding ExecuteSelectedCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="執行選取的 SQL 語句">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="▶" Margin="0,0,4,0" FontWeight="Bold" Foreground="Orange"/>
                        <TextBlock Text="執行選取"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding CancelCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="取消執行中的查詢"
                    Visibility="{Binding IsExecuting, Converter={StaticResource BooleanToVisibilityConverter}}">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="⏹" Margin="0,0,4,0" FontWeight="Bold" Foreground="Red"/>
                        <TextBlock Text="取消"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding NewCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="建立新查詢">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📄" Margin="0,0,4,0"/>
                        <TextBlock Text="新增"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding OpenCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="開啟 SQL 檔案">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📂" Margin="0,0,4,0"/>
                        <TextBlock Text="開啟"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding SaveCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="儲存查詢 (Ctrl+S)">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="💾" Margin="0,0,4,0"/>
                        <TextBlock Text="儲存"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <Button Command="{Binding ClearResultsCommand}"
                    Style="{StaticResource ToolbarButtonStyle}"
                    ToolTip="清除查詢結果">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="🗑" Margin="0,0,4,0"/>
                        <TextBlock Text="清除結果"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Separator/>

            <!-- Transaction Control Buttons -->
            <Button Command="{Binding CommitTransactionCommand}"
                    Style="{StaticResource TransactionButtonStyle}"
                    ToolTip="提交當前交易">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✓" Margin="0,0,4,0" FontWeight="Bold" Foreground="Green"/>
                        <TextBlock Text="提交"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <Button Command="{Binding RollbackTransactionCommand}"
                    Style="{StaticResource TransactionButtonStyle}"
                    ToolTip="回滾當前交易">
                <Button.Content>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="✗" Margin="0,0,4,0" FontWeight="Bold" Foreground="Red"/>
                        <TextBlock Text="回滾"/>
                    </StackPanel>
                </Button.Content>
            </Button>

            <!-- Progress indicator -->
            <ProgressBar Width="100" Height="16" 
                         IsIndeterminate="True"
                         Visibility="{Binding IsExecuting, Converter={StaticResource BooleanToVisibilityConverter}}"
                         Margin="10,0"/>
        </ToolBar>

        <!-- SQL Editor -->
        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1">
            <avalonedit:TextEditor x:Name="SqlEditor"
                                   FontFamily="Consolas"
                                   FontSize="12"
                                   ShowLineNumbers="True"
                                   WordWrap="False"
                                   HorizontalScrollBarVisibility="Auto"
                                   VerticalScrollBarVisibility="Auto"
                                   Background="White"
                                   Foreground="Black">
                <avalonedit:TextEditor.Options>
                    <avalonedit:TextEditorOptions ShowSpaces="False"
                                                  ShowTabs="False"
                                                  ShowEndOfLine="False"
                                                  ShowBoxForControlCharacters="False"
                                                  ConvertTabsToSpaces="True"
                                                  IndentationSize="4"/>
                </avalonedit:TextEditor.Options>
            </avalonedit:TextEditor>
        </Border>

        <!-- Splitter -->
        <GridSplitter Grid.Row="2" 
                      HorizontalAlignment="Stretch" 
                      VerticalAlignment="Stretch"
                      Background="LightGray"
                      ShowsPreview="True"/>

        <!-- Results and Messages Area -->
        <TabControl Grid.Row="3" Background="White">
            <!-- Results Tab -->
            <TabItem Header="結果">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="28*"/>
                        <ColumnDefinition Width="369*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Results toolbar -->
                    <!--<ToolBar Grid.Row="0" ToolBarTray.IsLocked="True" Grid.ColumnSpan="2">
                        <TextBlock Text="{Binding Results.Count, StringFormat='共 {0} 個結果集'}" 
                                   Style="{StaticResource StatusTextStyle}"/>
                        <Separator/>
                        <Button Content="匯出" 
                                Command="{Binding ExportResultsCommand}"
                                CommandParameter="{Binding SelectedItem, ElementName=ResultsTabControl}"
                                Style="{StaticResource ToolbarButtonStyle}"
                                ToolTip="匯出選取的結果集"/>
                    </ToolBar>-->

                    <!-- Results content -->
                    <TabControl x:Name="ResultsTabControl" Grid.Row="1"
                                ItemsSource="{Binding Results}"
                                SelectedIndex="{Binding SelectedTabIndex}"
                                Grid.ColumnSpan="2"
                                VirtualizingPanel.IsVirtualizing="True"
                                VirtualizingPanel.VirtualizationMode="Recycling">
                        <TabControl.ItemTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="結果 "/>
                                    <TextBlock>
                                        <TextBlock.Text>
                                            <MultiBinding Converter="{x:Static local:Converters.ItemIndexConverter}">
                                                <Binding />
                                                <Binding RelativeSource="{RelativeSource AncestorType=TabControl}" Path="ItemsSource" />
                                            </MultiBinding>
                                        </TextBlock.Text>
                                    </TextBlock>
                                    <!--<TextBlock Text="{Binding RowsAffected, StringFormat=' ({0} 行)'}" 
                                               Visibility="{Binding IsSuccess, Converter={StaticResource BooleanToVisibilityConverter}}"/>-->
                                    <!--<TextBlock Text=" ❌" 
                                               Visibility="{Binding IsSuccess, Converter={x:Static local:Converters.InverseBooleanToVisibilityConverter}}"
                                               Foreground="Red"/>-->
                                </StackPanel>
                            </DataTemplate>
                        </TabControl.ItemTemplate>
                        <TabControl.ContentTemplate>
                            <DataTemplate>
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <!-- Pagination controls -->
                                        <RowDefinition Height="*"/>
                                        <!-- Data grid -->
                                    </Grid.RowDefinitions>

                                    <!-- Pagination controls for SELECT results -->
                                    <Border Grid.Row="0"
                                            Background="LightGray"
                                            Padding="5"
                                            Visibility="{Binding IsPagedResult, Converter={StaticResource BooleanToVisibilityConverter}}">
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <!-- First page button -->
                                            <Button Content="⏮ 第一頁"
                                                    Command="{Binding DataContext.FirstPageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Margin="5,0"
                                                    Padding="10,2"
                                                    IsEnabled="{Binding CurrentPage, Converter={x:Static local:Converters.GreaterThanConverter}, ConverterParameter=1}"/>

                                            <!-- Page info -->
                                            <TextBlock VerticalAlignment="Center" Margin="10,0">
                                                <TextBlock.Text>
                                                    <MultiBinding StringFormat="第 {0} 頁，顯示 {1} 筆資料">
                                                        <Binding Path="CurrentPage"/>
                                                        <Binding Path="RowsAffected"/>
                                                    </MultiBinding>
                                                </TextBlock.Text>
                                            </TextBlock>

                                            <!-- Next page button -->
                                            <Button Content="下一頁 ▶"
                                                    Command="{Binding DataContext.NextPageCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Margin="5,0"
                                                    Padding="10,2"
                                                    IsEnabled="{Binding HasMoreData}"/>

                                            <!-- Load all data button -->
                                            <Button Content="📄 載入全部"
                                                    Command="{Binding DataContext.LoadAllDataCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"
                                                    Margin="20,0,5,0"
                                                    Padding="10,2"
                                                    Background="LightBlue"
                                                    Visibility="{Binding IsFullDataLoaded, Converter={x:Static local:Converters.InverseBooleanToVisibilityConverter}}"/>

                                            <!-- Full data loaded indicator -->
                                            <TextBlock Text="✅ 已載入全部資料"
                                                       VerticalAlignment="Center"
                                                       Margin="20,0,5,0"
                                                       Foreground="Green"
                                                       FontWeight="Bold"
                                                       Visibility="{Binding IsFullDataLoaded, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Success result -->
                                    <DataGrid x:Name="ResultsDataGrid"
                                              Grid.Row="1"
                                              ItemsSource="{Binding Data.DefaultView, IsAsync=True}"
                                              AutoGenerateColumns="True"
                                              IsReadOnly="True"
                                              GridLinesVisibility="All"
                                              HeadersVisibility="All"
                                              CanUserReorderColumns="True"
                                              CanUserResizeColumns="True"
                                              CanUserSortColumns="True"
                                              AlternatingRowBackground="LightGray"
                                              Visibility="{Binding IsSuccess, Converter={StaticResource BooleanToVisibilityConverter}}"
                                              EnableRowVirtualization="True"
                                              EnableColumnVirtualization="True"
                                              VirtualizingPanel.VirtualizationMode="Recycling"
                                              VirtualizingPanel.IsVirtualizing="True"
                                              VirtualizingPanel.ScrollUnit="Pixel"
                                              VirtualizingPanel.CacheLengthUnit="Item"
                                              VirtualizingPanel.CacheLength="20,20"
                                              ScrollViewer.CanContentScroll="True"
                                              ScrollViewer.IsDeferredScrollingEnabled="False"
                                              dgx:DataGridFilter.IsAutoFilterEnabled="True"
                                              dgx:Tools.ApplyInitialSorting="True">
                                        <DataGrid.ContextMenu>
                                            <ContextMenu>
                                                <MenuItem Header="複製" Click="CopyCell_Click"/>
                                                <MenuItem Header="複製行" Click="CopyRow_Click"/>
                                                <MenuItem Header="複製所有" Click="CopyAll_Click"/>
                                                <Separator/>
                                                <MenuItem Header="匯出至 CSV"
                                                          Command="{Binding DataContext.ExportResultsCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                          CommandParameter="{Binding}"/>
                                            </ContextMenu>
                                        </DataGrid.ContextMenu>
                                    </DataGrid>

                                    <!-- Error result -->
                                    <TextBox Grid.Row="1"
                                             Text="{Binding ErrorMessage}"
                                             IsReadOnly="True"
                                             Background="LightPink"
                                             Foreground="DarkRed"
                                             FontFamily="Consolas"
                                             TextWrapping="Wrap"
                                             VerticalScrollBarVisibility="Auto"
                                             HorizontalScrollBarVisibility="Auto"
                                             Visibility="{Binding IsSuccess, Converter={x:Static local:Converters.InverseBooleanToVisibilityConverter}}"/>
                                </Grid>
                            </DataTemplate>
                        </TabControl.ContentTemplate>
                    </TabControl>
                </Grid>
            </TabItem>

            <!-- Messages Tab -->
            <TabItem Header="訊息">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Messages toolbar -->
                    <ToolBar Grid.Row="0" ToolBarTray.IsLocked="True">
                        <Button Content="清除訊息" 
                                Click="ClearMessages_Click"
                                Style="{StaticResource ToolbarButtonStyle}"/>
                        <Button Content="複製訊息" 
                                Click="CopyMessages_Click"
                                Style="{StaticResource ToolbarButtonStyle}"/>
                    </ToolBar>

                    <!-- Messages content -->
                    <TextBox x:Name="MessagesTextBox"
                             Grid.Row="1"
                             Text="{Binding Messages}"
                             IsReadOnly="True"
                             FontFamily="Consolas"
                             FontSize="11"
                             Background="White"
                             Foreground="Black"
                             TextWrapping="NoWrap"
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"/>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Status Bar -->
        <StatusBar Grid.Row="4" Background="LightGray">
            <StatusBarItem>
                <TextBlock Text="{Binding FileName}" Style="{StaticResource StatusTextStyle}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Style="{StaticResource StatusTextStyle}">
                    <TextBlock.Text>
                        <MultiBinding StringFormat="行 {0}, 列 {1}">
                            <Binding Path="CurrentLine"/>
                            <Binding Path="CurrentColumn"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding LastExecutionTime, StringFormat='執行時間: {0:F2} 秒'}" 
                           Style="{StaticResource StatusTextStyle}"
                           Visibility="{Binding LastExecutionTime, Converter={x:Static local:Converters.TimeSpanToVisibilityConverter}}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="{Binding TransactionStatusText}" 
                           Style="{StaticResource TransactionStatusStyle}"
                           ToolTip="當前交易狀態"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock>
                    <TextBlock.Style>
                        <Style TargetType="TextBlock" BasedOn="{StaticResource StatusTextStyle}">
                            <Setter Property="Text" Value="就緒"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsExecuting}" Value="True">
                                    <Setter Property="Text" Value="執行中..."/>
                                    <Setter Property="Foreground" Value="Blue"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
