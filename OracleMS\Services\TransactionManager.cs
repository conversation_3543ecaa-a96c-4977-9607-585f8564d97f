using System.Data;
using OracleMS.Interfaces;

namespace OracleMS.Services;

/// <summary>
/// Manages database transactions with automatic lifecycle handling
/// </summary>
public class TransactionManager : ITransactionManager
{
    private IDbTransaction? _currentTransaction;
    private TransactionState _currentState = TransactionState.None;
    private readonly object _lockObject = new object();
    private IDbConnection? _currentConnection;
    private DateTime? _transactionStartTime;
    private int _transactionTimeoutSeconds = 0; // 0 = no timeout
    private bool _disposed = false;



    /// <inheritdoc />
    public bool HasActiveTransaction
    {
        get
        {
            lock (_lockObject)
            {
                return _currentTransaction != null && _currentState == TransactionState.Active;
            }
        }
    }

    /// <inheritdoc />
    public TransactionState CurrentState
    {
        get
        {
            lock (_lockObject)
            {
                return _currentState;
            }
        }
    }

    /// <inheritdoc />
    public IDbTransaction? CurrentTransaction
    {
        get
        {
            lock (_lockObject)
            {
                return _currentTransaction;
            }
        }
    }

    /// <inheritdoc />
    public int TransactionTimeoutSeconds
    {
        get
        {
            lock (_lockObject)
            {
                return _transactionTimeoutSeconds;
            }
        }
        set
        {
            lock (_lockObject)
            {
                _transactionTimeoutSeconds = Math.Max(0, value);
            }
        }
    }

    /// <inheritdoc />
    public DateTime? TransactionStartTime
    {
        get
        {
            lock (_lockObject)
            {
                return _transactionStartTime;
            }
        }
    }

    /// <inheritdoc />
    public event EventHandler<TransactionStateChangedEventArgs>? StateChanged;

    /// <inheritdoc />
    public async Task<IDbTransaction> BeginTransactionAsync(IDbConnection connection)
    {
        ThrowIfDisposed();
        
        if (connection == null)
            throw new ArgumentNullException(nameof(connection));

        lock (_lockObject)
        {
            if (HasActiveTransaction)
            {
                // Return existing transaction if already active
                return _currentTransaction!;
            }
        }

        try
        {
            // Check connection state and attempt to open if needed
            if (connection.State == ConnectionState.Closed)
            {
                await Task.Run(() => connection.Open());
            }
            else if (connection.State == ConnectionState.Broken)
            {
                // Connection is broken, try to close and reopen
                try
                {
                    connection.Close();
                    await Task.Run(() => connection.Open());
                }
                catch (Exception reopenEx)
                {
                    lock (_lockObject)
                    {
                        var previousState = _currentState;
                        _currentState = TransactionState.Error;
                        
                        OnStateChanged(new TransactionStateChangedEventArgs(
                            previousState, 
                            TransactionState.Error, 
                            "Failed to reopen broken connection for transaction", 
                            reopenEx));
                    }
                    throw new InvalidOperationException("Unable to establish database connection for transaction", reopenEx);
                }
            }
            else if (connection.State != ConnectionState.Open)
            {
                throw new InvalidOperationException($"Connection is in invalid state for transaction: {connection.State}");
            }

            var transaction = await Task.Run(() => connection.BeginTransaction());
            
            lock (_lockObject)
            {
                var previousState = _currentState;
                _currentTransaction = transaction;
                _currentConnection = connection;
                _currentState = TransactionState.Active;
                _transactionStartTime = DateTime.UtcNow;
                
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.Active, 
                    "Transaction started successfully"));
            }

            return transaction;
        }
        catch (Exception ex)
        {
            lock (_lockObject)
            {
                var previousState = _currentState;
                _currentState = TransactionState.Error;
                
                // Provide user-friendly error message based on exception type
                var userMessage = GetUserFriendlyTransactionStartError(ex);
                
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.Error, 
                    userMessage, 
                    ex));
            }
            throw;
        }
    }

    /// <inheritdoc />
    public async Task CommitAsync()
    {
        ThrowIfDisposed();
        
        IDbTransaction? transactionToCommit = null;
        IDbConnection? connection = null;
        
        lock (_lockObject)
        {
            if (!HasActiveTransaction)
            {
                // No active transaction to commit - handle gracefully
                return;
            }
            
            transactionToCommit = _currentTransaction;
            connection = _currentTransaction?.Connection;
        }

        try
        {
            // Check for transaction timeout before committing
            if (IsTransactionTimedOut())
            {
                lock (_lockObject)
                {
                    var previousState = _currentState;
                    CleanupTransactionResources();
                    _currentState = TransactionState.Error;
                    
                    OnStateChanged(new TransactionStateChangedEventArgs(
                        previousState, 
                        TransactionState.Error, 
                        $"Transaction timed out after {_transactionTimeoutSeconds} seconds", 
                        new TimeoutException("Transaction exceeded timeout limit")));
                }
                throw new TimeoutException($"Transaction timed out after {_transactionTimeoutSeconds} seconds");
            }

            // Check connection state before committing
            if (connection != null && IsConnectionLost(connection))
            {
                // Connection is lost, clean up transaction state
                lock (_lockObject)
                {
                    var previousState = _currentState;
                    CleanupTransactionResources();
                    _currentState = TransactionState.Error;
                    
                    OnStateChanged(new TransactionStateChangedEventArgs(
                        previousState, 
                        TransactionState.Error, 
                        "Connection lost during commit - transaction state cleaned up", 
                        new InvalidOperationException("Database connection was lost")));
                }
                throw new InvalidOperationException("Cannot commit transaction: database connection was lost");
            }

            await Task.Run(() => transactionToCommit!.Commit());
            
            lock (_lockObject)
            {
                var previousState = _currentState;
                CleanupTransactionResources();
                _currentState = TransactionState.Committed;
                
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.Committed, 
                    "Transaction committed successfully"));
            }
        }
        catch (Exception ex)
        {
            lock (_lockObject)
            {
                var previousState = _currentState;
                
                // Preserve transaction state on commit failure (Requirement 5.2)
                // Only set to error state, don't clean up transaction yet
                _currentState = TransactionState.Error;
                
                // Provide user-friendly error message
                var userMessage = GetUserFriendlyCommitError(ex);
                
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.Error, 
                    userMessage, 
                    ex));
            }
            throw;
        }
    }

    /// <inheritdoc />
    public async Task RollbackAsync()
    {
        ThrowIfDisposed();
        
        IDbTransaction? transactionToRollback = null;
        IDbConnection? connection = null;
        
        lock (_lockObject)
        {
            if (!HasActiveTransaction)
            {
                // No active transaction to rollback - handle gracefully
                return;
            }
            
            transactionToRollback = _currentTransaction;
            connection = _currentTransaction?.Connection;
        }

        try
        {
            // Check for transaction timeout before rolling back
            if (IsTransactionTimedOut())
            {
                lock (_lockObject)
                {
                    var previousState = _currentState;
                    CleanupTransactionResources();
                    _currentState = TransactionState.RolledBack;
                    
                    OnStateChanged(new TransactionStateChangedEventArgs(
                        previousState, 
                        TransactionState.RolledBack, 
                        $"Transaction timed out after {_transactionTimeoutSeconds} seconds - automatically rolled back"));
                }
                return;
            }

            // Check connection state before rolling back
            if (connection != null && IsConnectionLost(connection))
            {
                // Connection is lost, perform cleanup without attempting rollback
                lock (_lockObject)
                {
                    var previousState = _currentState;
                    CleanupTransactionResources();
                    _currentState = TransactionState.RolledBack; // Consider it rolled back since connection is lost
                    
                    OnStateChanged(new TransactionStateChangedEventArgs(
                        previousState, 
                        TransactionState.RolledBack, 
                        "Connection lost - transaction automatically rolled back by database"));
                }
                return;
            }

            await Task.Run(() => transactionToRollback!.Rollback());
            
            lock (_lockObject)
            {
                var previousState = _currentState;
                CleanupTransactionResources();
                _currentState = TransactionState.RolledBack;
                
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.RolledBack, 
                    "Transaction rolled back successfully"));
            }
        }
        catch (Exception ex)
        {
            lock (_lockObject)
            {
                var previousState = _currentState;
                
                // Perform comprehensive cleanup on rollback failure (Requirement 5.3)
                CleanupTransactionResources();
                _currentState = TransactionState.Error;
                
                // Provide user-friendly error message
                var userMessage = GetUserFriendlyRollbackError(ex);
                
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.Error, 
                    userMessage, 
                    ex));
            }
            throw;
        }
    }

    /// <inheritdoc />
    public bool ShouldAutoCommit(string sql)
    {
        return DdlStatementDetector.IsDdlStatement(sql);
    }

    /// <summary>
    /// Executes auto-commit logic for DDL statements
    /// </summary>
    /// <param name="sql">The SQL statement that was executed</param>
    /// <returns>True if auto-commit was performed, false otherwise</returns>
    public async Task<bool> AutoCommitIfNeededAsync(string sql)
    {
        if (!ShouldAutoCommit(sql))
            return false;

        if (!HasActiveTransaction)
            return false;

        try
        {
            await CommitAsync();
            return true;
        }
        catch (Exception)
        {
            // CommitAsync already handles error state and event raising
            // Just rethrow the exception for the caller to handle
            throw;
        }
    }

    /// <inheritdoc />
    public void Reset()
    {
        ThrowIfDisposed();
        
        lock (_lockObject)
        {
            var previousState = _currentState;
            
            CleanupTransactionResources();
            _currentState = TransactionState.None;
            
            if (previousState != TransactionState.None)
            {
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.None, 
                    "Transaction manager reset"));
            }
        }
    }

    /// <inheritdoc />
    public void ResetForNewConnection(IDbConnection newConnection)
    {
        ThrowIfDisposed();
        
        if (newConnection == null)
            throw new ArgumentNullException(nameof(newConnection));

        lock (_lockObject)
        {
            var previousState = _currentState;
            var hadActiveTransaction = HasActiveTransaction;
            
            // Check if this is actually a different connection
            if (_currentConnection != null && ReferenceEquals(_currentConnection, newConnection))
            {
                // Same connection, no need to reset
                return;
            }
            
            // Clean up any existing transaction state
            CleanupTransactionResources();
            _currentConnection = newConnection;
            _currentState = TransactionState.None;
            
            if (hadActiveTransaction)
            {
                OnStateChanged(new TransactionStateChangedEventArgs(
                    previousState, 
                    TransactionState.None, 
                    "Transaction state reset for new connection - previous transaction was automatically rolled back"));
            }
        }
    }

    /// <inheritdoc />
    public bool IsTransactionTimedOut()
    {
        lock (_lockObject)
        {
            if (!HasActiveTransaction || _transactionTimeoutSeconds <= 0 || _transactionStartTime == null)
            {
                return false;
            }

            var elapsed = DateTime.UtcNow - _transactionStartTime.Value;
            return elapsed.TotalSeconds > _transactionTimeoutSeconds;
        }
    }

    /// <summary>
    /// Raises the StateChanged event
    /// </summary>
    /// <param name="e">Event arguments</param>
    protected virtual void OnStateChanged(TransactionStateChangedEventArgs e)
    {
        StateChanged?.Invoke(this, e);
    }

    /// <summary>
    /// Checks if the database connection is lost or in an invalid state
    /// </summary>
    /// <param name="connection">The database connection to check</param>
    /// <returns>True if connection is lost, false otherwise</returns>
    private static bool IsConnectionLost(IDbConnection connection)
    {
        try
        {
            return connection.State == ConnectionState.Broken || 
                   connection.State == ConnectionState.Closed ||
                   connection.State == ConnectionState.Connecting;
        }
        catch
        {
            // If we can't even check the state, assume connection is lost
            return true;
        }
    }

    /// <summary>
    /// Cleans up transaction resources safely
    /// </summary>
    private void CleanupTransactionResources()
    {
        try
        {
            _currentTransaction?.Dispose();
        }
        catch
        {
            // Ignore disposal errors during cleanup
        }
        finally
        {
            _currentTransaction = null;
            _currentConnection = null;
            _transactionStartTime = null;
        }
    }

    /// <summary>
    /// Gets user-friendly error message for transaction start failures
    /// </summary>
    /// <param name="ex">The exception that occurred</param>
    /// <returns>User-friendly error message</returns>
    private static string GetUserFriendlyTransactionStartError(Exception ex)
    {
        return ex switch
        {
            InvalidOperationException when ex.Message.Contains("connection") => 
                "Unable to start transaction: database connection issue",
            TimeoutException => 
                "Transaction start timed out - database may be busy",
            UnauthorizedAccessException => 
                "Access denied when starting transaction - check database permissions",
            _ => $"Failed to start transaction: {ex.Message}"
        };
    }

    /// <summary>
    /// Gets user-friendly error message for commit failures
    /// </summary>
    /// <param name="ex">The exception that occurred</param>
    /// <returns>User-friendly error message</returns>
    private static string GetUserFriendlyCommitError(Exception ex)
    {
        return ex switch
        {
            InvalidOperationException when ex.Message.Contains("connection") => 
                "Cannot commit transaction: database connection was lost",
            TimeoutException => 
                "Transaction commit timed out - you can retry or rollback",
            _ => $"Failed to commit transaction: {ex.Message}. You can retry commit or choose to rollback."
        };
    }

    /// <summary>
    /// Gets user-friendly error message for rollback failures
    /// </summary>
    /// <param name="ex">The exception that occurred</param>
    /// <returns>User-friendly error message</returns>
    private static string GetUserFriendlyRollbackError(Exception ex)
    {
        return ex switch
        {
            InvalidOperationException when ex.Message.Contains("connection") => 
                "Rollback failed due to connection loss - transaction state has been cleaned up",
            TimeoutException => 
                "Rollback timed out - transaction state has been cleaned up",
            _ => $"Rollback failed: {ex.Message}. Transaction state has been cleaned up."
        };
    }

    /// <summary>
    /// Disposes the transaction manager and cleans up resources
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// Protected dispose method for proper disposal pattern
    /// </summary>
    /// <param name="disposing">True if disposing managed resources</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                lock (_lockObject)
                {
                    CleanupTransactionResources();
                    _currentState = TransactionState.None;
                }
            }
            _disposed = true;
        }
    }

    /// <summary>
    /// Throws ObjectDisposedException if the object has been disposed
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(TransactionManager));
        }
    }
}