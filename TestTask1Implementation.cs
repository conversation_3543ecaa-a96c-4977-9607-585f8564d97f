using OracleMS.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace OracleMS
{
    /// <summary>
    /// Test program to verify Task 1 implementation
    /// </summary>
    public class TestTask1Implementation
    {
        public static void RunTests()
        {
            Console.WriteLine("Testing Task 1 Implementation: IndexInfo and IndexDefinition Extensions");
            Console.WriteLine("=======================================================================");
            
            // Test 1: IndexEditorInfo (IndexInfo) functionality
            TestIndexEditorInfo();
            
            // Test 2: IndexDefinition extensions
            TestIndexDefinitionExtensions();
            
            Console.WriteLine("\nAll tests completed successfully!");
        }
        
        private static void TestIndexEditorInfo()
        {
            Console.WriteLine("\n1. Testing IndexEditorInfo class:");
            
            // Test default constructor
            var indexInfo = new IndexEditorInfo();
            Console.WriteLine($"   Default constructor - Schema: {indexInfo.Schema ?? "null"}, IsEditMode: {indexInfo.IsEditMode}");
            
            // Test property setting
            indexInfo.Schema = "TEST_SCHEMA";
            indexInfo.TableName = "TEST_TABLE";
            indexInfo.IndexName = "TEST_INDEX";
            indexInfo.IsUnique = true;
            indexInfo.IsEditMode = true;
            indexInfo.Columns.AddRange(new[] { "COL1", "COL2", "COL3" });
            
            Console.WriteLine($"   After setting properties:");
            Console.WriteLine($"     Schema: {indexInfo.Schema}");
            Console.WriteLine($"     TableName: {indexInfo.TableName}");
            Console.WriteLine($"     IndexName: {indexInfo.IndexName}");
            Console.WriteLine($"     IsUnique: {indexInfo.IsUnique}");
            Console.WriteLine($"     IsEditMode: {indexInfo.IsEditMode}");
            Console.WriteLine($"     Columns: [{string.Join(", ", indexInfo.Columns)}]");
            
            // Test create mode
            var createModeInfo = new IndexEditorInfo
            {
                Schema = "CREATE_SCHEMA",
                TableName = "CREATE_TABLE",
                IndexName = "NEW_INDEX",
                IsEditMode = false
            };
            Console.WriteLine($"   Create mode - IsEditMode: {createModeInfo.IsEditMode}");
        }
        
        private static void TestIndexDefinitionExtensions()
        {
            Console.WriteLine("\n2. Testing IndexDefinition extensions:");
            
            var indexDef = new IndexDefinition();
            
            // Test AvailableColumns property
            var availableColumns = new List<string> { "ID", "NAME", "EMAIL", "CREATED_DATE" };
            indexDef.AvailableColumns = availableColumns;
            Console.WriteLine($"   AvailableColumns set: [{string.Join(", ", indexDef.AvailableColumns)}]");
            
            // Test UpdateColumnsFromSelection method
            var selectedColumns = new List<string> { "ID", "NAME", "EMAIL" };
            indexDef.UpdateColumnsFromSelection(selectedColumns);
            
            Console.WriteLine($"   After UpdateColumnsFromSelection:");
            Console.WriteLine($"     Number of columns: {indexDef.Columns.Count}");
            
            for (int i = 0; i < indexDef.Columns.Count; i++)
            {
                var col = indexDef.Columns[i];
                Console.WriteLine($"     Column {i + 1}: Name={col.ColumnName}, Position={col.Position}, IsDescending={col.IsDescending}");
            }
            
            // Test replacing existing columns
            var newSelection = new List<string> { "EMAIL", "CREATED_DATE" };
            indexDef.UpdateColumnsFromSelection(newSelection);
            Console.WriteLine($"   After replacing with new selection:");
            Console.WriteLine($"     Number of columns: {indexDef.Columns.Count}");
            
            for (int i = 0; i < indexDef.Columns.Count; i++)
            {
                var col = indexDef.Columns[i];
                Console.WriteLine($"     Column {i + 1}: Name={col.ColumnName}, Position={col.Position}, IsDescending={col.IsDescending}");
            }
            
            // Test empty selection
            indexDef.UpdateColumnsFromSelection(new List<string>());
            Console.WriteLine($"   After empty selection - Number of columns: {indexDef.Columns.Count}");
        }
    }
}