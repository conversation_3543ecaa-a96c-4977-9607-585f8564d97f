﻿<UserControl x:Class="OracleMS.Views.DbSession"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OracleMS.Views"
             xmlns:views="clr-namespace:OracleMS.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Grid>
        <TabControl x:Name="MainTabControl"
            Background="White"
            BorderBrush="LightGray"
            BorderThickness="1">

            <!-- Welcome Tab - shown when no other tabs are open -->
            <TabItem x:Name="DbTab">
                <TabItem.Header>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="資料庫" VerticalAlignment="Center" Margin="0,0,8,0"/>
                        <Button Content="+"
                                Width="16"
                                Height="16"
                                FontSize="10"
                                FontWeight="Bold"
                                Background="Transparent"
                                BorderBrush="Transparent"
                                BorderThickness="1"
                                ToolTip="新增查詢"
                                Click="AddQueryTab_Click"/>
                    </StackPanel>
                </TabItem.Header>
                <Grid Background="White">
                    <views:ObjectExplorerView x:Name="objectExplorerView"/>
                </Grid>
            </TabItem>

            <!-- <TabItem Header="Untitled1" x:Name="Untitled1">
                <Grid Background="White">
                    <views:QueryEditorView x:Name="queryEditorView"/>
                </Grid>
            </TabItem> -->


        </TabControl>
       
    </Grid>
</UserControl>
