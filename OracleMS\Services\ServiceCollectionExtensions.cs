using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OracleMS.Interfaces;
using OracleMS.Repositories;
using Serilog;

namespace OracleMS.Services;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddOracleManagementStudioServices(this IServiceCollection services)
    {
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.File("logs/oracle-ms-.txt",
                rollingInterval: RollingInterval.Day,
                outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        // Add logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

        // Register data access layer services
        services.AddScoped<IConnectionProvider, OracleConnectionProvider>();
        services.AddScoped<IDatabaseRepository, OracleDatabaseRepository>();

        // Register configuration service
        services.AddSingleton<IConfigurationService, ConfigurationService>();

        // Register business logic services
        services.AddScoped<IConnectionService, ConnectionService>();
        services.AddScoped<IDatabaseService, DatabaseService>();
        services.AddScoped<IScriptGeneratorService, ScriptGeneratorService>();
        services.AddSingleton<ITransactionManager, TransactionManager>();
        services.AddScoped<IObjectEditorService, ObjectEditorService>();

        // Register ViewModels
        services.AddTransient<ViewModels.MainWindowViewModel>();
        services.AddTransient<ViewModels.ConnectionManagerViewModel>();
        services.AddTransient<ViewModels.ObjectExplorerViewModel>();
        services.AddTransient<ViewModels.QueryEditorViewModel>();
        services.AddTransient<ViewModels.DataGridViewModel>();
        services.AddTransient<ViewModels.TableDesignerViewModel>();
        
        // Register Object Editor ViewModels
        services.AddTransient<ViewModels.TableEditorViewModel>();
        services.AddTransient<ViewModels.ViewEditorViewModel>();
        services.AddTransient<ViewModels.ProcedureEditorViewModel>();
        services.AddTransient<ViewModels.FunctionEditorViewModel>();
        services.AddTransient<ViewModels.PackageEditorViewModel>();
        services.AddTransient<ViewModels.SequenceEditorViewModel>();
        services.AddTransient<ViewModels.TriggerEditorViewModel>();
        services.AddTransient<ViewModels.IndexEditorViewModel>();

        return services;
    }
}