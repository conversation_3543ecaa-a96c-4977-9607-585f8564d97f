using System.Collections.Generic;

namespace OracleMS.Models
{
    /// <summary>
    /// 索引編輯器參數類別，用於傳遞索引編輯器的初始化參數
    /// </summary>
    public class IndexEditorInfo
    {
        /// <summary>
        /// Schema名稱
        /// </summary>
        public string? Schema { get; set; }

        /// <summary>
        /// 資料表名稱
        /// </summary>
        public string? TableName { get; set; }

        /// <summary>
        /// 索引名稱
        /// </summary>
        public string? IndexName { get; set; }

        /// <summary>
        /// 是否為唯一索引
        /// </summary>
        public bool IsUnique { get; set; }

        /// <summary>
        /// 索引欄位集合
        /// </summary>
        public List<string> Columns { get; set; } = new();

        /// <summary>
        /// 是否為編輯模式
        /// </summary>
        public bool IsEditMode { get; set; }
    }
}