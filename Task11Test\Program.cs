using System;
using System.Threading.Tasks;
using OracleMS;

namespace Task11Test
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("Task 11 - 實作索引創建的資料庫操作 測試");
            Console.WriteLine("=".PadRight(50, '='));

            try
            {
                var test = new TestTask11Implementation();
                await test.RunAllTests();
                
                Console.WriteLine("\n" + "=".PadRight(50, '='));
                Console.WriteLine("Task 11 測試完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n測試執行失敗: {ex.Message}");
                Console.WriteLine($"詳細錯誤: {ex}");
                Environment.Exit(1);
            }

            Console.WriteLine("\n按任意鍵結束...");
            Console.ReadKey();
        }
    }
}