using System.Data;
using Microsoft.Extensions.Logging;
using Oracle.ManagedDataAccess.Client;
using OracleMS.Exceptions;
using OracleMS.Interfaces;
using OracleMS.Models;

namespace OracleMS.Repositories;

/// <summary>
/// Oracle 資料庫連線提供者
/// </summary>
public class OracleConnectionProvider : IConnectionProvider
{
    private readonly ILogger<OracleConnectionProvider> _logger;

    public OracleConnectionProvider(ILogger<OracleConnectionProvider> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 建立 Oracle 資料庫連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>資料庫連線物件</returns>
    /// <exception cref="OracleManagementException">連線建立失敗時拋出</exception>
    public async Task<IDbConnection> CreateConnectionAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            _logger.LogInformation("正在建立 Oracle 連線: {ConnectionName}", connectionInfo.Name);

            // 驗證連線資訊
            var (isValid, errorMessage) = connectionInfo.Validate();
            if (!isValid)
            {
                throw new OracleManagementException($"連線資訊驗證失敗: {errorMessage}");
            }

            // 建構連線字串
            var connectionString = BuildConnectionString(connectionInfo);
            //connectionString = "User Id=******;Password=******;Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*************)(PORT=1521))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME=AFLC)))";
            // 建立連線
            var connection = new OracleConnection(connectionString);

            // 開啟連線
            connection.Open();

            //await connection.OpenAsync();
            
            _logger.LogInformation("Oracle 連線建立成功: {ConnectionName}", connectionInfo.Name);
            
            return connection;
        }
        catch (OracleException ex)
        {
            _logger.LogError(ex, "Oracle 連線失敗: {ConnectionName}, 錯誤代碼: {ErrorCode}", 
                connectionInfo.Name, ex.Number);
            
            var errorMessage = GetUserFriendlyOracleErrorMessage(ex);
            throw new OracleManagementException($"Oracle 連線失敗: {errorMessage}", ex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建立 Oracle 連線時發生未預期錯誤: {ConnectionName}", connectionInfo.Name);
            throw new OracleManagementException($"建立連線時發生錯誤: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 測試 Oracle 資料庫連線
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>連線測試結果</returns>
    public async Task<bool> TestConnectionAsync(ConnectionInfo connectionInfo)
    {
        try
        {
            _logger.LogInformation("正在測試 Oracle 連線: {ConnectionName}", connectionInfo.Name);

            using var connection = await CreateConnectionAsync(connectionInfo);
            
            // 執行簡單查詢來驗證連線
            using var command = connection.CreateCommand();
            command.CommandText = "SELECT 1 FROM DUAL";
            command.CommandTimeout = 10;
            
            var result = await Task.Run(() => command.ExecuteScalar());
            
            _logger.LogInformation("Oracle 連線測試成功: {ConnectionName}", connectionInfo.Name);
            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Oracle 連線測試失敗: {ConnectionName}", connectionInfo.Name);
            return false;
        }
    }

    /// <summary>
    /// 建構 Oracle 連線字串
    /// </summary>
    /// <param name="connectionInfo">連線資訊</param>
    /// <returns>連線字串</returns>
    public string BuildConnectionString(ConnectionInfo connectionInfo)
    {
        try
        {
            var connectionString = "";
            //    = connectionInfo.Type switch
            //{
            //    ConnectionType.Basic => BuildBasicConnectionString(connectionInfo),
            //    ConnectionType.TNS => BuildTnsConnectionString(connectionInfo),
            //    ConnectionType.Advanced => BuildAdvancedConnectionString(connectionInfo),
            //    _ => BuildBasicConnectionString(connectionInfo)
            //};
            if (string.IsNullOrEmpty(connectionInfo.Username) || string.IsNullOrEmpty(connectionInfo.Password) || string.IsNullOrEmpty(connectionInfo.ServiceName))
            {
                throw new OracleManagementException("使用者名稱或密碼或服務名稱不能為空");
            }
            if (string.IsNullOrEmpty(connectionInfo.Server))
            {
                connectionString = BuildTnsConnectionString(connectionInfo);
            } else
            {
                connectionString = BuildAdvancedConnectionString(connectionInfo);
            }

            // 加入額外的連線參數
            //connectionString += "Connection Timeout=30;Command Timeout=60;Pooling=true;Min Pool Size=1;Max Pool Size=10;";

            _logger.LogDebug("建構連線字串完成，類型: {ConnectionType}", connectionInfo.Type);
            
            return connectionString;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "建構連線字串失敗: {ConnectionName}", connectionInfo.Name);
            throw new OracleManagementException($"建構連線字串失敗: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 建構基本連線字串
    /// </summary>
    private static string BuildBasicConnectionString(ConnectionInfo connectionInfo)
    {
        return $"Data Source={connectionInfo.Server}:{connectionInfo.Port}/{connectionInfo.ServiceName};" +
               $"User Id={connectionInfo.Username};Password={connectionInfo.Password};";
    }

    /// <summary>
    /// 建構 TNS 連線字串
    /// </summary>
    private static string BuildTnsConnectionString(ConnectionInfo connectionInfo)
    {
        return $"Data Source={connectionInfo.ServiceName};" +
               $"User Id={connectionInfo.Username};Password={connectionInfo.Password};";
    }

    /// <summary>
    /// 建構進階連線字串
    /// </summary>
    private static string BuildAdvancedConnectionString(ConnectionInfo connectionInfo)
    {
        return $"User Id={connectionInfo.Username};Password={connectionInfo.Password};Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={connectionInfo.Server})(PORT={(connectionInfo.Port <= 0 ? 1521 : connectionInfo.Port)}))(CONNECT_DATA=(SERVER=DEDICATED)(SERVICE_NAME={connectionInfo.ServiceName})))";
        //return $"Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={connectionInfo.Server})(PORT={(connectionInfo.Port <= 0 ? 1521 : connectionInfo.Port)}))" +
        //       $"(CONNECT_DATA=(SERVICE_NAME={connectionInfo.ServiceName})));" +
        //       $"User Id={connectionInfo.Username};Password={connectionInfo.Password};";
    }

    /// <summary>
    /// 將 Oracle 錯誤轉換為使用者友善的錯誤訊息
    /// </summary>
    /// <param name="ex">Oracle 例外</param>
    /// <returns>使用者友善的錯誤訊息</returns>
    private static string GetUserFriendlyOracleErrorMessage(OracleException ex)
    {
        return ex.Number switch
        {
            1017 => "使用者名稱或密碼無效",
            1045 => "使用者沒有 CREATE SESSION 權限",
            12154 => "TNS: 無法解析指定的連線識別碼",
            12514 => "TNS: 監聽程式目前無法識別連線描述元中請求的服務",
            12541 => "TNS: 沒有監聽程式",
            12170 => "TNS: 連線逾時",
            12504 => "TNS: 監聽程式在 CONNECT_DATA 中未獲得 SERVICE_NAME",
            28000 => "帳戶已鎖定",
            28001 => "密碼已過期",
            _ => $"Oracle 錯誤 (ORA-{ex.Number:D5}): {ex.Message}"
        };
    }
}