<UserControl x:Class="OracleMS.Views.Controls.ProgressIndicatorControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:OracleMS.Views.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="40" d:DesignWidth="300">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    
    <Grid Visibility="{Binding ShowProgress, Converter={StaticResource BooleanToVisibilityConverter}, Mode=OneWay}">
        <Border Background="#F0F0F0" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="3" Padding="8">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Status Message -->
                <TextBlock Grid.Row="0" Grid.Column="0"
                           Text="{Binding StatusMessage, Mode=OneWay}"
                           FontWeight="SemiBold"
                           Margin="0,0,0,4"/>
                
                <!-- Cancel Button -->
                <Button Grid.Row="0" Grid.Column="1"
                        Content="✕"
                        Command="{Binding CancelCommand, Mode=OneWay}"
                        Width="20" Height="20"
                        Margin="8,0,0,0"
                        ToolTip="取消操作"/>
                
                <!-- Progress Bar -->
                <ProgressBar Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2"
                             Height="10"
                             Minimum="0" Maximum="100"
                             Value="{Binding Progress, Mode=OneWay}"
                             IsIndeterminate="{Binding IsIndeterminateProgress, Mode=OneWay}"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>