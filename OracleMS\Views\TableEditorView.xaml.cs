using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using ICSharpCode.AvalonEdit;
using ICSharpCode.AvalonEdit.Highlighting;
using ICSharpCode.AvalonEdit.Highlighting.Xshd;
using System.Xml;
using System.IO;
using System.Reflection;
using OracleMS.ViewModels;

namespace OracleMS.Views
{
    /// <summary>
    /// TableEditorView.xaml 的互動邏輯
    /// </summary>
    public partial class TableEditorView : UserControl
    {
        public TableEditorView()
        {
            InitializeComponent();
            
            // 載入 SQL 語法高亮定義
            LoadSqlHighlighting();
            
            // 訂閱 Loaded 事件
            Loaded += TableEditorView_Loaded;
        }

        private void TableEditorView_Loaded(object sender, RoutedEventArgs e)
        {
            // 設定 DataContext 變更時的處理
            DataContextChanged += OnDataContextChanged;
        }

        private void OnDataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            // 取消訂閱舊的 ViewModel
            if (e.OldValue is TableEditorViewModel oldViewModel)
            {
                oldViewModel.PropertyChanged -= OnViewModelPropertyChanged;
            }

            // 訂閱新的 ViewModel
            if (e.NewValue is TableEditorViewModel newViewModel)
            {
                newViewModel.PropertyChanged += OnViewModelPropertyChanged;

                // 初始設定 DDL 預覽文字
                if (DdlPreviewEditor != null)
                {
                    DdlPreviewEditor.Text = newViewModel.DdlPreview ?? string.Empty;
                }
            }
        }

        private void OnViewModelPropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(TableEditorViewModel.DdlPreview))
            {
                if (sender is TableEditorViewModel viewModel && DdlPreviewEditor != null)
                {
                    DdlPreviewEditor.Text = viewModel.DdlPreview ?? string.Empty;
                }
            }
        }

        private void LoadSqlHighlighting()
        {
            try
            {
                // 嘗試載入 SQL 語法高亮定義
                // 假設 SQL 語法高亮定義檔案位於資源中
                string resourceName = "OracleMS.Resources.SQL.xshd";
                
                using (Stream stream = Assembly.GetExecutingAssembly().GetManifestResourceStream(resourceName))
                {
                    if (stream != null)
                    {
                        using (XmlReader reader = XmlReader.Create(stream))
                        {
                            DdlPreviewEditor.SyntaxHighlighting = HighlightingLoader.Load(reader, HighlightingManager.Instance);
                        }
                    }
                    else
                    {
                        // 如果找不到資源，使用預設的 SQL 高亮
                        DdlPreviewEditor.SyntaxHighlighting = HighlightingManager.Instance.GetDefinition("SQL");
                    }
                }
            }
            catch (Exception)
            {
                // 發生錯誤時，嘗試使用預設的 SQL 高亮
                try
                {
                    DdlPreviewEditor.SyntaxHighlighting = HighlightingManager.Instance.GetDefinition("SQL");
                }
                catch
                {
                    // 忽略錯誤，使用無高亮
                }
            }
        }
    }
}