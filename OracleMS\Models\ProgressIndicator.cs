using System;
using System.Threading;
using System.Threading.Tasks;

namespace OracleMS.Models
{
    /// <summary>
    /// 進度指示器類別，用於追蹤和報告長時間操作的進度
    /// </summary>
    public class ProgressIndicator : IDisposable
    {
        private readonly Action<string> _statusUpdateCallback;
        private readonly Action<double> _progressUpdateCallback;
        private readonly CancellationTokenSource _cancellationTokenSource;
        private bool _isIndeterminate;
        private double _progress;
        private string _statusMessage;
        private bool _isCompleted;
        private bool _isDisposed;

        /// <summary>
        /// 取得進度值 (0-100)
        /// </summary>
        public double Progress => _progress;

        /// <summary>
        /// 取得狀態訊息
        /// </summary>
        public string StatusMessage => _statusMessage;

        /// <summary>
        /// 取得是否為不確定進度
        /// </summary>
        public bool IsIndeterminate => _isIndeterminate;

        /// <summary>
        /// 取得是否已完成
        /// </summary>
        public bool IsCompleted => _isCompleted;

        /// <summary>
        /// 取得取消權杖
        /// </summary>
        public CancellationToken CancellationToken => _cancellationTokenSource.Token;

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="statusUpdateCallback">狀態更新回呼</param>
        /// <param name="progressUpdateCallback">進度更新回呼</param>
        public ProgressIndicator(Action<string> statusUpdateCallback, Action<double> progressUpdateCallback)
        {
            _statusUpdateCallback = statusUpdateCallback ?? throw new ArgumentNullException(nameof(statusUpdateCallback));
            _progressUpdateCallback = progressUpdateCallback ?? throw new ArgumentNullException(nameof(progressUpdateCallback));
            _cancellationTokenSource = new CancellationTokenSource();
            _isIndeterminate = true;
            _progress = 0;
            _statusMessage = string.Empty;
            _isCompleted = false;
        }

        /// <summary>
        /// 開始操作
        /// </summary>
        /// <param name="operationName">操作名稱</param>
        /// <param name="isIndeterminate">是否為不確定進度</param>
        public void Start(string operationName, bool isIndeterminate = true)
        {
            _isIndeterminate = isIndeterminate;
            _progress = 0;
            _isCompleted = false;
            
            UpdateStatus($"正在{operationName}...");
            UpdateProgress(0);
        }

        /// <summary>
        /// 更新進度
        /// </summary>
        /// <param name="progress">進度值 (0-100)</param>
        /// <param name="statusMessage">狀態訊息</param>
        public void UpdateProgress(double progress, string? statusMessage = null)
        {
            _progress = Math.Clamp(progress, 0, 100);
            
            if (statusMessage != null)
            {
                UpdateStatus(statusMessage);
            }
            
            _progressUpdateCallback(_progress);
        }

        /// <summary>
        /// 更新狀態
        /// </summary>
        /// <param name="statusMessage">狀態訊息</param>
        public void UpdateStatus(string statusMessage)
        {
            _statusMessage = statusMessage;
            _statusUpdateCallback(_statusMessage);
        }

        /// <summary>
        /// 完成操作
        /// </summary>
        /// <param name="successMessage">成功訊息</param>
        public void Complete(string successMessage)
        {
            _isCompleted = true;
            _progress = 100;
            
            UpdateStatus(successMessage);
            UpdateProgress(100);
        }

        /// <summary>
        /// 操作失敗
        /// </summary>
        /// <param name="errorMessage">錯誤訊息</param>
        public void Fail(string errorMessage)
        {
            _isCompleted = true;
            
            UpdateStatus($"失敗：{errorMessage}");
        }

        /// <summary>
        /// 取消操作
        /// </summary>
        public void Cancel()
        {
            if (!_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
                UpdateStatus("操作已取消");
            }
        }

        /// <summary>
        /// 等待指定時間並更新進度
        /// </summary>
        /// <param name="delayMs">延遲毫秒數</param>
        /// <param name="progressIncrement">進度增量</param>
        /// <returns>非同步工作</returns>
        public async Task DelayWithProgressAsync(int delayMs, double progressIncrement = 5)
        {
            try
            {
                await Task.Delay(delayMs, _cancellationTokenSource.Token);
                UpdateProgress(Math.Min(_progress + progressIncrement, 99));
            }
            catch (OperationCanceledException)
            {
                // 忽略取消例外
            }
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        /// <param name="disposing">是否正在釋放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    _cancellationTokenSource.Dispose();
                }

                _isDisposed = true;
            }
        }
    }
}