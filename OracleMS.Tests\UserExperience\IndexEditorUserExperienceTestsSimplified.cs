using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using OracleMS.Interfaces;
using OracleMS.Models;
using OracleMS.ViewModels;
using Xunit;

namespace OracleMS.Tests.UserExperience
{
    /// <summary>
    /// IndexEditor 用戶體驗測試 - 簡化版本，專注於UI行為和用戶互動體驗
    /// </summary>
    public class IndexEditorUserExperienceTestsSimplified
    {
        private readonly Mock<IDatabaseService> _mockDatabaseService;
        private readonly Mock<IScriptGeneratorService> _mockScriptGeneratorService;
        private readonly Mock<IObjectEditorService> _mockObjectEditorService;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<ILogger> _mockLogger;

        public IndexEditorUserExperienceTestsSimplified()
        {
            _mockDatabaseService = new Mock<IDatabaseService>();
            _mockScriptGeneratorService = new Mock<IScriptGeneratorService>();
            _mockObjectEditorService = new Mock<IObjectEditorService>();
            _mockConnection = new Mock<IDbConnection>();
            _mockLogger = new Mock<ILogger>();

            SetupMockServices();
        }

        private void SetupMockServices()
        {
            _mockDatabaseService.Setup(x => x.GetSchemasAsync(It.IsAny<IDbConnection>()))
                .ReturnsAsync(new List<string> { "HR", "SALES", "INVENTORY" });

            _mockDatabaseService.Setup(x => x.GetTablesBySchemaAsync(It.IsAny<IDbConnection>(), "HR"))
                .ReturnsAsync(new List<DatabaseObject> 
                { 
                    new DatabaseObject { Name = "EMPLOYEES", Type = DatabaseObjectType.Table },
                    new DatabaseObject { Name = "DEPARTMENTS", Type = DatabaseObjectType.Table },
                    new DatabaseObject { Name = "POSITIONS", Type = DatabaseObjectType.Table }
                });
        }

        /// <summary>
        /// 測試創建模式下的用戶體驗
        /// Requirements: 1.1, 4.1
        /// </summary>
        [Fact]
        public async Task CreateModeUserExperience_ShouldProvideIntuitiveBehavior()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // Act & Assert - 用戶體驗測試

            // UX1: 初始狀態應該清楚顯示這是創建模式
            Assert.True(viewModel.IsCreateMode);
            Assert.False(viewModel.IsEditMode);
            Assert.Empty(viewModel.SelectedSchema);
            Assert.Empty(viewModel.SelectedTable);

            // UX2: Schema 下拉選單應該有可用選項
            Assert.Contains("HR", viewModel.AvailableSchemas);
            Assert.Contains("SALES", viewModel.AvailableSchemas);

            // UX3: 索引名稱欄位應該可以編輯
            viewModel.IndexDefinition.Name = "IDX_EMPLOYEES_EMAIL";
            Assert.Equal("IDX_EMPLOYEES_EMAIL", viewModel.IndexDefinition.Name);

            // UX4: 更新命令在創建模式下應該不可用
            Assert.False(viewModel.UpdateIndexCommand.CanExecute(null));
        }

        /// <summary>
        /// 測試編輯模式下的用戶體驗
        /// Requirements: 1.2, 4.2
        /// </summary>
        [Fact]
        public async Task EditModeUserExperience_ShouldProvideAppropriateRestrictions()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "EMPLOYEES",
                IndexName = "IDX_EMPLOYEES_EXISTING",
                IsEditMode = true,
                IsUnique = false,
                Columns = new List<string> { "FIRST_NAME", "LAST_NAME" }
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // Act & Assert - 編輯模式用戶體驗測試

            // UX1: 初始狀態應該清楚顯示這是編輯模式
            Assert.False(viewModel.IsCreateMode);
            Assert.True(viewModel.IsEditMode);

            // UX2: 基本資訊應該預先填入
            Assert.Equal("HR", viewModel.SelectedSchema);
            Assert.Equal("EMPLOYEES", viewModel.SelectedTable);
            Assert.Equal("IDX_EMPLOYEES_EXISTING", viewModel.IndexDefinition.Name);

            // UX3: 創建命令在編輯模式下應該不可用
            Assert.False(viewModel.CreateIndexCommand.CanExecute(null));
        }

        /// <summary>
        /// 測試欄位選擇的用戶體驗
        /// Requirements: 2.1, 2.2, 2.3, 2.4
        /// </summary>
        [Fact]
        public async Task ColumnSelectionUserExperience_ShouldBeIntuitive()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "EMPLOYEES",
                IndexName = "IDX_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 手動設定測試欄位
            viewModel.AvailableColumns.Add("EMPLOYEE_ID");
            viewModel.AvailableColumns.Add("FIRST_NAME");
            viewModel.AvailableColumns.Add("EMAIL");

            // Act & Assert - 欄位選擇用戶體驗測試

            // UX1: 雙 ListBox 應該正確顯示可用和已選欄位
            Assert.True(viewModel.AvailableColumns.Count > 0);
            Assert.Empty(viewModel.SelectedColumns);

            // UX2: 新增欄位應該提供即時回饋
            var originalAvailableCount = viewModel.AvailableColumns.Count;
            viewModel.AddColumnCommand.Execute("EMAIL");

            Assert.Equal(originalAvailableCount - 1, viewModel.AvailableColumns.Count);
            Assert.Single(viewModel.SelectedColumns);
            Assert.Contains("EMAIL", viewModel.SelectedColumns);

            // UX3: 移除欄位應該恢復到可用列表
            viewModel.RemoveColumnCommand.Execute("EMAIL");

            Assert.Equal(originalAvailableCount, viewModel.AvailableColumns.Count);
            Assert.Empty(viewModel.SelectedColumns);
            Assert.Contains("EMAIL", viewModel.AvailableColumns);

            // UX4: 多欄位選擇和排序
            var columnsToAdd = new[] { "EMPLOYEE_ID", "FIRST_NAME", "EMAIL" };
            foreach (var column in columnsToAdd)
            {
                viewModel.AddColumnCommand.Execute(column);
            }

            Assert.Equal(3, viewModel.SelectedColumns.Count);
            Assert.Equal("EMPLOYEE_ID", viewModel.SelectedColumns[0]);
            Assert.Equal("FIRST_NAME", viewModel.SelectedColumns[1]);
            Assert.Equal("EMAIL", viewModel.SelectedColumns[2]);

            // UX5: 欄位順序調整應該提供清楚的視覺回饋
            viewModel.MoveColumnUpCommand.Execute("FIRST_NAME");

            Assert.Equal("FIRST_NAME", viewModel.SelectedColumns[0]);
            Assert.Equal("EMPLOYEE_ID", viewModel.SelectedColumns[1]);
            Assert.Equal("EMAIL", viewModel.SelectedColumns[2]);

            // UX6: 邊界條件應該有適當的命令狀態
            Assert.False(viewModel.MoveColumnUpCommand.CanExecute("FIRST_NAME")); // 已經是第一個
            Assert.False(viewModel.MoveColumnDownCommand.CanExecute("EMAIL")); // 已經是最後一個
            Assert.True(viewModel.MoveColumnDownCommand.CanExecute("FIRST_NAME")); // 可以下移
            Assert.True(viewModel.MoveColumnUpCommand.CanExecute("EMAIL")); // 可以上移

            // UX7: 命令參數驗證
            Assert.False(viewModel.AddColumnCommand.CanExecute(null));
            Assert.False(viewModel.AddColumnCommand.CanExecute(""));
            Assert.False(viewModel.AddColumnCommand.CanExecute("NON_EXISTENT_COLUMN"));
        }

        /// <summary>
        /// 測試驗證回饋的用戶體驗
        /// Requirements: 3.1, 3.2
        /// </summary>
        [Fact]
        public async Task ValidationFeedbackUserExperience_ShouldBeHelpful()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "EMPLOYEES",
                IndexName = "",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // Act & Assert - 驗證回饋用戶體驗測試

            // UX1: 即時驗證應該預設啟用
            Assert.True(viewModel.IsRealTimeValidationEnabled);

            // UX2: 初始狀態應該顯示驗證錯誤
            await Task.Delay(100); // 等待即時驗證觸發
            Assert.True(viewModel.HasValidationErrors);
            Assert.False(string.IsNullOrEmpty(viewModel.ValidationErrorMessage));

            // UX3: 錯誤訊息應該具體且有幫助
            Assert.Contains("索引名稱", viewModel.ValidationErrorMessage);

            // UX4: 修正錯誤後應該即時更新驗證狀態
            viewModel.IndexDefinition.Name = "IDX_EMPLOYEES_TEST";
            await Task.Delay(100);

            // 仍然會有錯誤，因為沒有選擇欄位
            Assert.True(viewModel.HasValidationErrors);
            Assert.Contains("欄位", viewModel.ValidationErrorMessage);

            // UX5: 完全修正後應該顯示成功狀態
            viewModel.AvailableColumns.Add("EMAIL");
            viewModel.AddColumnCommand.Execute("EMAIL");
            await Task.Delay(100);

            Assert.False(viewModel.HasValidationErrors);
            Assert.Contains("完成", viewModel.ValidationStatusText);

            // UX6: 警告應該與錯誤區分顯示
            viewModel.IndexDefinition.Name = "BADNAME"; // 沒有 IDX 前綴
            await Task.Delay(100);

            Assert.False(viewModel.HasValidationErrors); // 沒有錯誤
            Assert.True(viewModel.HasValidationWarnings); // 但有警告
            Assert.Contains("警告", viewModel.ValidationStatusText);

            // UX7: 驗證摘要應該提供清楚的狀態概覽
            viewModel.IndexDefinition.Name = ""; // 重新引入錯誤
            await Task.Delay(100);

            Assert.True(viewModel.ErrorCount > 0);
            Assert.False(string.IsNullOrEmpty(viewModel.ValidationSummary));

            // UX8: 用戶應該能夠關閉即時驗證
            viewModel.IsRealTimeValidationEnabled = false;
            Assert.False(viewModel.IsRealTimeValidationEnabled);
        }

        /// <summary>
        /// 測試模式切換的視覺指示
        /// Requirements: 4.1, 4.2, 4.3
        /// </summary>
        [Fact]
        public async Task ModeIndicators_ShouldProvideVisualFeedback()
        {
            // 測試創建模式指示器
            var createModeInfo = new IndexEditorInfo
            {
                IsEditMode = false,
                Columns = new List<string>()
            };

            var createViewModel = new IndexEditorViewModel(
                createModeInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 驗證創建模式的視覺狀態
            Assert.True(createViewModel.IsCreateMode);
            Assert.False(createViewModel.IsEditMode);

            // 測試編輯模式指示器
            var editModeInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "EMPLOYEES",
                IndexName = "IDX_EXISTING",
                IsEditMode = true,
                Columns = new List<string> { "ID" }
            };

            var editViewModel = new IndexEditorViewModel(
                editModeInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 驗證編輯模式的視覺狀態
            Assert.False(editViewModel.IsCreateMode);
            Assert.True(editViewModel.IsEditMode);

            // 驗證模式特定的命令可用性
            Assert.False(createViewModel.UpdateIndexCommand.CanExecute(null));
            Assert.False(editViewModel.CreateIndexCommand.CanExecute(null));
        }

        /// <summary>
        /// 測試錯誤恢復的用戶體驗
        /// Requirements: 3.2, 3.3
        /// </summary>
        [Fact]
        public async Task ErrorRecoveryUserExperience_ShouldBeGraceful()
        {
            // Arrange
            var indexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "EMPLOYEES",
                IndexName = "IDX_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // Act & Assert - 錯誤恢復用戶體驗測試

            // 錯誤場景1: 嘗試創建無效索引（無欄位）
            var validationResult = viewModel.IndexDefinition.Validate();
            Assert.False(validationResult.IsValid);
            Assert.False(viewModel.CreateIndexCommand.CanExecute(null));

            // 恢復1: 新增欄位
            viewModel.AvailableColumns.Add("USERNAME");
            viewModel.AddColumnCommand.Execute("USERNAME");
            
            validationResult = viewModel.IndexDefinition.Validate();
            Assert.True(validationResult.IsValid);

            // 錯誤場景2: 移除所有欄位（回到無效狀態）
            viewModel.RemoveColumnCommand.Execute("USERNAME");
            
            validationResult = viewModel.IndexDefinition.Validate();
            Assert.False(validationResult.IsValid);
            Assert.False(viewModel.CreateIndexCommand.CanExecute(null));

            // 恢復2: 重新新增欄位
            viewModel.AddColumnCommand.Execute("USERNAME");
            validationResult = viewModel.IndexDefinition.Validate();
            Assert.True(validationResult.IsValid);
        }

        /// <summary>
        /// 測試大量資料處理的用戶體驗
        /// Requirements: 1.3, 2.1
        /// </summary>
        [Fact]
        public async Task LargeDataHandlingUserExperience_ShouldBeResponsive()
        {
            // Arrange - 設定大量資料的場景
            var indexInfo = new IndexEditorInfo
            {
                Schema = "HR",
                TableName = "LARGE_TABLE",
                IndexName = "IDX_LARGE_TEST",
                IsEditMode = false,
                Columns = new List<string>()
            };

            var viewModel = new IndexEditorViewModel(
                indexInfo,
                _mockDatabaseService.Object,
                _mockScriptGeneratorService.Object,
                _mockObjectEditorService.Object,
                () => _mockConnection.Object,
                _mockLogger.Object
            );

            await Task.Delay(50);

            // 手動新增大量欄位來模擬大量資料情況
            for (int i = 1; i <= 50; i++)
            {
                viewModel.AvailableColumns.Add($"COLUMN_{i:D2}");
            }

            // Act & Assert - 大量資料用戶體驗測試

            // UX1: 大量欄位應該正確載入
            Assert.Equal(50, viewModel.AvailableColumns.Count);
            Assert.Contains("COLUMN_01", viewModel.AvailableColumns);
            Assert.Contains("COLUMN_50", viewModel.AvailableColumns);

            // UX2: 欄位選擇操作應該保持響應性
            var startTime = DateTime.Now;
            
            // 選擇多個欄位
            for (int i = 1; i <= 10; i++)
            {
                viewModel.AddColumnCommand.Execute($"COLUMN_{i:D2}");
            }

            var elapsedTime = DateTime.Now - startTime;
            Assert.True(elapsedTime.TotalSeconds < 1, "欄位選擇操作應該在1秒內完成");

            // UX3: 大量已選欄位的排序操作應該正常工作
            Assert.Equal(10, viewModel.SelectedColumns.Count);
            
            // 測試移動操作
            viewModel.MoveColumnUpCommand.Execute("COLUMN_05");
            Assert.Equal("COLUMN_05", viewModel.SelectedColumns[3]); // 從位置4移到位置3

            // UX4: 驗證大量欄位時應該保持性能
            startTime = DateTime.Now;
            var validationResult = viewModel.IndexDefinition.Validate();
            elapsedTime = DateTime.Now - startTime;

            Assert.True(elapsedTime.TotalMilliseconds < 500, "驗證操作應該在500毫秒內完成");
            Assert.True(validationResult.IsValid);
        }
    }
}