# DataGridExtensions 使用指南

## 概述
DataGridExtensions 是一個免費的開源 WPF DataGrid 增強套件，為標準的 WPF DataGrid 提供了強大的篩選和排序功能。

## 主要功能

### 1. 自動篩選功能
- **啟用方式**：在 XAML 中添加 `dgx:DataGridFilter.IsAutoFilterEnabled="True"`
- **使用方法**：
  - 每個欄位標題右側會出現一個小的下拉箭頭
  - 點擊箭頭可以開啟篩選選單
  - 可以選擇特定的值進行篩選
  - 支援多重選擇篩選
  - 支援文字搜尋篩選

### 2. 初始排序
- **啟用方式**：在 XAML 中添加 `dgx:Tools.ApplyInitialSorting="True"`
- **功能**：自動應用初始的排序設定

### 3. 增強的使用者體驗
- 保持所有原有的 DataGrid 功能
- 更直觀的篩選介面
- 更好的大數據處理效能

## 在專案中的應用

### QueryEditorView.xaml
```xml
<DataGrid x:Name="ResultsDataGrid"
          ItemsSource="{Binding Data.DefaultView, IsAsync=True}"
          AutoGenerateColumns="True"
          IsReadOnly="True"
          dgx:DataGridFilter.IsAutoFilterEnabled="True"
          dgx:Tools.ApplyInitialSorting="True">
```

### DataGridView.xaml
```xml
<DataGrid x:Name="MainDataGrid"
          ItemsSource="{Binding DataView}" 
          SelectedItem="{Binding SelectedRow}"
          AutoGenerateColumns="True"
          dgx:DataGridFilter.IsAutoFilterEnabled="True"
          dgx:Tools.ApplyInitialSorting="True">
```

## 使用步驟

### 1. 查看查詢結果
1. 執行 SQL 查詢
2. 在結果頁籤中查看資料
3. 注意每個欄位標題右側的篩選按鈕

### 2. 使用篩選功能
1. 點擊任何欄位標題右側的下拉箭頭
2. 在篩選選單中：
   - 取消勾選不需要的值
   - 使用搜尋框快速找到特定值
   - 點擊「確定」應用篩選
3. 可以同時對多個欄位進行篩選

### 3. 清除篩選
1. 點擊篩選按鈕
2. 選擇「全選」或「清除篩選」
3. 點擊「確定」

### 4. 排序功能
1. 點擊欄位標題進行排序
2. 再次點擊可以切換升序/降序
3. 按住 Ctrl 鍵點擊可以進行多欄位排序

## 優勢

### 相比原始 DataGrid
- ✅ 內建篩選功能，無需額外程式碼
- ✅ 更好的使用者體驗
- ✅ 支援大數據集的高效篩選
- ✅ 完全免費，無授權問題

### 相比 Syncfusion
- ✅ 免費開源
- ✅ 輕量級，不會影響啟動速度
- ✅ 基於標準 WPF DataGrid，相容性極佳
- ✅ 無需學習新的 API

## 技術細節

### 命名空間
```xml
xmlns:dgx="urn:tom-englert.de/DataGridExtensions"
```

### 主要屬性
- `dgx:DataGridFilter.IsAutoFilterEnabled` - 啟用自動篩選
- `dgx:Tools.ApplyInitialSorting` - 啟用初始排序

### NuGet 套件
```xml
<PackageReference Include="DataGridExtensions" Version="2.6.0" />
```

## 注意事項
1. 篩選功能會自動處理不同的資料類型
2. 對於大數據集，篩選操作是高效的
3. 所有原有的 DataGrid 功能都保持不變
4. 可以與現有的程式碼完全相容

## 疑難排解
如果篩選功能沒有出現：
1. 確認已添加正確的命名空間
2. 確認已設定 `dgx:DataGridFilter.IsAutoFilterEnabled="True"`
3. 確認 DataGrid 有資料綁定
4. 檢查是否有編譯錯誤
