using System;

namespace OracleMS.Interfaces
{
    /// <summary>
    /// Interface for enhanced application logging
    /// </summary>
    public interface IApplicationLogger
    {
        /// <summary>
        /// Log a debug message
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The debug message</param>
        void LogDebug(object source, string message);

        /// <summary>
        /// Log an informational message
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The informational message</param>
        void LogInformation(object source, string message);

        /// <summary>
        /// Log a warning message
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The warning message</param>
        void LogWarning(object source, string message);

        /// <summary>
        /// Log an error message
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The error message</param>
        void LogError(object source, string message);

        /// <summary>
        /// Log an error message with exception details
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The error message</param>
        /// <param name="exception">The exception that occurred</param>
        void LogError(object source, string message, Exception exception);

        /// <summary>
        /// Log a critical error message
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The critical error message</param>
        void LogCritical(object source, string message);

        /// <summary>
        /// Log a critical error message with exception details
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="message">The critical error message</param>
        /// <param name="exception">The exception that occurred</param>
        void LogCritical(object source, string message, Exception exception);

        /// <summary>
        /// Log a user action
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="action">The action performed by the user</param>
        /// <param name="details">Additional details about the action</param>
        void LogUserAction(object source, string action, string details = null);

        /// <summary>
        /// Log a database operation
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="operation">The database operation</param>
        /// <param name="details">Additional details about the operation</param>
        /// <param name="duration">The duration of the operation in milliseconds</param>
        void LogDatabaseOperation(object source, string operation, string details = null, long? duration = null);

        /// <summary>
        /// Log a performance metric
        /// </summary>
        /// <param name="source">The source object or component</param>
        /// <param name="operation">The operation being measured</param>
        /// <param name="durationMs">The duration of the operation in milliseconds</param>
        /// <param name="details">Additional details about the operation</param>
        void LogPerformance(object source, string operation, long durationMs, string details = null);
    }
}