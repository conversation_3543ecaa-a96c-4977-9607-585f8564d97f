using System.Text.RegularExpressions;

namespace OracleMS.Services;

/// <summary>
/// Utility class for detecting DDL (Data Definition Language) statements
/// that should trigger automatic transaction commits
/// </summary>
public static class DdlStatementDetector
{
    /// <summary>
    /// DDL statement patterns that should trigger auto-commit
    /// </summary>
    private static readonly Regex[] DdlPatterns = new[]
    {
        new Regex(@"^\s*CREATE\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled),
        new Regex(@"^\s*ALTER\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled),
        new Regex(@"^\s*DROP\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled),
        new Regex(@"^\s*TRUNCATE\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled),
        new Regex(@"^\s*RENAME\s+", RegexOptions.IgnoreCase | RegexOptions.Compiled)
    };

    /// <summary>
    /// Determines if the given SQL statement is a DDL statement that should trigger auto-commit
    /// </summary>
    /// <param name="sql">The SQL statement to evaluate</param>
    /// <returns>True if the statement is a DDL statement requiring auto-commit, false otherwise</returns>
    public static bool IsDdlStatement(string sql)
    {
        if (string.IsNullOrWhiteSpace(sql))
            return false;

        // Remove comments and check if the SQL statement matches any DDL patterns
        var cleanedSql = RemoveComments(sql);
        return DdlPatterns.Any(pattern => pattern.IsMatch(cleanedSql.Trim()));
    }

    /// <summary>
    /// Gets the type of DDL statement from the SQL text
    /// </summary>
    /// <param name="sql">The SQL statement to analyze</param>
    /// <returns>The DDL statement type or null if not a DDL statement</returns>
    public static string? GetDdlStatementType(string sql)
    {
        if (string.IsNullOrWhiteSpace(sql))
            return null;

        var cleanedSql = RemoveComments(sql).Trim();
        
        foreach (var pattern in DdlPatterns)
        {
            var match = pattern.Match(cleanedSql);
            if (match.Success)
            {
                // Extract the DDL keyword (CREATE, ALTER, DROP, etc.)
                return match.Value.Trim().ToUpperInvariant();
            }
        }

        return null;
    }

    /// <summary>
    /// Checks if a SQL statement contains multiple statements and identifies any DDL statements
    /// </summary>
    /// <param name="sql">The SQL text that may contain multiple statements</param>
    /// <returns>True if any statement in the batch is a DDL statement</returns>
    public static bool ContainsDdlStatement(string sql)
    {
        if (string.IsNullOrWhiteSpace(sql))
            return false;

        // Split by common SQL statement separators
        var statements = sql.Split(new[] { ';', '\n' }, StringSplitOptions.RemoveEmptyEntries);
        
        return statements.Any(statement => IsDdlStatement(statement));
    }

    /// <summary>
    /// Removes SQL comments from the given SQL text
    /// </summary>
    /// <param name="sql">The SQL text to clean</param>
    /// <returns>SQL text with comments removed</returns>
    private static string RemoveComments(string sql)
    {
        if (string.IsNullOrWhiteSpace(sql))
            return sql;

        // Remove single-line comments (-- comment)
        var lines = sql.Split('\n');
        var cleanedLines = new List<string>();
        
        foreach (var line in lines)
        {
            var commentIndex = line.IndexOf("--", StringComparison.Ordinal);
            if (commentIndex >= 0)
            {
                cleanedLines.Add(line.Substring(0, commentIndex));
            }
            else
            {
                cleanedLines.Add(line);
            }
        }
        
        var result = string.Join("\n", cleanedLines);
        
        // Remove multi-line comments (/* comment */)
        var multiLineCommentPattern = new Regex(@"/\*.*?\*/", RegexOptions.Singleline | RegexOptions.Compiled);
        result = multiLineCommentPattern.Replace(result, " ");
        
        return result;
    }
}