# SQL Folding Strategy 實現說明

## 概述

已成功為所有 AvalonEdit 編輯器實現了 SQL/PL-SQL 代碼摺疊功能，包括：

- **ProcedureEditorView** - 程序編輯器
- **FunctionEditorView** - 函數編輯器  
- **TriggerEditorView** - 觸發器編輯器
- **PackageEditorView** - 套件編輯器（規格和主體）
- **QueryEditorView** - 查詢編輯器

## 實現的摺疊類型

### 1. 基本結構摺疊
- **BEGIN...END** 區塊
- **多行註解** (`/* ... */`)

### 2. 命名區塊摺疊
- **PROCEDURE** 程序
- **FUNCTION** 函數
- **PACKAGE** 套件
- **PACKAGE BODY** 套件主體
- **TRIGGER** 觸發器

### 3. 控制結構摺疊
- **IF...END IF** 條件語句
- **CASE...END CASE** 選擇語句
- **LOOP...END LOOP** 迴圈語句
- **EXCEPTION...END** 例外處理

### 4. 巢狀結構支援
- 支援巢狀的 IF、CASE、LOOP 語句
- 正確處理多層巢狀結構
- 避免重疊摺疊區域

## 使用方式

### 在編輯器中的使用

1. **自動初始化**: 當編輯器載入時，folding 功能會自動初始化
2. **即時更新**: 當文字內容變更時，folding 區域會自動更新
3. **視覺指示**: 在編輯器左側會顯示摺疊/展開的控制項

### 範例代碼

```sql
CREATE OR REPLACE PROCEDURE example_proc(p_id IN NUMBER)
IS
    v_count NUMBER;
BEGIN                          -- 可摺疊區域開始
    IF p_id > 0 THEN          -- 巢狀摺疊區域
        FOR i IN 1..p_id LOOP -- 更深層的巢狀摺疊
            SELECT COUNT(*) INTO v_count 
            FROM users WHERE id = i;
            
            IF v_count > 0 THEN
                UPDATE users SET status = 'ACTIVE' WHERE id = i;
            END IF;
        END LOOP;
    END IF;
END example_proc;             -- 可摺疊區域結束
```

## 技術實現細節

### 核心類別
- **SqlFoldingStrategy**: 主要的摺疊策略類別
- **FoldingManager**: AvalonEdit 的摺疊管理器
- **NewFolding**: 摺疊區域定義

### 實現方式

1. **初始化摺疊**:
```csharp
foldingManager = FoldingManager.Install(textEditor.TextArea);
foldingStrategy = new SqlFoldingStrategy();
```

2. **更新摺疊**:
```csharp
private void UpdateFolding()
{
    if (foldingManager != null && foldingStrategy != null && textEditor.Document != null)
    {
        foldingStrategy.UpdateFoldings(foldingManager, textEditor.Document);
    }
}
```

3. **文字變更時自動更新**:
```csharp
textEditor.TextChanged += (sender, e) => UpdateFolding();
```

## 支援的 SQL/PL-SQL 語法

### 程序和函數
- `CREATE [OR REPLACE] PROCEDURE name IS/AS ... END name;`
- `CREATE [OR REPLACE] FUNCTION name RETURN type IS/AS ... END name;`

### 套件
- `CREATE [OR REPLACE] PACKAGE name AS ... END name;`
- `CREATE [OR REPLACE] PACKAGE BODY name AS ... END name;`

### 觸發器
- `CREATE [OR REPLACE] TRIGGER name ... BEGIN ... END;`

### 控制結構
- `IF condition THEN ... END IF;`
- `CASE expression WHEN ... END CASE;`
- `FOR/WHILE/LOOP ... END LOOP;`

## 特色功能

1. **智能巢狀處理**: 正確識別和處理巢狀結構
2. **重疊避免**: 自動移除重疊的摺疊區域
3. **最小長度檢查**: 只為有意義的代碼區塊創建摺疊
4. **錯誤處理**: 包含完整的錯誤處理和調試輸出

## 測試

已包含完整的單元測試來驗證各種摺疊場景：
- 基本 BEGIN...END 摺疊
- 程序、函數、套件摺疊
- 控制結構摺疊
- 巢狀結構摺疊
- 註解摺疊

## 使用建議

1. **大型 PL/SQL 文件**: 使用摺疊功能可以更好地組織和瀏覽代碼
2. **調試**: 摺疊不相關的代碼區塊，專注於當前工作的部分
3. **代碼審查**: 快速瀏覽代碼結構和主要邏輯流程

這個實現提供了完整的 SQL/PL-SQL 代碼摺疊功能，大大提升了代碼編輯和瀏覽的體驗。
