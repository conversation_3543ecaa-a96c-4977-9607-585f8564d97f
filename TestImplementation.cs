using OracleMS.Models;
using System;
using System.Collections.Generic;

// Simple test to verify the implementation works
class TestImplementation
{
    static void Main()
    {
        Console.WriteLine("Running implementation tests...");
        
        try
        {
            TestTask1Implementation.RunTests();
            Console.WriteLine();
            
            TestColumnSelectionManager.RunTests();
            Console.WriteLine();
            
            TestTask3Implementation.RunTests();
            Console.WriteLine();
            
            Console.WriteLine("All tests completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Test failed: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
        
        Console.WriteLine("Press any key to exit...");
        Console.ReadKey();
    }
}