using System.Linq;
using Xunit;
using OracleMS.Models;

namespace OracleMS.Tests.Models
{
    public class IndexDefinitionValidationTests
    {
        [Fact]
        public void Validate_EmptyIndexName_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE"
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("索引名稱不能為空")));
        }

        [Fact]
        public void Validate_InvalidIndexNameFormat_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "123_INVALID", // 不能以數字開頭
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE"
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("索引名稱格式無效")));
        }

        [Fact]
        public void Validate_IndexNameTooLong_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "THIS_IS_A_VERY_LONG_INDEX_NAME_THAT_EXCEEDS_THIRTY_CHARACTERS",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE"
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("索引名稱長度不能超過 30 個字元")));
        }

        [Fact]
        public void Validate_NoColumns_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE"
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("索引必須指定至少一個欄位")));
        }

        [Fact]
        public void Validate_DuplicateColumns_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 2 } // 重複
                }
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("欄位 'COLUMN1' 重複")));
        }

        [Fact]
        public void Validate_TooManyColumns_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>()
            };

            // 新增超過 32 個欄位
            for (int i = 1; i <= 33; i++)
            {
                indexDefinition.Columns.Add(new IndexColumnDefinition 
                { 
                    ColumnName = $"COLUMN{i}", 
                    Position = i 
                });
            }

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("索引欄位數量不能超過 32 個")));
        }

        [Fact]
        public void Validate_InvalidColumnPositions_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "COLUMN2", Position = 3 } // 跳過位置 2
                }
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("欄位位置必須從 1 開始且連續")));
        }

        [Fact]
        public void Validate_CreateMode_MissingSchema_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "", // 空的 Schema
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 1 }
                }
            };

            // Act
            var result = indexDefinition.Validate(ValidationMode.Create);

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("創建索引時必須指定 Schema")));
        }

        [Fact]
        public void Validate_CreateMode_ReservedWordName_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "INDEX", // Oracle 保留字
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 1 }
                }
            };

            // Act
            var result = indexDefinition.Validate(ValidationMode.Create);

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("索引名稱不能使用 Oracle 保留字")));
        }

        [Fact]
        public void Validate_EditMode_NoColumnsSelected_ShouldReturnError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                AvailableColumns = new System.Collections.Generic.List<string> { "COLUMN1", "COLUMN2" },
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>() // 沒有選擇任何欄位
            };

            // Act
            var result = indexDefinition.Validate(ValidationMode.Edit);

            // Assert
            Assert.False(result.IsValid);
            Assert.True(result.Errors.Any(e => e.Contains("請從可用欄位中選擇要包含在索引中的欄位")));
        }

        [Fact]
        public void Validate_ValidIndex_ShouldReturnSuccess()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "COLUMN1", Position = 1 },
                    new IndexColumnDefinition { ColumnName = "COLUMN2", Position = 2 }
                }
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }

        [Fact]
        public void Validate_EnhancedValidation_ShouldCategorizeErrors()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "", // Required error
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE"
            };

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(1, result.ErrorCount);
            Assert.Equal(ValidationErrorType.Required, result.DetailedErrors.First().ErrorType);
            Assert.Equal("Name", result.DetailedErrors.First().FieldName);
        }

        [Fact]
        public void Validate_ManyColumns_ShouldGeneratePerformanceWarning()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>()
            };

            // 新增 6 個欄位（超過建議的 5 個）
            for (int i = 1; i <= 6; i++)
            {
                indexDefinition.Columns.Add(new IndexColumnDefinition 
                { 
                    ColumnName = $"COLUMN{i}", 
                    Position = i 
                });
            }

            // Act
            var result = indexDefinition.Validate();

            // Assert
            Assert.True(result.IsValid); // 仍然有效，但有警告
            Assert.True(result.HasWarnings);
            Assert.Equal(2, result.WarningCount); // 效能警告 + 最佳實務警告
            Assert.Contains(result.Warnings, w => w.WarningType == ValidationWarningType.Performance);
        }

        [Fact]
        public void Validate_CreateMode_WithBestPractices_ShouldGenerateWarnings()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "BADNAME", // 沒有 IDX 前綴
                Owner = "TEST_SCHEMA",
                TableName = "USERS",
                IsUnique = true,
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "ID", Position = 1 }
                }
            };

            // Act
            var result = indexDefinition.Validate(ValidationMode.Create);

            // Assert
            Assert.True(result.IsValid);
            Assert.True(result.HasWarnings);
            Assert.True(result.WarningCount >= 2); // 命名建議 + 唯一索引建議
        }

        [Fact]
        public void Validate_EditMode_WithInvalidColumns_ShouldReturnDependencyError()
        {
            // Arrange
            var indexDefinition = new IndexDefinition
            {
                Name = "TEST_INDEX",
                Owner = "TEST_SCHEMA",
                TableName = "TEST_TABLE",
                AvailableColumns = new System.Collections.Generic.List<string> { "COLUMN1", "COLUMN2" },
                Columns = new System.Collections.Generic.List<IndexColumnDefinition>
                {
                    new IndexColumnDefinition { ColumnName = "INVALID_COLUMN", Position = 1 }
                }
            };

            // Act
            var result = indexDefinition.Validate(ValidationMode.Edit);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains(result.DetailedErrors, e => e.ErrorType == ValidationErrorType.Dependency);
        }

        [Fact]
        public void ValidationResult_GetErrorSummary_ShouldFormatCorrectly()
        {
            // Arrange
            var result = new ValidationResult();
            result.AddError("錯誤1", ValidationErrorType.Required);
            result.AddError("錯誤2", ValidationErrorType.Format);
            result.AddWarning("警告1", ValidationWarningType.Performance);

            // Act
            var summary = result.GetErrorSummary();

            // Assert
            Assert.Contains("發現 2 個錯誤", summary);
            Assert.Contains("發現 1 個警告", summary);
            Assert.Contains("錯誤1", summary);
            Assert.Contains("警告1", summary);
        }

        [Fact]
        public void IndexValidationMessages_GetFormattedMessage_ShouldFormatWithParameters()
        {
            // Act
            var message = IndexValidationMessages.GetFormattedMessage(
                "DuplicateColumn", 
                "欄位 '{0}' 重複", 
                "TEST_COLUMN");

            // Assert
            Assert.Equal("欄位 'TEST_COLUMN' 重複", message);
        }
    }
}